package com.epcos.common.core.enums;

/**
 * 不同客户区分
 */
public enum ClientEnum {

    DEV("dev", "本地开发", "本地开发"),
    TEST("test", "测试环境", "测试环境"),
    PROD("prod", "易建采科技（武汉）有限公司", "易建采"),
    TK("tk", "测试电脑", "测试电脑"),
    ZL("zl", "江西省肿瘤医院", "江西肿瘤"),
    TH("th", "十堰市太和医院", "十堰太和"),
    PR("pr", "湖北正泓源医疗科技有限公司电子采购平台", "普仁"),
    XK("xk", "首都医科大学附属北京胸科医院", "北京胸科"),
    XY("xy", "襄阳市中医医院电子采购平台", "襄阳中医"),
    WS("ws", "湖北省武汉市武商集团版本", "武商集团"),
    LG("lg", "龙港城发集团数字化采购管理平台", "龙港集团"),
    SMART("smart", "易建采采购管理平台", "易建采"),
    FJ("fj", "福建农信电子采购管理平台", "福建农信"),
    CQ("cq", "重庆医科大学附属第一医院采购信息化管理平台", "重庆附一"),
    ;

    String code;
    String name;
    String shortName;

    @Override
    public String toString() {
        return name + "（" + code + "）";
    }

    /**
     * 根据 code 返回 this
     */
    public static ClientEnum ofCode(String code) {
        for (ClientEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    ClientEnum(String code, String name, String shortName) {
        this.code = code;
        this.name = name;
        this.shortName = shortName;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getShortName() {
        return shortName;
    }

}
