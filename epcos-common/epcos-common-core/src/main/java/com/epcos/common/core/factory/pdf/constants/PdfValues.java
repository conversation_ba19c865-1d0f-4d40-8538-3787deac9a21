package com.epcos.common.core.factory.pdf.constants;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 11:12
 */
public class PdfValues {


    //=====================================================审批单======================================================
    public static final String AUDIT_PDF_FILE_NAME = "审批单_";
    public static final String AUDIT_PDF_HEAD_NAME = "审批单";

    /**
     * 符合性评审表头
     * reviewType === 1
     */
    public static final List<String> AUDIT_HEAD_ONE = new ArrayList<String>() {{
        add("序号");
        add("审查因素");
        add("审查因素描述");
        add("主观分/客观分");
    }};

    /**
     * 技术商务打分表头
     * reviewType == 2 || 3 || 4
     */
    public static final List<String> AUDIT_HEAD_OTHER = new ArrayList<String>() {{
        add("评分模块");
        add("序号");
        add("审查因素");
        add("审查因素描述");
        add("分值");
        add("主观分/客观分");
    }};

    public static final List<String> AUDIT_PROCESS_HEAD = new ArrayList<String>() {{
        add("审批人");
        add("审批状态");
        add("审核要求");
        add("审批意见");
        add("审批时间");
        add("附件");
    }};


    //===================================================龙港市城建======================================================

    public static final String LG_BUY_ITEM_CONTENT_FILE_NAME = "龙港市城市建设发展有限公司非工程采购需求表_";
    public static final String LG_BUY_ITEM_CONTENT = "龙港市城市建设发展有限公司非工程采购需求表";


    //==================================================响应对照表pdf=====================================================

    public static final String REVIEW_RSP_COMPARE_FILE_NAME = "评审响应对照表_";
    public static final String REVIEW_RSP_COMPARE_HEAD_NAME = "评审响应对照表";
    public static final List<String> REVIEW_RSP_COMPARE_TABLE_HEAD = new ArrayList<String>() {{
        add("序号");
        add("审查因素");
        add("审查因素描述");
        add("响应对照页码");
    }};
}
