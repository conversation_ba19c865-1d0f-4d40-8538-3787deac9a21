package com.epcos.common.core.utils;

import com.epcos.common.core.enums.ClientEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class LogPrint {

    private static final List<String> DATA_URLS =
            Arrays.asList("mysql.url", "spring.datasource.url");

    public static void log(Environment env) {
        if (Objects.isNull(env)) {
            return;
        }
        String environment = env.getProperty("environment");
        String name = Optional.ofNullable(environment)
                .map(ClientEnum::ofCode)
                .map(ClientEnum::getName)
                .orElse("未知");
        log.error("======激活环境======environment={}-{}======", environment, name);
        log.error("======激活配置======activeProfiles={}======", Arrays.toString(env.getActiveProfiles()));
        String dataUrl = DATA_URLS.stream()
                .map(env::getProperty)
                .filter(StringUtils::hasText)
                .findFirst()
                .orElse("NONE");
        for (String profile : env.getActiveProfiles()) {
            if ("dev".equals(profile) || "test".equals(profile)) {
                log.error("======数据库连接======{}======", dataUrl);
            }
        }
    }

}
