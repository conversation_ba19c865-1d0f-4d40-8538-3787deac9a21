package com.epcos.common.core.invoice;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InvoiceGoodsQueryResponse implements Serializable {

    private static final long serialVersionUID = -1446157633289274288L;
    /**
     * 响应状态	retCode	Max(6)	000000：成功	MS
     */
    private String retCode;

    /**
     * 响应消息	retMsg	Max(20)		MS
     */
    private String retMsg;

    /**
     * 随机数	randomStr	Max（20）		MS
     */
    private String randomStr;

    /**
     * 商户号	mchntCd	Max(50)	富友提供的商户号	MS
     */
    private String mchntCd;

    /**
     * 终端号	fyTermId	Max(30)	非必填（若无值参加加密时值拼接空字符串:&fyTermId=）	OS
     */
    private String fyTermId;

    /**
     * 机构编号	insCd	Max(30)	富友机构编号	M
     */
    private String insCd;

    /**
     * 渠道	channel	Max(4)	渠道： 0004：富掌柜云票；0006：诺诺	M
     */
    private String channel;

    /**
     * 商品税率列表	cargosList	List		O
     */
    private List<Cargos> cargosList;

    /**
     * 摘要	sign			M
     */
    private String sign;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Cargos {
        /**
         * 编码库版本号	bb	Max(10)		M
         */
        @ApiModelProperty("编码库版本号")
        private String bb;

        /**
         * 商品编码	spbm	Max(30)		M
         */
        @ApiModelProperty("商品编码")
        private String spbm;

        /**
         * 富友商品编码	fySpbm	Max(30)		M
         */
        @ApiModelProperty("富友商品编码")
        private String fySpbm;

        /**
         * 商品名称	spmc	Max(300)		M
         */
        @ApiModelProperty("商品名称")
        private String spmc;

        /**
         * 商品编码简称	spbmjc	Max(100)		O
         */
        @ApiModelProperty("商品编码简称")
        private String spbmjc;

        /**
         * 说明	sm	Max(1200)		O
         */
        @ApiModelProperty("说明")
        private String sm;

        /**
         * 增值税税率	zzssl	Max(10)		O
         */
        @ApiModelProperty("增值税税率")
        private String zzssl;

        /**
         * 关键字	gjz	Max(100)		O
         */
        @ApiModelProperty("关键字")
        private String gjz;

        /**
         * 是否汇总项	hzx	Max(5)	值为 Y 为汇总项商品  不能开票	O
         */
        @ApiModelProperty("是否汇总项")
        private String hzx;

        /**
         * 增值税特殊管理	zzstsgl	Max(100)		O
         */
        @ApiModelProperty("增值税特殊管理")
        private String zzstsgl;
    }

}
