package com.epcos.common.core.invoice;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票记录
 */
@Data
public class InvoiceRecordVo {

    private Long id;

    /**
     * 发票id
     */
    private Long invoiceId;

    /**
     * 请求订单号
     */
    private String orderNo;

    /**
     * 收票人邮箱
     */
    private String email;

    /**
     * 收票人手机号
     */
    private String mobilephoneNo;

    /**
     * 购买方名称
     */
    private String buyerName;

    /**
     * 购买方纳税人识别号
     */
    private String buyerTaxId;

    /**
     * 购买方开户行账号
     */
    private String buyerBankAccount;

    /**
     * 购买方开户行名称
     */
    private String buyerBankName;

    /**
     * 购买方地址
     */
    private String buyerAddress;

    /**
     * 购买方电话
     */
    private String buyerTelephoneNo;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 税率 DECIMAL(10, 2)
     */
    private BigDecimal taxRate;

    /**
     * 含税总金额 DECIMAL(10, 2)
     */
    private BigDecimal totalAmount;

    /**
     * 税额 DECIMAL(14, 6)
     */
    private BigDecimal taxAmount;

    /**
     * 未税金额 DECIMAL(14, 6)
     */
    private BigDecimal untaxedAmount;

    /**
     * 发票状态：0-初始状态,1-开票成功,2-开票失败,3-发票开具中,5-取消开票(红冲)
     */
    private Integer invoiceStatus;

    /**
     * 开票方式：0-默认表单开票，1-手机扫码开票
     */
    private Integer invoiceMethod;

    /**
     * 手机扫码开票地址
     */
    private String mobileScanUrl;

    /**
     * 富友请求数据
     */
    private String fyRequest;

    /**
     * 富友响应数据
     */
    private String fyResponse;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 更新人
     */
    private String updateBy;

}
