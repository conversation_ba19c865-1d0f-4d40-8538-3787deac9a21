package com.epcos.common.core.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 成功标记
     */
    public static final Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    public static final Integer FAIL = 500;

    /**
     * 登录成功状态
     */
    public static final String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败状态
     */
    public static final String LOGIN_FAIL_STATUS = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 当前记录起始索引
     */
    public static final String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录数
     */
    public static final String PAGE_SIZE = "pageSize";

    /**
     * 排序列
     */
    public static final String ORDER_BY_COLUMN = "orderByColumn";

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    public static final String IS_ASC = "isAsc";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 验证码有效期（分钟）
     */
    public static final long CAPTCHA_EXPIRATION = 2;


    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 客户端版本
     */
    public static final String SYS_CLIENT_VERSION = "client_version";


    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = {"com.epcos"};

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.epcos.common.core.utils.file"};

    /**
     * 采购文件
     * 合格制评审
     * purchaser_conformity_system
     */
    public static final String PURCHASER_CONFORMITY_SYSTEM = "purchaser_conformity_system";

    /**
     * 采购文件
     * 打分制评审
     * purchaser_conformity_system
     */
    public static final String PURCHASER_SCORING_SYSTEM = "purchaser_scoring_system";

    /**
     * nonce key
     */
    public static final String NONCE_KEY = "nonce:";

    /**
     * 通用报价表取值,固定4个字段
     * 0-产品名称
     * 1-数量
     * 2-单价
     * 3-总价
     */
    public static final String QUOTE_FIELD = "quote_field";

    /**
     * 是否开启密码有效时间校验（三个月更换一次密码）
     */
    public static final String PASSWORD_SWITCH_OPEN = "0";

    public static final String PASSWORD_SWITCH_SHUT = "1";

    /**
     * 供应商账号停用规则
     */
    public static final String SUPPLIER_ACCOUNT_DISABLE = "supplier_account_disable";

    /**
     * 供应商企业信息中的类型
     */
    public static final String SUPPLIER_BID_TYPE = "supplier_bid_type";

    /**
     * 后台发票税率参数配置
     */
    public static final String SYS_TAX_RATE = "sys.tax.rate";

    /**
     * 后台发票开票人参数配置
     */
    public static final String SYS_TAX_DRAWER = "sys.tax.drawer";

    /**
     * 新增后台富友商品发票编码
     */
    public static final String SYS_TAX_GOODS_CODE = "sys.tax.goods.code";

}
