package com.epcos.common.core.utils.poi;

import cn.hutool.core.io.IoUtil;
import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 9:37
 */
@Slf4j
public class ResUtils {

    public static ResponseEntity<byte[]> unify(Path generateExcel) {
        try {
            String fileName = URLEncoder.encode(generateExcel.toFile().getName(), StandardCharsets.UTF_8.name());
            return ResponseEntity.ok()
                    .headers(httpHeaders -> {
                        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                        httpHeaders.setContentDispositionFormData("attachment", fileName);
                    })
                    .body(IoUtil.readBytes(Files.newInputStream(generateExcel)));
        } catch (IOException e) {
            log.error("导出失败,e:{}", e);
            throw new ServiceException("下载失败");
        }
    }
}
