package com.epcos.common.core.invoice;

import lombok.Data;

import java.util.Date;

/**
 * 红冲记录
 */
@Data
public class InvoiceCancelRecordVo {

    private Long id;

    /**
     * 发票id
     */
    private Long invoiceId;

    /**
     * 开票记录id
     */
    private Long invoiceRecordId;

    /**
     * 原开票请求订单号
     */
    private String oldOrderNo;

    /**
     * 请求订单号
     */
    private String orderNo;

    /**
     * 发票状态：0–初始状态,1-开票成功,2-开票失败,3-发票开具中,5-取消开票
     */
    private Integer cancelStatus;

    /**
     * 冲红原因，富掌柜云票渠道，必填，开具负数普通发票必须提供。1：销货退回2：开票有误3：应税服务中止4：发生销售折让
     */
    private String negInvReason;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 富友请求数据
     */
    private String fyRequest;

    /**
     * 富友响应数据
     */
    private String fyResponse;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 更新人
     */
    private String updateBy;

}
