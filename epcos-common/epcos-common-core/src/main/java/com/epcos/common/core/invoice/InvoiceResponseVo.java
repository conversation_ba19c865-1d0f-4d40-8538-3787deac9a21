package com.epcos.common.core.invoice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("发票信息")
public class InvoiceResponseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("发票id")
    private Long id;

    @ApiModelProperty("商户名称")
    private String merchantName;

    @ApiModelProperty("渠道：0004-富掌柜云票，0006-诺诺")
    private String channel;

    @ApiModelProperty("系统内订单号")
    private String orderId;

    @ApiModelProperty("供应商ID")
    private Long supplierId;

    @ApiModelProperty("开票人")
    private String drawer;

    @ApiModelProperty("发票类型：0-平推普票;1-平推专票;4-电票普票 5-电专票 6-全电普票 7-全电专票")
    private String invoiceType;

    @ApiModelProperty("请求时间-yyyyMMddHHmmss")
    private String requestTime;

    @ApiModelProperty("终端号")
    private String fyTermId;

    @ApiModelProperty("业务类型：1-开票，2-红冲")
    private Integer bizType;

    @ApiModelProperty("发票状态：0-发票开具中，1-开票成功，2-开票失败，3-红冲中，5-红冲成功")
    private Integer status;

    @ApiModelProperty("发票PDF下载地址")
    private String invoiceUrl;

    @ApiModelProperty("红冲发票PDF下载地址")
    private String cancelUrl;

    @ApiModelProperty("发票版本号")
    private Integer invoiceVersion;

    @ApiModelProperty("0-未激活 1-已激活")
    private Integer isActive;

    @ApiModelProperty("重开票是父发票id")
    private Long parentInvoiceId;

    @ApiModelProperty("创建时间")
    private Date createAt;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新时间")
    private Date updateAt;

    @ApiModelProperty("更新人")
    private String updateBy;

    // 响应码与说明：respCode + "-" + respDesc
    private String respCodeAndRespDesc;

    @ApiModelProperty("对应的开票记录")
    private InvoiceRecordVo invoiceRecordVo;

    @ApiModelProperty("对应的红冲记录")
    private InvoiceCancelRecordVo invoiceCancelRecordVo;


}