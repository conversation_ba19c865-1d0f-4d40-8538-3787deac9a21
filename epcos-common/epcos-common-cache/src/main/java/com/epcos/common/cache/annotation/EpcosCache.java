package com.epcos.common.cache.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * 缓存注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EpcosCache {

    /**
     * 缓存条件,支持spel表达式
     */
    String condition() default "";

    /**
     * 缓存键,支持spel表达式
     */
    String key() default "";

    /**
     * 数据表名称
     */
    String table() default "";

    /**
     * 缓存过期时间
     */
    int expire() default 10;

    /**
     * 过期时间单位
     */
    TimeUnit timeUnit() default TimeUnit.MINUTES;

    /**
     * 排除条件,是否缓存,支持spel表达式
     */
    String unless() default "";

    /**
     * 是否异步设置缓存
     */
    boolean async() default false;


}
