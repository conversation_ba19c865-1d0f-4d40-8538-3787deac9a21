package com.epcos.common.cache.aspect;

import com.epcos.common.cache.annotation.EpcosCache;
import com.epcos.common.cache.annotation.EpcosCacheEvict;
import com.epcos.common.cache.constants.EpcosCacheConstants;
import com.epcos.common.cache.service.CacheService;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.redis.lock.RedisLock;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.Instant;
import java.util.*;
import java.util.function.Supplier;

@Order(1)
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class EpcosCacheAspect {

    private final CacheService cacheService;
    private final ThreadPoolTaskScheduler threadPoolTaskScheduler;
    private final Environment environment;
    private final RedisLock redisLock;

    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    private final Map<String, Expression> expressionCache = Collections.synchronizedMap(new LinkedHashMap<String, Expression>() {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, Expression> eldest) {
            return size() > 200;
        }
    });
    private final Map<Method, String[]> parameterNamesCache = Collections.synchronizedMap(new LinkedHashMap<Method, String[]>() {
        @Override
        protected boolean removeEldestEntry(Map.Entry<Method, String[]> eldest) {
            return size() > 500;
        }
    });
    private final Map<Method, String> defaultKeyTemplateCache = Collections.synchronizedMap(new LinkedHashMap<Method, String>() {
        @Override
        protected boolean removeEldestEntry(Map.Entry<Method, String> eldest) {
            return size() > 500;
        }
    });

    private String serviceName() {
        return environment.getProperty("spring.application.name", "unknow-service-name");
    }

    @Around("@annotation(epcosCache)")
    public Object handleCache(ProceedingJoinPoint joinPoint, EpcosCache epcosCache) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        if (!condition(epcosCache.condition(), method, args)) {
            log.debug("缓存条件不满足，直接执行方法, method={}, condition={}", method.getName(), epcosCache.condition());
            return joinPoint.proceed();
        }
        String cacheKey = generateCacheKey(epcosCache.key(), epcosCache.table(), method, args);
        return redisLock.executeWithLock(cacheKey, 5, () -> {
            Object obj = cacheService.get(cacheKey);
            if (Objects.nonNull(obj)) {
                log.debug("命中缓存, key={}, method={}", cacheKey, method.getName());
                return obj;
            }
            log.debug("未命中缓存，执行方法, key={}, method={}", cacheKey, method.getName());
            Object result = proceed(joinPoint);
            // 结果不为null,并且unless条件不满足时
            if (result != null && epcosCache.expire() > 0 && !unless(epcosCache.unless(), method, args, result)) {
                if (epcosCache.async()) {
                    cacheService.putAsync(cacheKey, result, epcosCache.expire(), epcosCache.timeUnit());
                } else {
                    cacheService.put(cacheKey, result, epcosCache.expire(), epcosCache.timeUnit());
                }
                log.debug("设置缓存成功, key={}, method={}, expireTime={}", cacheKey, method.getName(), epcosCache.expire() + "-" + epcosCache.timeUnit());
            } else {
                log.debug("unless条件满足，不缓存结果, key={}, result={}, method={}", cacheKey, result, method.getName());
            }
            return result;
        }, () -> proceed(joinPoint));
    }

    private Object proceed(ProceedingJoinPoint joinPoint) {
        try {
            return joinPoint.proceed();
        } catch (Throwable e) {
            log.error("切面方法失败, method={}", joinPoint.getSignature().getName(), e);
            throw new RuntimeException(e);
        }
    }

    @Around("@annotation(epcosCacheEvict)")
    public Object handleEvict(ProceedingJoinPoint joinPoint, EpcosCacheEvict epcosCacheEvict) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        if (!condition(epcosCacheEvict.condition(), method, args)) {
            log.debug("缓存清理条件不满足，直接执行方法, method={}, condition={}", method.getName(), epcosCacheEvict.condition());
            return joinPoint.proceed();
        }
        final String cacheKey = generateCacheKey(epcosCacheEvict.key(), epcosCacheEvict.table(), method, args);
        Supplier<Object> success = () -> {
            String servicePattern = delCacheBefore(epcosCacheEvict, cacheKey);
            Object result = proceed(joinPoint);
            delCacheAfter(cacheKey, servicePattern);
            return result;
        };
        return redisLock.executeWithLock(cacheKey, 10, success, success);
    }

    // 切面之后删除缓存
    private void delCacheAfter(String cacheKey, String servicePattern) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    delayDelete(cacheKey, servicePattern, 0);
                }
            });
        } else {
            delayDelete(cacheKey, servicePattern, 100);
        }
    }

    // 删除缓存
    private String delCacheBefore(EpcosCacheEvict epcosCacheEvict, String cacheKey) {
        String servicePattern;
        cacheService.evict(cacheKey);
        if (epcosCacheEvict.delService()) {
            servicePattern = EpcosCacheConstants.buildClearPattern(serviceName(), null);
            cacheService.clear(servicePattern);
        } else if (epcosCacheEvict.delTable() && StringUtils.hasText(epcosCacheEvict.table())) {
            servicePattern = EpcosCacheConstants.buildClearPattern(serviceName(), epcosCacheEvict.table());
            cacheService.clear(servicePattern);
        } else {
            servicePattern = null;
        }
        return servicePattern;
    }

    // 延时删除缓存
    private void delayDelete(String cacheKey, String servicePattern, int milliseconds) {
        threadPoolTaskScheduler.schedule(() -> {
            try {
                cacheService.evict(cacheKey);
                if (StringUtils.hasText(servicePattern)) {
                    cacheService.clear(servicePattern);
                }
            } catch (Exception e) {
                log.error("延时删除缓存失败, cacheKey={}, servicePattern={}", cacheKey, servicePattern, e);
            }
        }, Instant.now().plusMillis(milliseconds));
    }

    // 是否缓存的条件,true=不缓存
    private boolean unless(String unless, Method method, Object[] args, Object result) {
        if (!StringUtils.hasText(unless)) {
            return false;
        }
        Boolean value = evaluateExpression(unless, Boolean.class, method, args, result);
        return value != null && value;
    }

    // 获取缓存key
    private String generateCacheKey(String keyEL, String table, Method method, Object[] args) {
        if (StringUtils.hasText(keyEL)) {
            String key = evaluateExpression(keyEL, String.class, method, args, null);
            return EpcosCacheConstants.buildCacheKey(serviceName(), table, key);
        } else if (StringUtils.hasText(table)) {
            return EpcosCacheConstants.buildCacheKey(serviceName(), table, null);
        } else {
            return generateDefaultKey(method, args);
        }
    }

    // 生成默认key
    // 格式：epcos:{service}:{class}:{method}:{argsMd5}
    private String generateDefaultKey(Method method, Object[] args) {
        String template = defaultKeyTemplateCache.computeIfAbsent(
                method, m -> EpcosCacheConstants.PREFIX +
                        EpcosCacheConstants.SEPARATOR +
                        serviceName() +
                        EpcosCacheConstants.SEPARATOR +
                        m.getDeclaringClass().getSimpleName() +
                        EpcosCacheConstants.SEPARATOR +
                        m.getName() +
                        EpcosCacheConstants.SEPARATOR);
        return template + DigestUtils.md5Hex(Arrays.toString(args));
    }

    // 计算缓存条件,true=执行缓存
    private boolean condition(String condition, Method method, Object[] args) {
        if (!StringUtils.hasText(condition)) {
            return true;
        }
        Boolean value = evaluateExpression(condition, Boolean.class, method, args, null);
        return value != null && value;
    }

    // 计算spel值
    private <T> T evaluateExpression(String expression, Class<T> tClass, Method method, Object[] args, Object result) {
        try {
            Expression exp = expressionCache.computeIfAbsent(expression, parser::parseExpression);
            StandardEvaluationContext context = createEvaluationContext(method, args, result);
            return exp.getValue(context, tClass);
        } catch (Exception e) {
            log.error("计算spel值失败: expression={}, tClass={}, method={}, args={}",
                    expression, tClass, method.getName(), Arrays.toString(args), e);
            throw new ServiceException("计算spel值失败: " + e.getMessage());
        }
    }

    // 创建spel上下文
    private StandardEvaluationContext createEvaluationContext(Method method, Object[] args, Object result) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        String[] parameterNames = getParameterNames(method);
        for (int i = 0; i < parameterNames.length && i < args.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
            context.setVariable("p" + i, args[i]);
        }
        // 设置方法信息
        context.setVariable("method", method);
        context.setVariable("methodName", method.getName());
        context.setVariable("className", method.getDeclaringClass().getSimpleName());
        // 设置结果
        context.setVariable("result", result);
        return context;
    }

    // 获取方法参数名称
    private String[] getParameterNames(Method method) {
        return parameterNamesCache.computeIfAbsent(method, m -> {
            String[] parameterNames = parameterNameDiscoverer.getParameterNames(m);
            if (parameterNames == null) {
                parameterNames = new String[m.getParameterCount()];
                for (int i = 0; i < parameterNames.length; i++) {
                    parameterNames[i] = "arg" + i;
                }
            }
            return parameterNames;
        });
    }
}
