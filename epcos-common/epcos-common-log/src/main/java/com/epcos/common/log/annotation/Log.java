package com.epcos.common.log.annotation;

import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.log.enums.OperatorType;
import org.springframework.core.annotation.Order;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 * 人物 （ 操作人）、 事件（业务功能） 、 时间（发生的时间 ） 、地点（业务所在模块）、结果 （响应结果）
 * <AUTHOR>
 */
@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Order(3)
public @interface Log
{
    /**
     * 模块
     */
    public String title() default "";

    /**
     * 功能
     */
    public BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    public OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求的参数
     */
    public boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    public boolean isSaveResponseData() default true;
}
