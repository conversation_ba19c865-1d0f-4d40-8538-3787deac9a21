package com.epcos.common.log.annotation;

import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.log.enums.OperatorType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 *
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogBySupplier {
    /**
     * 模块
     */
    public String title();

    /**
     * 功能
     */
    public BusinessType businessType();

    /**
     * 操作人类别
     */
    public OperatorType operatorType() default OperatorType.SUPPLIER;

    /**
     * 是否保存请求的参数
     */
    public boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    public boolean isSaveResponseData() default true;
}
