package com.epcos.common.log.aspect;

import com.alibaba.fastjson.JSON;
import com.epcos.common.core.utils.ServletUtils;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.ip.IpUtils;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.annotation.LogBySupplier;
import com.epcos.common.log.enums.BusinessStatus;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.log.enums.OperatorType;
import com.epcos.common.log.service.AsyncLogService;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.domain.SysOperLog;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class LogAspect {
    private static final Logger log = LoggerFactory.getLogger(LogAspect.class);

    @Autowired
    private AsyncLogService asyncLogService;

    private final String pointcut = "@annotation(com.epcos.common.log.annotation.Log) || " +
            "@annotation(com.epcos.common.log.annotation.LogBySupplier)";


    @Pointcut(pointcut)
    public void pointcut() {
    }

    @AfterReturning(value = "pointcut()", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
        handleLog(joinPoint, null, jsonResult);
    }


    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
//    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
//    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult)
//    {
//        handleLog(joinPoint, controllerLog, null, jsonResult);
//    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
//    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
//    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
//        handleLog(joinPoint, controllerLog, e, null);
//    }
    @AfterThrowing(value = "pointcut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        handleLog(joinPoint, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult) {
        try {
            // *========数据库日志=========*//
            SysOperLog operLog = new SysOperLog();
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            // 请求的地址
            String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
            operLog.setOperIp(ip);
            operLog.setOperUrl(ServletUtils.getRequest().getRequestURI());
            String username = SecurityUtils.getUsername();
            if (StringUtils.isNotBlank(username)) {
                operLog.setOperName(username);
            }

            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, operLog, jsonResult);
            // 保存数据库
            asyncLogService.saveSysLog(operLog);
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("==前置通知异常==");
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, SysOperLog operLog, Object jsonResult) throws Exception {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        final Method method = signature.getMethod();
        final Log log = method.getAnnotation(Log.class);
        if (Objects.nonNull(log)) {
            setOperLogAttribute(joinPoint, jsonResult, operLog, log.businessType(), log.operatorType(), log.title(), log.isSaveRequestData(), log.isSaveResponseData());
        }
        final LogBySupplier logBySupplier = method.getAnnotation(LogBySupplier.class);
        if (Objects.nonNull(logBySupplier)) {
            setOperLogAttribute(joinPoint, jsonResult, operLog, logBySupplier.businessType(), logBySupplier.operatorType(), logBySupplier.title(), logBySupplier.isSaveRequestData(), logBySupplier.isSaveResponseData());
        }
    }

    private void setOperLogAttribute(JoinPoint joinPoint, Object jsonResult, SysOperLog operLog, BusinessType businessType, OperatorType operatorType, String title, boolean isSaveRequestData, boolean isSaveResponseData) throws Exception {
        // 设置action动作
        operLog.setBusinessType(businessType.ordinal());
        // 设置标题
        operLog.setTitle(title);
        // 设置操作人类别
        operLog.setOperatorType(operatorType.ordinal());
        // 是否需要保存request，参数和值
        if (isSaveRequestData) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog);
        }
        // 是否需要保存response，参数和值
        if (isSaveResponseData && StringUtils.isNotNull(jsonResult)) {
            operLog.setJsonResult(StringUtils.substring(JSON.toJSONString(jsonResult), 0, 2000));
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, SysOperLog operLog) throws Exception {
        String requestMethod = operLog.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            String params = argsArrayToString(joinPoint.getArgs());
            operLog.setOperParam(StringUtils.substring(params, 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        String params = "";
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (StringUtils.isNotNull(o) && !isFilterObject(o)) {
                    try {
                        Object jsonObj = JSON.toJSON(o);
                        params += jsonObj.toString() + " ";
                    } catch (Exception e) {
                    }
                }
            }
        }
        return params.trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.entrySet()) {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
}
