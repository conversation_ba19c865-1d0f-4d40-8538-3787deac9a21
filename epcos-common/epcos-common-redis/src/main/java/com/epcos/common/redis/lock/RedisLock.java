package com.epcos.common.redis.lock;

import com.epcos.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static com.epcos.common.core.constant.CacheConstants.REDIS_LOCK;

/**
 * 分布式锁
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisLock {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 加锁成功后执行，有返回
    public <T> T executeWithLock(String redisKey, int expireSecond, Supplier<T> success) {
        return executeWithLock(redisKey, expireSecond, success, null);
    }

    // 加锁成功后执行，有返回
    public void executeWithLock(String redisKey, int expireSecond, Runnable success) {
        executeWithLock(redisKey, expireSecond, () -> {
            success.run();
            return null;
        }, null);
    }

    // 加锁成功后执行 success，加锁失败后执行 failback
    public <T> T executeWithLock(String redisKey, int expireSecond,
                                 Supplier<T> success, Supplier<T> failback) {
        String redisValue = UUID.randomUUID().toString();
        if (!lock(redisKey, redisValue, expireSecond)) {
            log.error("加锁失败，执行-failback，redisKey={}， failback={}", redisKey, failback);
            if (failback != null) {
                return failback.get();
            }
            return null;
        }
        try {
            return success.get();
        } finally {
            release(redisKey, redisValue);
        }
    }

    private String getLockKey(String redisKey) {
        return REDIS_LOCK + redisKey;
    }

    private boolean tryGetLock(String lockKey, String redisValue, int expireSecond) {
        Boolean res = stringRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, redisValue, expireSecond, TimeUnit.SECONDS);
        return Boolean.TRUE.equals(res);
    }

    /**
     * 加锁
     * 返回 null 证明加锁失败
     */
    public boolean lock(String redisKey, String redisValue, int expireSecond) {
        String lockKey = getLockKey(redisKey);
        long startTime = System.currentTimeMillis();
        while (true) {
            try {
                if (tryGetLock(lockKey, redisValue, expireSecond)) {
                    return true;
                }
            } catch (Exception e) {
                log.error("获取Redis锁异常，lockKey={}, redisValue={}", lockKey, redisValue, e);
                return false;
            }
            if (System.currentTimeMillis() - startTime >= 1000) {
                log.warn("获取Redis锁超时，lockKey={}, redisValue={}", lockKey, redisValue);
                return false;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
                log.warn("加锁线程被中断，线程name={}, lockKey={}, redisValue={}",
                        Thread.currentThread().getName(), lockKey, redisValue);
                Thread.currentThread().interrupt();
                return false;
            }
        }
    }

    /**
     * 释放锁
     */
    public boolean release(String redisKey, String redisValue) {
        if (!StringUtils.hasText(redisKey) || !StringUtils.hasText(redisValue)) {
            return false;
        }
        String lockKey = getLockKey(redisKey);
        final String lua = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        final Long executeResult = stringRedisTemplate.execute(
                new DefaultRedisScript<>(lua, Long.class),
                Collections.singletonList(lockKey),
                redisValue);
        return Objects.equals(1L, executeResult);
    }


}
