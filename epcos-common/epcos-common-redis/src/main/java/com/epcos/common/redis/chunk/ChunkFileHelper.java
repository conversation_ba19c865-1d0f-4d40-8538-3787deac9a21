package com.epcos.common.redis.chunk;

import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.sign.AESUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * 通用切片文件处理工具类
 * 提供切片上传、合并、状态管理等通用功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChunkFileHelper {

    public static final String CHUNK_TEMP_DIR = System.getProperty("java.io.tmpdir") + File.separator + "epcos-chunks";
    public static final String CHUNK_PREFIX = "chunk_";
    private final String chunk_upload_status = "chunk_upload:status:";
    private final String chunk_upload_session = "chunk_upload:session:";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public static Path getChunkFilePath(String sessionId, int chunkNumber) {
        return Paths.get(CHUNK_TEMP_DIR, sessionId, CHUNK_PREFIX + chunkNumber);
    }

    /**
     * 仅处理切片上传，不自动完成处理
     *
     * @param file        切片文件
     * @param sessionId   会话ID
     * @param filename    原始文件名
     * @param chunkNumber 切片编号（从1开始）
     * @param totalChunks 总切片数
     */
    public void upload(MultipartFile file, String sessionId, String filename,
                       int chunkNumber, int totalChunks) {
        filename = filename.trim();
        if (filename.length() > 100) {
            String subFilename = filename.substring(filename.length() - 100);
            log.error("文件名长度大于100，裁减只保留后100位，filename={}, subFilename={}", filename, subFilename);
            filename = subFilename;
        }
        String chunkHash;
        try (InputStream inputStream = file.getInputStream()) {
            chunkHash = DigestUtils.md5DigestAsHex(inputStream);
        } catch (IOException e) {
            log.error("计算切片MD5失败: sessionId={}, chunkNumber={}, filename={}", sessionId, chunkNumber, filename, e);
            throw new ServiceException("计算切片MD5失败");
        }
        initOrUpSession(sessionId, filename, totalChunks);
        if (hasChunk(sessionId, chunkNumber, file.getSize(), chunkHash)) {
            return;
        }
        saveChunk(file, sessionId, chunkNumber);
        updateChunkStatus(sessionId, chunkNumber);
    }

    // 增加解密文件切片的方法
    public void upload(MultipartFile file, String sessionId, String filename,
                       int chunkNumber, int totalChunks, boolean isEncrypted, String decryptedAesKey) {
        filename = filename.trim();
        if (filename.length() > 100) {
            String subFilename = filename.substring(filename.length() - 100);
            log.error("文件名长度大于100，裁减只保留后100位，filename={}, subFilename={}", filename, subFilename);
            filename = subFilename;
        }
        byte[] chunkData;
        try {
            byte[] originalBytes = file.getBytes();
            if (isEncrypted) {
                chunkData = AESUtil.decrypt(originalBytes, decryptedAesKey.getBytes(StandardCharsets.UTF_8));
            } else {
                chunkData = originalBytes;
            }
        } catch (IOException e) {
            log.error("读取切片数据失败: sessionId={}, chunkNumber={}, filename={}", sessionId, chunkNumber, filename, e);
            throw new ServiceException("读取切片数据失败");
        }
        String chunkHash = DigestUtils.md5DigestAsHex(chunkData);
        initOrUpSession(sessionId, filename, totalChunks);
        if (hasChunk(sessionId, chunkNumber, file.getSize(), chunkHash)) {
            return;
        }
        saveChunk(chunkData, sessionId, chunkNumber);
        updateChunkStatus(sessionId, chunkNumber);
    }

    /**
     * 获取 SessionInfo
     */
    public SessionInfo getSessionInfo(String sessionId) {
        String sessionInfo = getSessionInfoStr(sessionId);
        if (StringUtils.isBlank(sessionInfo)) {
            throw new ServiceException("会话不存在或已过期，请重新上传");
        }
        SessionInfo info = parseSessionInfo(sessionId, sessionInfo);
        if (!hasAllUpload(sessionId, info.getTotalChunks())) {
            throw new ServiceException("上传未全部完成，请重新上传");
        }
        return info;
    }

    /**
     * 获取 context
     */
    public ChunkCompleteContext getContext(SessionInfo info) {
        return new ChunkCompleteContext(info.getSessionId(), info.getTotalChunks(), info.getFilename());
    }

    /**
     * 获取 context
     */
    public ChunkCompleteContext getContext(String sessionId) {
        return getContext(getSessionInfo(sessionId));
    }

    /**
     * 处理完整文件
     */
    public <R> R complete(String sessionId, Function<ChunkCompleteContext, R> onComplete) {
        ChunkCompleteContext context = null;
        try {
            context = getContext(sessionId);
            return onComplete.apply(context);
        } catch (Exception e) {
            log.error("处理完整文件失败: sessionId={}, context={}", sessionId, context, e);
            throw new ServiceException(e.getMessage());
        } finally {
            cleanupSession(sessionId);
        }
    }

    /**
     * 初始化或更新上传会话
     */
    public void initOrUpSession(String sessionId, String filename, int totalChunks) {
        String sessionKey = chunk_upload_session + sessionId;
        String chunk_upload_session_value = "filename=%s,totalChunks=%d,timestamp=%s";
        String sessionInfo = String.format(chunk_upload_session_value, filename, totalChunks,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        redisTemplate.opsForValue().set(sessionKey, sessionInfo, Duration.ofHours(2));
    }

    /**
     * 获取用户会话ID
     */
    public String getSessionId(String subpackageCode, Long userId, String resumableIdentifier) {
        String s = subpackageCode + "_" + userId;
        // 检查 resumableIdentifier 是否有关于路径地址的冲突
        if (org.springframework.util.StringUtils.hasText(resumableIdentifier)) {
            if (resumableIdentifier.contains("/") || resumableIdentifier.contains("\\")) {
                throw new ServiceException("非法的文件名：" + resumableIdentifier);
            }
            return s + "_" + resumableIdentifier;
        }
        return s;
    }

    /**
     * 更新切片完成状态
     */
    public void updateChunkStatus(String sessionId, int chunkNumber) {
        String statusKey = chunk_upload_status + sessionId;
        redisTemplate.opsForSet().add(statusKey, String.valueOf(chunkNumber));
        redisTemplate.expire(statusKey, Duration.ofHours(2));
    }

    /**
     * 检查切片是否已存在
     */
    public boolean hasChunk(String sessionId, int chunkNumber, long expectedSize, String chunkHash) {
        Path chunkFile = Paths.get(CHUNK_TEMP_DIR, sessionId, CHUNK_PREFIX + chunkNumber);
        if (Files.notExists(chunkFile)) {
            return false;
        }
        try {
            long actualSize = Files.size(chunkFile);
            if (actualSize == expectedSize &&
                    Objects.equals(chunkHash, DigestUtils.md5DigestAsHex(Files.newInputStream(chunkFile)))) {
                log.error("切片已存在且验证通过: sessionId={}, chunkNumber={}, size={}MB, chunkHash={}",
                        sessionId, chunkNumber, actualSize / 1024 / 1024, chunkHash);
                return true;
            } else {
                log.error("切片验证失败，需重新上传，删除当前同名切片文件: sessionId={}, chunkNumber={}, expected={}MB, actual={}MB, chunkFile={}",
                        sessionId, chunkNumber, expectedSize / 1024 / 1024, actualSize / 1024 / 1024, chunkFile);
                Files.deleteIfExists(chunkFile);
                return false;
            }
        } catch (IOException e) {
            log.error("检查切片文件失败: sessionId={}, chunkNumber={}, chunkFile={}", sessionId, chunkNumber, chunkFile, e);
            return false;
        }
    }

    private Path createChunkDir(String sessionId) {
        Path chunkDir = Paths.get(CHUNK_TEMP_DIR, sessionId);
        if (Files.notExists(chunkDir)) {
            try {
                Files.createDirectories(chunkDir);
            } catch (IOException e) {
                log.error("创建切片目录失败: sessionId={}, chunkDir={}", sessionId, chunkDir, e);
                throw new ServiceException("创建切片目录失败");
            }
        }
        return chunkDir;
    }

    public void saveChunk(byte[] chunkData, String sessionId, int chunkNumber) {
        Path chunkDir = createChunkDir(sessionId);
        // 保存切片文件
        Path chunkFile = chunkDir.resolve("chunk_" + chunkNumber);
        try {
            Files.write(chunkFile, chunkData);
        } catch (IOException e) {
            log.error("保存切片文件失败: sessionId={}, chunkNumber={}, chunkFile={}", sessionId, chunkNumber, chunkFile, e);
            throw new ServiceException("保存切片文件失败");
        }
    }

    /**
     * 保存切片文件
     */
    public void saveChunk(MultipartFile chunk, String sessionId, int chunkNumber) {
        Path chunkDir = createChunkDir(sessionId);
        // 保存切片文件
        Path chunkFile = chunkDir.resolve("chunk_" + chunkNumber);
        try (InputStream inputStream = chunk.getInputStream();
             OutputStream outputStream = Files.newOutputStream(chunkFile)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("保存切片文件失败: sessionId={}, chunkNumber={}, chunkFile={}", sessionId, chunkNumber, chunkFile, e);
            throw new ServiceException("保存切片文件失败");
        }
    }

    /**
     * 检查所有切片是否都已上传
     */
    public boolean hasAllUpload(String sessionId, int totalChunks) {
        Path chunkDir = Paths.get(CHUNK_TEMP_DIR, sessionId);
        if (Files.notExists(chunkDir)) {
            return false;
        }
        for (int i = 1; i <= totalChunks; i++) {
            Path chunkFile = chunkDir.resolve(CHUNK_PREFIX + i);
            if (!Files.exists(chunkFile)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 清理会话相关的所有资源
     */
    public void cleanupSession(String sessionId) {
        try {
            // 清理Redis
            String sessionKey = chunk_upload_session + sessionId;
            String statusKey = chunk_upload_status + sessionId;
            redisTemplate.delete(Arrays.asList(sessionKey, statusKey));
            // 清理临时文件
            cleanupChunks(sessionId);
        } catch (Exception e) {
            log.warn("清理会话失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 清理切片临时文件
     */
    public void cleanupChunks(String sessionId) {
        Path chunkDir = Paths.get(CHUNK_TEMP_DIR, sessionId);
        if (Files.notExists(chunkDir)) {
            return;
        }
        try (Stream<Path> walk = Files.walk(chunkDir)) {
            walk.sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(file -> {
                        boolean delete = file.delete();
                        if (!delete) {
                            log.error("清理分块文件失败：{}", file.getAbsolutePath());
                        }
                    });
            log.error("清理切片完成: sessionId={}, chunkDir={}", sessionId, chunkDir);
        } catch (IOException e) {
            log.error("清理切片失败: sessionId={}, chunkDir={}", sessionId, chunkDir, e);
        }
    }

    /**
     * 获取会话信息
     */
    public String getSessionInfoStr(String sessionId) {
        String sessionKey = chunk_upload_session + sessionId;
        return redisTemplate.opsForValue().get(sessionKey);
    }

    /**
     * 获取已完成的切片数量
     */
    public int getUploadCount(String sessionId) {
        String statusKey = chunk_upload_status + sessionId;
        Set<String> completedChunks = redisTemplate.opsForSet().members(statusKey);
        return completedChunks != null ? completedChunks.size() : 0;
    }

    /**
     * 解析会话格式: "filename=xxx,totalChunks=10,timestamp=xxx"
     */
    private SessionInfo parseSessionInfo(String sessionId, String sessionInfo) {
        try {
            String[] parts = sessionInfo.split(",");
            String filename = null;
            int totalChunks = 0;
            LocalDateTime timestamp = null;
            for (String part : parts) {
                String[] kv = part.split("=", 2);
                if (kv.length == 2) {
                    switch (kv[0]) {
                        case "filename":
                            filename = kv[1];
                            break;
                        case "totalChunks":
                            totalChunks = Integer.parseInt(kv[1]);
                            break;
                        case "timestamp":
                            // 2025-07-03 19:30:38
                            timestamp = LocalDateTime.parse(kv[1], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                            break;
                    }
                }
            }
            return new SessionInfo(sessionId, filename, totalChunks, timestamp);
        } catch (Exception e) {
            log.error("解析会话信息失败: sessionInfo={}", sessionInfo, e);
            throw new RuntimeException("解析会话信息错误");
        }
    }


}