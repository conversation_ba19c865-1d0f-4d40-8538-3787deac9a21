package com.epcos.common.redis.annotation;

import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Order(1)
public @interface RedisLock {

    /**
     * redis key
     */
    public abstract String name() default "";

    /**
     * el key
     */
    public abstract String keyEl() default "";

    /**
     * redis expire second
     */
    public abstract int second() default 8;

}
