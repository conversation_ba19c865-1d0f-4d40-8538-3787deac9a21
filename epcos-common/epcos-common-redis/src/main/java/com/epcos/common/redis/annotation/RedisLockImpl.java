package com.epcos.common.redis.annotation;

import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvalSpelUtil;
import com.epcos.common.core.web.domain.AjaxResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class RedisLockImpl {

    private final com.epcos.common.redis.lock.RedisLock lock;

    @Around("@annotation(redisLock)")
    public Object around(ProceedingJoinPoint point, RedisLock redisLock) {
        final Method method = ((MethodSignature) point.getSignature()).getMethod();
        final Class<?> returnType = method.getReturnType();
        final String redisKey = getKey(redisLock, method, point.getArgs());
        return lock.executeWithLock(redisKey, redisLock.second(), () -> {
            try {
                return point.proceed();
            } catch (ServiceException e) {
                throw e;
            } catch (Throwable e) {
                log.error("RedisLock 执行异常, method:{}, args:{}", method, point.getArgs(), e);
                throw new ServiceException(e.getMessage());
            }
        }, () -> returnErrObj(returnType));
    }

    private String getKey(RedisLock redisLock, Method method, Object[] args) {
        String keyFromEl = "";
        if (StringUtils.hasText(redisLock.keyEl())) {
            keyFromEl = EvalSpelUtil.get(method, args, redisLock.keyEl(), String.class);
        }
        return Stream.of(/*method.getDeclaringClass().getSimpleName(),
                        method.getName(),*/
                        redisLock.name(),
                        keyFromEl)
                .filter(StringUtils::hasText)
                .collect(Collectors.joining(":"));
    }

    private Object returnErrObj(Class<?> returnType) {
        final String errMsg = "操作频繁，请稍候重试！";
        if (returnType == AjaxResult.class) {
            return AjaxResult.error(errMsg);
        } else if (returnType == R.class) {
            return R.fail(errMsg);
        } else {
            return null;
        }
    }
}
