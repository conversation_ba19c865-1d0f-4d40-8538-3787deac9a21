package com.epcos.epcfile.api;

import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.ServiceNameConstants;
import com.epcos.common.core.domain.R;
import com.epcos.epcfile.api.domain.dto.FileUploadDto;
import com.epcos.epcfile.api.domain.vo.FileDataVo;
import com.epcos.epcfile.api.domain.vo.ProjectDocumentVo;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.epcfile.api.factory.RemoteProjectFileFallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 项目文件文件服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteProjectFileService", value = ServiceNameConstants.FILE_SERVICE, fallbackFactory = RemoteProjectFileFallbackFactory.class)
public interface RemoteProjectFileService {

    @PostMapping(value = "/file/file/bigFileUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("大文件上传")
    R<SysFileVo> bigFileUpload(@ModelAttribute FileUploadDto dto);

    @PostMapping(value = "/projectFile/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation(value = "项目归档文件上传", notes = "项目归档文件列表")
    R<SysFileVo> projectFiles(@RequestPart(value = "file") MultipartFile file,
                              @RequestParam("yearMonthSplit") String yearMonthSplit,
                              @RequestParam("buyItemCode") String buyItemCode,
                              @RequestParam(value = "supplierNumber", required = false) String supplierNumber,
                              @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                              @RequestParam("fileTypeName") String fileTypeName,
                              @RequestParam(value = "fileStatus", required = false) Integer fileStatus);


    @PostMapping(value = "/projectFile/uploadEnclosure", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("项目内非归档文件上传")
    R<SysFileVo> projectEnclosureFile(@RequestPart(value = "file") MultipartFile file,
                                      @RequestParam(value = "yearMonthSplit", required = false) String yearMonthSplit,
                                      @RequestParam("buyItemCode") String buyItemCode,
                                      @RequestParam(value = "supplierNumber", required = false) String supplierNumber,
                                      @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                      @RequestParam("fileTypeName") String fileTypeName);


    @GetMapping(value = "/projectFile/fileArchivingListInfo")
    @ApiOperation(value = "项目归档文件列表", notes = "项目归档文件列表")
    R<List<ProjectDocumentVo>> fileArchivingListInfo(
            @RequestParam("yearMonthSplit") String yearMonthSplit,
            @RequestParam("buyItemCode") String buyItemCode,
            @RequestParam(value = "subpackageCode", required = false) String subpackageCode);


    @GetMapping(value = "/projectFile/fileArchivingListByProject")
    @ApiOperation(value = "项目归档文件列表", notes = "项目归档文件列表")
    R<List<ProjectDocumentVo>> fileArchivingListByProject(@RequestParam("buyItemCode") String buyItemCode);


    @GetMapping(value = "/projectFile/fileArchivingListBySection")
    @ApiOperation(value = "按标段查项目归档文件列表", notes = "按标段查项目归档文件列表")
    R<List<ProjectDocumentVo>> fileArchivingListBySection(@RequestParam("yearMonthSplit") String yearMonthSplit,
                                                          @RequestParam("buyItemCode") String buyItemCode,
                                                          @RequestParam("subpackageCode") String subpackageCode);

    @ApiOperation("项目内归档文件下载 ，对应epcosfiles上传接口")
    @GetMapping("/projectFile/download")
    ResponseEntity<Resource> downloadFile(@RequestParam("fileKey") String fileKey);


    @GetMapping("/projectFile/deleteFileBySubpackageCode")
    @ApiOperation("按包删除该包下所有项目内归档文件")
    R<Boolean> deleteFileData(@RequestParam("subpackageCode") String subpackageCode);


    @ApiOperation("项目下归档文件删除 ，对应epcosfiles上传接口")
    @GetMapping("/projectFile/deleteFileKey")
    R<Boolean> deleteFileKey(@RequestParam("fileKey") String fileKey);

    @ApiOperation("文件删除,物理删除")
    @GetMapping("/projectFile/deleteFilePath")
    R<Boolean> deleteFilePath(@RequestParam("filePath") String filePath);


    @ApiOperation("归档文件批量删除")
    @GetMapping("/projectFile/deleteBiddingFileList")
    R<Boolean> deleteBiddingFileList(@RequestParam(value = "buyItemCode", required = false) String buyItemCode,
                                     @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                     @RequestParam(value = "fileTypeName", required = false) String fileTypeName,
                                     @RequestParam(value = "supplierNumber", required = false) Long supplierNumber);

    @ApiOperation("根据项目code批量删除项目所有文件")
    @GetMapping("/projectFile/deleteProjectFileAll")
    R<Boolean> deleteProjectFileAll(@RequestParam("buyItemCode") String buyItemCode);


    @ApiOperation("项目内非归档文件下载 ，对应epcosenclosure上传接口")
    @GetMapping("/projectFile/downloadEnclosure")
    ResponseEntity<Resource> downloadEnclosure(@RequestParam("fileKey") String fileKey);


    @ApiOperation("项目下非归档文件删除")
    @GetMapping("/projectFile/deleteEnclosureFileKey")
    R<Boolean> deleteEnclosureFileKey(@RequestParam("fileKey") String fileKey);

    @ApiOperation("项目内非归档文件批量删除")
    @GetMapping("/projectFile/deleteTenderDocFileList")
    R<Boolean> deleteTenderDocFileList(@RequestParam("buyItemCode") String buyItemCode,
                                       @RequestParam("supplierNumber") Long supplierNumber,
                                       @RequestParam("subpackageCode") String subpackageCode,
                                       @RequestParam("fileTypeName") String fileTypeName);


    @ApiOperation("项目内归档和不归档文件信息表")
    @GetMapping("/projectFile/fileInfo")
    R<ProjectDocumentVo> fileInfo(@RequestParam("fileKey") String fileKey);

    @ApiOperation("数据库中修改文件存储路径")
    @GetMapping("/projectFile/updateProjectFilePath")
    R<Boolean> updateProjectFilePath(@RequestParam("fileKey") String fileKey, @RequestParam("fileStoragePath") String fileStoragePath);

    @ApiOperation("项目内归档文件批量下载")
    @GetMapping("/projectFile/downloadFileList")
    ResponseEntity<Resource> downloadFileList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source, @RequestParam("fileKeyList") List<String> fileKeyList, @RequestParam("buyItemCode") String buyItemCode);


    @ApiOperation("修改文件状态")
    @GetMapping("/projectFile/updateProjectFileStatus")
    R<Boolean> updateProjectFileStatus(@RequestParam("fileKeys") List<String> fileKeys);

    @ApiOperation("删除临时文件")
    @GetMapping("/projectFile/delTemporaryFile")
    R<Boolean> delTemporaryFile(@RequestParam("buyItemCode") String buyItemCode,
                                @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                @RequestParam("supplierNumber") Long supplierNumber);

    @ApiOperation("非项目文件转移到项目中")
    @GetMapping("/projectFile/noFileToProjectFile")
    R<Boolean> noFileToProjectFile(@RequestParam("fileKeyList") @NotNull List<String> fileKeyList,
                                   @RequestParam("buyItemCode") @NotBlank String buyItemCode);

}
