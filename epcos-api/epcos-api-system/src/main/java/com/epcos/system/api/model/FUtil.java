package com.epcos.system.api.model;

import com.alibaba.fastjson.JSONArray;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.AttachmentDto;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.file.FileUtils;
import com.epcos.common.core.web.domain.AjaxResult;
import com.epcos.epcfile.api.RemoteDingTalkFileService;
import com.epcos.epcfile.api.RemoteNonProjectFileService;
import com.epcos.epcfile.api.RemoteProjectFileService;
import com.epcos.epcfile.api.domain.dto.FileUploadDto;
import com.epcos.epcfile.api.domain.vo.FileDataVo;
import com.epcos.epcfile.api.domain.vo.ProjectDocumentVo;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.seal.api.RemoteNewSealService;
import com.epcos.seal.api.RemoteSealService;
import com.epcos.seal.api.domain.dto.SealConditionDto;
import com.epcos.seal.api.domain.dto.SealContentDto;
import com.epcos.seal.api.domain.vo.SealContentVo;
import com.epcos.seal.api.domain.vo.SealDataVo;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.RemoteUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component(value = "fUtil")
public class FUtil {

    private static RemoteNonProjectFileService nonProjectFile;
    private static RemoteProjectFileService projectFile;
    private static RemoteDingTalkFileService dingTalkFile;
    private static RemoteSealService seal;
    private static RemoteNewSealService newSeal;
    private static RemoteSystemService RS;
    private static RemoteUserService userService;

    @Autowired
    private RemoteSystemService remoteSystemService;
    @Autowired
    private RemoteNonProjectFileService nonProjectFileService;
    @Autowired
    private RemoteProjectFileService projectFileService;
    @Autowired
    private RemoteDingTalkFileService dingTalkFileService;
    @Autowired
    private RemoteSealService sealService;
    @Autowired
    private RemoteNewSealService newSealService;
    @Autowired
    private RemoteUserService remoteUserService;

    @PostConstruct
    public void setRemoteSystemService() {
        RS = remoteSystemService;
    }

    @PostConstruct
    public void setSealService() {
        seal = sealService;
    }

    @PostConstruct
    public void setNewSealService() {
        newSeal = newSealService;
    }

    @PostConstruct
    public void setNonProjectFileService() {
        nonProjectFile = nonProjectFileService;
    }

    @PostConstruct
    public void setProjectFileService() {
        projectFile = projectFileService;
    }

    @PostConstruct
    public void setDingTalkFileService() {
        dingTalkFile = dingTalkFileService;
    }

    @PostConstruct
    public void setUserService() {
        userService = remoteUserService;
    }

    /**
     * true: 签章开启 false: 签章关闭
     *
     * @return
     */
    public static boolean signatureIsOff() {
        R<Long> r = RS.remoteGetIdByConfigKey(UserConstants.signature);
        if (r.hasFail()) {
            log.error("查询config中,签章开关配置失败,r={}", r);
            throw new ServiceException("查询签章开关配置失败");
        }
        return r.getData() != 1;
    }

    /**
     * 上传钉钉推相关文件
     *
     * @param f
     * @param fileType
     * @param instanceId
     * @return
     */
    public static String upDingFile(MultipartFile f, String fileType, String instanceId) {
        FileUploadDto fileUploadDto = new FileUploadDto();
        fileUploadDto.setFile(f);
        fileUploadDto.setFileTypeName(fileType);
        fileUploadDto.setInstanceId(instanceId);
        final R<SysFileVo> upload = dingTalkFile.upload(fileUploadDto);
        if (upload == null || upload.hasFail() || upload.getData() == null) {
            throw new ServiceException(String.valueOf(upload));
        }
        return upload.getData().getUrl();
    }


    /**
     * 项目归档文件
     *
     * @return
     * @tempFileStatus 1-临时文件
     */
    public static String upFile(String tenderProjectCode, String yearMonthSplit, File file,
                                String fileType, String sectionBidCode, Long userId, Integer tempFileStatus) {
        MultipartFile multipartFile = FileUtils.fileToMultipartFile(file);
        final R<SysFileVo> upload = projectFile.projectFiles(multipartFile, yearMonthSplit, tenderProjectCode,
                userId == null ? null : userId.toString(),
                sectionBidCode, fileType, tempFileStatus);
        if (upload == null || upload.hasFail() || upload.getData() == null) {
            throw new ServiceException(String.valueOf(upload));
        }
        return upload.getData().getUrl();
    }

    /**
     * 项目归档文件
     */
    public static String upFile(String tenderProjectCode, String yearMonthSplit, File f,
                                String fileType, String sectionBidCode, Long userId) {
        return upFile(tenderProjectCode, yearMonthSplit, f, fileType, sectionBidCode, userId, null);
    }

    public static String upFile(String tenderProjectCode, String yearMonthSplit, MultipartFile file,
                                String fileType, String sectionBidCode, Long userId) {
        final R<SysFileVo> upload = projectFile.projectFiles(file, yearMonthSplit, tenderProjectCode,
                userId == null ? null : userId.toString(),
                sectionBidCode, fileType, null);
        if (upload == null || upload.hasFail() || upload.getData() == null) {
            throw new ServiceException(String.valueOf(upload));
        }
        return upload.getData().getUrl();
    }

    /**
     * 非项目文件转移到项目中
     *
     * @param fileKeyList
     * @return
     */
    public static R<Boolean> noFileToProjectFile(List<String> fileKeyList, String buyItemCode) {
        return projectFile.noFileToProjectFile(fileKeyList, buyItemCode);
    }

    /**
     * 非项目文件-根据文件key获取文件信息
     *
     * @param fileKeyList
     * @return
     */
    public static List<FileDataVo> getNoFileInfo(List<String> fileKeyList) {
        R<List<FileDataVo>> r = nonProjectFile.getFileInfo(fileKeyList);
        if (r.hasFail() || r.getData() == null) {
            log.error("根据文件key获取非项目文件信息失败,fileKeyList={},r={}", fileKeyList, r);
            return null;
        }
        return r.getData();
    }

    /**
     * 项目(非)归档文件
     *
     * @return
     */
    public static String upFileWithAttachment(String tenderProjectCode, String yearMonthSplit,
                                              File file, String fileType,
                                              String sectionBidCode, Long userId) {
        MultipartFile multipartFile = FileUtils.fileToMultipartFile(file);
        R<SysFileVo> epcosenclosure = projectFile.projectEnclosureFile(multipartFile, yearMonthSplit, tenderProjectCode, userId == null ? null : userId.toString(), sectionBidCode, fileType);
        if (epcosenclosure == null || epcosenclosure.hasFail() || epcosenclosure.getData() == null) {
            throw new ServiceException(String.valueOf(epcosenclosure));
        }
        return epcosenclosure.getData().getUrl();
    }

    /**
     * 删除归档文件
     *
     * @param fileKey
     * @return
     */
    public static R delFile(String fileKey) {
        if (StringUtils.hasText(fileKey)) {
            final R<Boolean> booleanR = projectFile.deleteFileKey(fileKey);
            if (booleanR == null || booleanR.hasFail() || Objects.equals(java.lang.Boolean.FALSE, booleanR.getData())) {
                log.error("删除归档文件 失败,booleanR={}", booleanR);
                throw new ServiceException(String.valueOf(booleanR));
            }
            return booleanR;
        }
        return null;
    }

    /**
     * 删除(非)归档文件
     *
     * @param fileKey
     * @return
     */
    public static R delNonArchivedFileByFileKey(String fileKey) {
        if (StringUtils.hasText(fileKey)) {
            R<Boolean> booleanR = projectFile.deleteEnclosureFileKey(fileKey);
            if (booleanR == null || booleanR.hasFail() || Objects.equals(java.lang.Boolean.FALSE, booleanR.getData())) {
                log.error("项目内非归档文件删除 失败,delBidFileR={}", booleanR);
                throw new ServiceException(String.valueOf(booleanR));
            }
            return booleanR;
        }
        return null;
    }

    /**
     * 归档文件批量删除
     *
     * @param subpackageCode
     * @param fileTypeName
     * @return
     */
    public static void delProjectFileList(String subpackageCode, String fileTypeName) {
        if (StringUtils.hasText(subpackageCode) || StringUtils.hasText(fileTypeName)) {
            R<Boolean> r = projectFile.deleteBiddingFileList(null, subpackageCode, fileTypeName, null);
            if (r == null || r.hasFail()) {
                log.error("归档文件批量删除失败,r={}", r);
                throw new ServiceException(String.valueOf(r));
            }
        }
    }


    /**
     * 删除项目非归档文件
     *
     * @param tenderProjectCode
     * @param userId
     * @param bidCode
     * @param fileTypeName
     * @return
     */
    public static R delNonArchivedFileByFileType(String tenderProjectCode, Long userId, String bidCode, String fileTypeName) {
        R<Boolean> delBidFileR = projectFile.deleteTenderDocFileList(tenderProjectCode, userId, bidCode, fileTypeName);
        if (delBidFileR == null || delBidFileR.hasFail() || Objects.equals(java.lang.Boolean.FALSE, delBidFileR.getData())) {
            log.error("根据文件类型删除非归档文件 失败,delBidFileR={}", delBidFileR);
            throw new ServiceException(String.valueOf(delBidFileR));
        }
        return delBidFileR;
    }

    public static String toJsonAtt(List<AttachmentDto> supplierAttachmentList) {
        String jsonStr = null;
        if (!CollectionUtils.isEmpty(supplierAttachmentList)) {
            jsonStr = JSONArray.toJSONString(supplierAttachmentList);
        }
        return jsonStr;
    }

    public static List<AttachmentDto> parseJsonAtt(String jsonStr) {
        List<AttachmentDto> attachmentDtoList = null;
        if (StringUtils.hasText(jsonStr)) {
            attachmentDtoList = JSONArray.parseArray(jsonStr, AttachmentDto.class);
        }
        return attachmentDtoList;
    }


    // ===========================================  新版签章方法  ===========================================

    /**
     * 查询e签宝版本
     *
     * @return true: 版本 3 false: 版本 2
     */
    public static boolean getSealVersion() {
        AjaxResult r = RS.getConfigKey(UserConstants.SEAL_VERSION);
        if ((Integer) r.get("code") != 200) {
            log.error("调用接口/config/configKey失败,configKey={},r={}", UserConstants.SEAL_VERSION, r);
            return false;
        }
        String value = String.valueOf(r.get("data"));

        return "3".equals(value);
    }


    /**
     * 调用签章服务
     *
     * @param sealCondition
     */
    private static void new_seal(SealConditionDto sealCondition) {
        R<Boolean> booleanR = newSeal.seal(SecurityConstants.INNER, sealCondition);
        if (booleanR == null || booleanR.hasFail() || Objects.equals(java.lang.Boolean.FALSE, booleanR.getData())) {
            log.error("新签章失败, sealCondition={}, booleanR={}", sealCondition.toString(), booleanR);
            if (booleanR != null) {
                throw new ServiceException(booleanR.getMsg());
            } else {
                throw new ServiceException("签章失败");
            }
        }
    }

    /**
     * 查询单个用户e签宝账户信息
     *
     * @param onlySign 唯一标识
     * @return
     */
    public static SealContentVo getOneSealContent(String onlySign) {
        R<SealContentVo> r = newSeal.getOneSealContent(SecurityConstants.INNER, onlySign);
        if (r.hasFail() || r.getData() == null) {
            log.error("调用/signSealBack/getSealContent接口失败,r={}", r);
            return null;
        }
        return r.getData();
    }

    /**
     * 查询用户e签宝账户信息列表
     *
     * @param ids
     * @return
     */
    public static Map<String, SealContentVo> getSealList(List<String> ids) {
        R<Map<String, SealContentVo>> r = newSeal.getSealList(SecurityConstants.INNER, ids);
        if (r.hasFail() || r.getData() == null) {
            log.error("调用/signSealBack/getSealList接口失败,r={}", r);
            return null;
        }
        return r.getData();
    }

    /**
     * 修改e签宝账户信息 (只能修改名称)
     *
     * @return
     */
    public static boolean updateAccount(Integer sealType, String onlySign, String accountUserName) {
        if (!signatureIsOff()) {
            return true;
        }
        SealContentDto dto = new SealContentDto();
        dto.setSealType(sealType);
        dto.setOnlySign(onlySign);
        dto.setAccountUserName(accountUserName);
        R<Boolean> r = newSeal.updateAccount(SecurityConstants.INNER, dto);
        if (r.hasFail() || r.getData() == null) {
            log.error("调用/signSealBack/updateAccount接口失败,r={}", r);
            return false;
        }
        return r.getData();
    }

    /**
     * 注销用户(删除整条数据库记录)
     *
     * @param onlySign
     * @return
     */
    public static boolean deleteUser(String onlySign) {
        if (!signatureIsOff()) {
            return true;
        }
        R<Boolean> r = newSeal.deleteUser(SecurityConstants.INNER, onlySign);
        if (r.hasFail() || r.getData() == null) {
            log.error("调用/signSealBack/deleteUser接口失败,r={}", r);
            return false;
        }
        return r.getData();
    }

//    /**
//     * 注销e签宝账户(只删除e签宝账户和签章)
//     *
//     * @param sealType 1-个人 2 组织
//     * @param onlySign
//     * @return
//     */
//    public static boolean deleteAccount(Integer sealType, String onlySign) {
//        if (!signatureIsOff()) {
//            return true;
//        }
//        SealContentDto dto = new SealContentDto();
//        dto.setSealType(sealType);
//        dto.setOnlySign(onlySign);
//
//        R<Boolean> r = newSeal.deleteAccount(SecurityConstants.INNER, dto);
//        if (r.hasFail() || r.getData() == null) {
//            log.error("调用/signSealBack/deleteAccount接口失败,sealType={},onlySign={},r={}", sealType, onlySign, r);
//            return false;
//        }
//        return r.getData();
//    }


    /**
     * 过渡使用的删除签章方法
     *
     * @param accountId
     * @param sealType
     * @param onlySign
     * @return
     */
    public static R transitionUse(String accountId, Integer sealType, String onlySign) {
        // v3 版本
        if (getSealVersion()) {
            deleteUser(onlySign);
            return null;
            // v2 版本
        } else {
            R r = delEsignAccount(accountId, sealType, onlySign);
            return r;
        }
    }

//    /**
//     * 个人用户认证, 发送验证码
//     *
//     * @return
//     */
//    public static String getMessageCode(String name, String idNo, String phone) {
//        R<String> r = newSeal.getMessageCode(SecurityConstants.INNER, name, idNo, phone);
//        if (r.hasFail() || r.getData() == null) {
//            log.error("调用/signSealBack/getMessageCode接口失败,r={}", r);
//            return null;
//        }
//        return r.getData();
//    }


    // ===========================================  新版签章方法  ===========================================


    /**
     * 创建签章
     *
     * @param psn             个人 true 企业 false
     * @param name            印章内容
     * @param idNo            个人--身份证   企业--社会信用代码
     * @param onlySign        唯一标识(用户id,组织code)
     * @param accountUserName e签宝账户所属用户的姓名
     * @return
     */
    public static SealDataVo createEsignAccount(boolean psn, String name, String idNo, String onlySign, String accountUserName) {
        // V3版本
        if (!signatureIsOff() && getSealVersion()) {
            return null;
        }
        // V2版本
        if (!signatureIsOff() && !getSealVersion()) {
            throw new ServiceException("系统不支持电子签章");
        }
        R<SealDataVo> r;
        if (getSealVersion()) {
            if (onlySign == null || onlySign.isEmpty()) {
                log.error("参数不能为空,onlySign={}", onlySign);
                throw new ServiceException("唯一标识为空");
            }
            SealContentDto dto = new SealContentDto();
            dto.setSealType(psn ? 1 : 2);
            dto.setOnlySign(onlySign);
            dto.setSealName(name);
            dto.setAccountUserName(accountUserName);
            dto.setIdNo(idNo);
            r = newSeal.createSeal(SecurityConstants.INNER, dto);
        } else {
            r = seal.createSeal(SecurityConstants.INNER, (psn ? 1 : 2), name, idNo);
        }
        if (r.hasFail() || r.getData() == null) {
            log.error("创建印章，异常，psn: {}, name: {}, idNo: {}，sealR.code: {}，sealR.msg：{} \n", psn, name, idNo, r.getCode(), r.getMsg());
            throw new ServiceException(String.valueOf(r.getMsg()));
        }
        return r.getData();
    }


    /**
     * 删除签章
     *
     * @param accountId e签宝账户
     * @param sealType  签章类型 1:个人,2:企业
     * @param onlySign  唯一标识
     * @return
     */
    public static R delEsignAccount(String accountId, Integer sealType, String onlySign) {
        if (!signatureIsOff()) {
            return null;
        }
        R<Boolean> r;
        if (getSealVersion()) {
            if (sealType == null || onlySign == null || onlySign.isEmpty()) {
                log.error("参数不能为空,sealType={},onlySign={}", sealType, onlySign);
                throw new ServiceException("签章类型或唯一标识为空");
            }
            SealContentDto dto = new SealContentDto();
            dto.setSealType(sealType);
            dto.setOnlySign(onlySign);
            r = newSeal.deleteAccount(SecurityConstants.INNER, dto);
        } else {
            r = seal.deleteSeal(SecurityConstants.INNER, accountId);
        }
        if (r.hasFail() || Objects.equals(Boolean.FALSE, r.getData())) {
            log.error("删除签章失败： accountId：{}， r：{} ", accountId, r);
            throw new ServiceException(r.getMsg());
        }
        return r;
    }

    /**
     * 根据id删除签章
     *
     * @return
     */
    public static void deleteSealById(Integer sealType, Long id) {
        if (!signatureIsOff()) {
            throw new ServiceException("系统不支持电子签章");
        }
        R<Boolean> r = newSeal.deleteSealById(SecurityConstants.INNER, sealType, id);
        if (r.hasFail() || r.getData() == null) {
            log.error("根据id删除签章失败： id：{}， r：{} ", id, r);
            throw new ServiceException(r.getMsg());
        }
    }


    /**
     * 调用签章服务
     *
     * @param sealCondition
     */
    private static void seal(SealConditionDto sealCondition) {
        R<Boolean> booleanR = seal.seal(SecurityConstants.INNER, sealCondition);
        if (booleanR != null && booleanR.getCode() == 10086) {
            return;
        }
        if (booleanR == null || booleanR.hasFail() || Objects.equals(java.lang.Boolean.FALSE, booleanR.getData())) {
            log.error("签章失败, sealCondition={}, booleanR={}", sealCondition.toString(), booleanR);
            if (booleanR != null) {
                throw new ServiceException(booleanR.getMsg());
            } else {
                throw new ServiceException("签章失败");
            }
        }
    }


    /**
     * 获取印章的组织编码
     *
     * @param userId
     * @param orgCode
     * @return
     */
    public static String getSealOrgCode(Long userId, String orgCode) {
        String type;
        AjaxResult r = RS.getConfigKey(UserConstants.USE_DEPT_SEAL);
        if ((Integer) r.get("code") != 200) {
            log.error("1调用接口/config/configKey失败,configKey={},r={}", UserConstants.USE_DEPT_SEAL, r);
            type = "0";
        } else {
            type = String.valueOf(r.get("data"));
        }
        if (type == null || type.isEmpty() || "0".equals(type)) {
            log.error("1获取印章的组织编码userId={},orgCode={}", userId, orgCode);
            return orgCode;
        } else {
            R<String> email = userService.getEmail(SecurityConstants.INNER, userId);
            if (email.hasFail() || email.getData() == null || email.getData().isEmpty()) {
                log.error("查询部门关联组织编码失败,userId={}", userId);
                log.error("2获取印章的组织编码userId={},orgCode={}", userId, orgCode);
                return orgCode;
            } else {
                log.error("3获取印章的组织编码userId={},orgCode={}", userId, email.getData());
                return email.getData();
            }
        }
    }


    /**
     * 企业盖章
     */
    public static void startSeal(String fileKey, String keyword, String psnOrgId, String extrasContents, String sealParameterKey) {
        startSeal(fileKey, keyword, psnOrgId, extrasContents, sealParameterKey, null, null);
    }

    /**
     * 个人盖章
     *
     * @param fileKey          文件key
     * @param keyword          关键字
     * @param psnOrgId         个人id/企业code
     * @param extrasContents   印章额外需要添加的内容
     * @param sealParameterKey 盖章参数key不能为空
     * @param flowId           流程id
     * @param authCode         验证码
     */
    public static void startSeal(String fileKey, String keyword, String psnOrgId, String extrasContents, String sealParameterKey, String flowId, String authCode) {
        if (!signatureIsOff()) {
            return;
        }
        if ((UserConstants.PERSON_KEY_SEAL_PARAMETER.equals(sealParameterKey) || UserConstants.FIRM_KEY_SEAL_PARAMETER.equals(sealParameterKey) || UserConstants.LEGAL_PERSON_KEY_SEAL_PARAMETER.equals(sealParameterKey)) && (keyword == null || keyword.isEmpty())) {
            log.error("个人章关键字为空：keyword=null");
            throw new ServiceException("关键字不能为空");
        }
        SealConditionDto sealCondition = new SealConditionDto();
        sealCondition.setFileKey(fileKey);
        sealCondition.setExtrasContents(extrasContents);
        sealCondition.setPsnOrgId(psnOrgId);
        sealCondition.setKey(keyword);
        sealCondition.setSealParameterKey(sealParameterKey);
        if (getSealVersion()) {
            if ((UserConstants.PERSON_KEY_SEAL_PARAMETER.equals(sealParameterKey) || UserConstants.PERSON_COORDINATE_SEAL_PARAMETER.equals(sealParameterKey)) && (flowId == null || flowId.isEmpty() || authCode == null || authCode.isEmpty())) {
                log.error("流程id或验证码为空：flowId={},authCode={}", flowId, authCode);
                throw new ServiceException("流程id或验证码不能为空");
            }
            sealCondition.setFlowId(flowId);
            sealCondition.setAuthCode(authCode);
            new_seal(sealCondition);
        } else {
            seal(sealCondition);
        }
    }


    /**
     * 上传系统文件
     * 上传项目报表excel
     *
     * @param file
     * @param fileTypeName
     * @return
     */
    public static String rotationChart(MultipartFile file, String fileTypeName) {
        R<SysFileVo> sysFileR = nonProjectFile.uploadNonProjectFile(file, fileTypeName);
        if (Objects.isNull(sysFileR) || sysFileR.hasFail()) {
            throw new ServiceException(Objects.isNull(sysFileR) ? "上传系统文件失败" : sysFileR.getMsg());
        }
        return sysFileR.getData().getUrl();
    }

    /**
     * 非项目文件删除
     *
     * @param fileKey
     */
    public static void deleteNonProjectFile(String fileKey) {
        R<Boolean> deleted = nonProjectFile.deleteNonProjectFile(fileKey);
        if (Objects.isNull(deleted) || deleted.hasFail()) {
            throw new ServiceException(Objects.isNull(deleted) ? "删除文件失败" : deleted.getMsg());
        }
    }

    /**
     * 归档文件下载
     *
     * @param fileKey
     * @return
     */
    public static ResponseEntity<Resource> downloadProjectFile(String fileKey) {
        return projectFile.downloadFile(fileKey);
    }

    /**
     * 钉钉文件下载
     *
     * @param fileKey
     * @return
     */
    public static ResponseEntity<byte[]> dingTalkFileDownload(String fileKey) {
        return dingTalkFile.download(fileKey);
    }


    /**
     * 根据标段code获取项目归档文件
     *
     * @param subpackageCode
     * @return
     */
    public static List<ProjectDocumentVo> getProjectFileBySubCode(String buyItemCode, String subpackageCode) {
        R<List<ProjectDocumentVo>> r = projectFile.fileArchivingListBySection("", buyItemCode, subpackageCode);
        if (r.hasFail() || r.getData() == null || r.getData().isEmpty()) {
            log.error("调用接口查询项目归档文件列表失败,buyItemCode={},subpackageCode={},r={}", buyItemCode, subpackageCode, r);
            throw new ServiceException("调用接口查询项目归档文件列表失败");
        }
        return r.getData();
    }

    /**
     * 提交文件并删除临时中间文件
     *
     * @param commitFileUrlList 提交文件urls
     * @param buyItemCode       项目code
     * @param subpackageCode    标段code
     * @param userId            用户id
     */
    public static void commitFileAndDelTmpFiles(List<String> commitFileUrlList, String buyItemCode, String subpackageCode, Long userId) {
        if (!CollectionUtils.isEmpty(commitFileUrlList)) {
            R<Boolean> commitFileR = projectFile.updateProjectFileStatus(commitFileUrlList);
            if (commitFileR.hasFail() || !Boolean.TRUE.equals(commitFileR.getData())) {
                throw new ServiceException(commitFileR.getMsg());
            }
        }
        R<Boolean> delTmpR = projectFile.delTemporaryFile(buyItemCode, subpackageCode, userId);
        if (delTmpR.hasFail() || !Boolean.TRUE.equals(delTmpR.getData())) {
            throw new ServiceException(delTmpR.getMsg());
        }
    }

    /**
     * 提交文件
     *
     * @param commitFileUrl 文件url
     */
    public static void commitFile(String... commitFileUrl) {
        if (Objects.isNull(commitFileUrl) || commitFileUrl.length == 0) {
            return;
        }
        List<String> commitFileUrlList = Arrays.asList(commitFileUrl);
        R<Boolean> commitFileR = projectFile.updateProjectFileStatus(commitFileUrlList);
        if (commitFileR.hasFail() || !Boolean.TRUE.equals(commitFileR.getData())) {
            throw new ServiceException(commitFileR.getMsg());
        }
    }


}
