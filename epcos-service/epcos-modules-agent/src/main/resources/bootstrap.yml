server:
  port: 9211
  compression:
    enabled: false
  servlet:
    encoding:
      enabled: true

feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
  compression:
    request:
      enabled: false
    response:
      enabled: true

spring:
  cloud:
    nacos:
      config:
        enabled: false
    sentinel:
      filter:
        # sentinel 在 springboot 2.6.x 不兼容问题的处理
        enabled: false
  application:
    name: epcos-agent
  profiles:
    active: '@epc@'
  #  autoconfigure:
  #    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

