<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.agent.mapper.BidSectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.epcos.agent.api.domain.AgentBidSection">
        <id column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="bid_section_name" property="bidSectionName"/>
        <result column="bid_section_code" property="bidSectionCode"/>
        <result column="announcement_time" property="announcementTime"/>
        <result column="registration_deadline" property="registrationDeadline"/>
        <result column="bid_opening_time" property="bidOpeningTime"/>
        <result column="deadline_bid_evaluation" property="deadlineBidEvaluation"/>
        <result column="result_publicity_time" property="resultPublicityTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="sectionCount" resultType="Long">
        select id
        from agent_bid_section
        order by id desc limit 1
    </select>

</mapper>
