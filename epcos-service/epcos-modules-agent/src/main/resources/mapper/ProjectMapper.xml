<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.agent.mapper.ProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.epcos.agent.api.domain.AgentProject">
        <id column="id" property="id"/>
        <result column="year_month_split" property="yearMonthSplit"/>
        <result column="project_code" property="projectCode"/>
        <result column="project_name" property="projectName"/>
        <result column="industries_type" property="industriesType"/>
        <result column="fund_source" property="fundSource"/>
        <result column="project_amount" property="projectAmount"/>
        <result column="use_department" property="useDepartment"/>
        <result column="representative" property="representative"/>
        <result column="evaluation_method" property="evaluationMethod"/>
        <result column="im_port" property="imPort"/>
        <result column="procurement_method" property="procurementMethod"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="project_status" property="projectStatus"/>
        <result column="complete_time" property="completeTime"/>
        <result column="reason" property="reason"/>
        <result column="agency_id" property="agencyId"/>
        <result column="approval_documents_file_key" property="approvalDocumentsFileKey"/>
        <result column="requirements_file_key" property="requirementsFileKey"/>
        <result column="other_file_key" property="otherFileKey"/>
        <result column="enter_prepare" property="enterPrepare"/>
        <result column="save_result" property="saveResult"/>
        <result column="del_flag" property="delFlag"/>
        <result column="intention_time" property="intentionTime"/>
        <result column="intention_url" property="intentionUrl"/>
        <result column="reviewed_by_name" property="reviewedByName"/>
        <result column="reviewed_user_id" property="reviewedUserId"/>
        <result column="reviewed_time" property="reviewedTime"/>
        <result column="create_by_name" property="createByName"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="projectCount" resultType="java.lang.Long">
        select id
        from agent_project
        order by id desc limit 1
    </select>

    <select id="list" resultType="com.epcos.agent.api.params.dto.QueryProjectDto" resultMap="BaseResultMap">
        select ap.*
        from agent_project ap
        left join agent_project_operator apo
        on ap.project_code = apo.project_code and ap.agency_id = apo.operator_id
        <where>
            <if test="dto.projectName != null and dto.projectName != ''">and ap.project_name like concat('%',
                #{dto.projectName},'%')
            </if>
            <if test="dto.useDepartment != null and dto.useDepartment != '' ">and ap.use_department like concat('%',
                #{dto.useDepartment},'%')
            </if>
            <if test="dto.agencyName != null and dto.agencyName != '' ">and apo.operator_name like concat('%',
                #{dto.agencyName},'%')
            </if>
            <if test="dto.auditStatus != null ">and ap.audit_status = #{dto.auditStatus}</if>
            <if test="dto.projectStatus != null">and ap.project_status = #{dto.projectStatus}</if>
            <if test="dto.agencyId != null">and ap.agency_id = #{dto.agencyId}</if>
            <if test="dto.createUserId != null">and ap.create_user_id = #{dto.createUserId}</if>
        </where>
        order by create_time desc
    </select>

</mapper>
