<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.agent.mapper.AgentProjectOperatorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.epcos.agent.api.domain.AgentProjectOperator">
        <id column="id" property="id"/>
        <result column="project_code" property="projectCode"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_category" property="operatorCategory"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper>
