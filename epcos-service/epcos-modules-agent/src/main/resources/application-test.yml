nacos:
  server-addr: ************:8848
  namespace: 516b92c8-098c-4ddd-b61e-909cccad3b5a
  username: test
  password: test
redis:
  host: ************
  password:
  port: 6379
  database: 2
  timeout: 5000

mysql:
  url: jdbc:mysql://************:3307/smart-bidding?connectTimeout=60000&socketTimeout=300000&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&autoReconnect=true&failOverReadOnly=false
  username: root
  password: root1233

spring:
  datasource:
    druid:
      initial-size: 2
      min-idle: 1
      max-active: 2
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true

aliyun:
  sms:
    enable: false
domainNameUrl: http://xhszag.epc1688.com/ag/
downFileApiUri: confidentialFile/downloadFile?encryptedString=
winningBidPushUrl: http://**************:8888/tjgys/OutSysQueryService/query

swagger:
  enabled: true

---
spring:
  config:
    activate:
      on-profile: polarx
mysql:
  url: jdbc:mysql://***********:4886/smart_bidding?connectTimeout=60000&socketTimeout=300000&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&autoReconnect=true&failOverReadOnly=false
  username: root
  password: root123456
