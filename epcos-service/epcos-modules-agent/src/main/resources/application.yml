spring:
  cloud:
    nacos:
      discovery:
        register-enabled: true
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        username: ${nacos.username}
        password: ${nacos.password}
        server-addr: ${nacos.server-addr}
        namespace: ${nacos.namespace}
  servlet:
    multipart:
      enabled: true
      max-file-size: 1600MB
      max-request-size: 1600MB
      resolve-lazily: false
      location: /tmp/
      file-size-threshold: 52428800
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    database: ${redis.database}
    lettuce:
      pool:
        enabled: true
        max-active: 50
        max-idle: 50
        min-idle: 2
        max-wait: 1000ms
        time-between-eviction-runs: 60000ms
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    url: ${mysql.url}
    username: ${mysql.username}
    password: ${mysql.password}
    druid:
      async-init: true
      initial-size: 10
      min-idle: 5
      max-active: 20
      max-wait: 60000
      pool-prepared-statements: true
      max-open-prepared-statements: 20
      validation-query: SELECT 1
      validation-query-timeout: 1000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 1800000
      max-evictable-idle-time-millis: 2592000
      keep-alive: true
      phy-timeout-millis: 3600000
      phy-max-use-count: 2000
      use-global-data-source-stat: true
      remove-abandoned: true
      remove-abandoned-timeout: 60
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=3000
      filter:
        wall:
          enabled: true
          db-type: mysql
          config:
            multi-statement-allow: true
            delete-allow: true
            drop-table-allow: false
            alter-table-allow: false
            truncate-allow: false
            create-table-allow: false
      web-stat-filter:
        enabled: false
        url-pattern: /*
        exclusions: "*.js,*.mjs,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        session-stat-enable: true
        principal-session-name: user
        profile-enable: true
      stat-view-servlet:
        enabled: false
        url-pattern: /druid/*
        login-username: druid
        login-password: druid
        allow: ***********/24
        reset-enable: false
  freemarker:
    cache: false  #关闭模板缓存，方便测试
    settings:
      template_update_delay: 0  #检查模板更新延迟时间，设置为0表示立即检查，如果时间大于0会有缓存不方便进行模板测试
    template-loader-path: classpath:/templates
    charset: UTF-8
    check-template-location: true
    suffix: .ftl
    content-type: text/html
    expose-request-attributes: true
    expose-session-attributes: true
    request-context-attribute: request

mybatis-plus:
  type-aliases-package: com.epcos.agent
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto

swagger:
  enabled: false
  title: 委托采购