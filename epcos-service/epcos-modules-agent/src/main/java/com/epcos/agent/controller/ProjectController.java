package com.epcos.agent.controller;

import com.epcos.agent.api.params.dto.*;
import com.epcos.agent.api.params.vo.AgentFileVo;
import com.epcos.agent.api.params.vo.AgentProjectInfoVo;
import com.epcos.agent.api.params.vo.StatusVo;
import com.epcos.agent.api.params.vo.WinBidProVo;
import com.epcos.agent.service.ProjectService;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.utils.PageResult;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.jxls.common.Context;
import org.jxls.transform.Transformer;
import org.jxls.util.JxlsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

/**
 * <p>
 * 代理项目 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Api(tags = "委托代理项目")
@Slf4j
//@ApiIgnore
@RestController
@RequestMapping("/agent.project")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    /**
     * 创建委托代理项目
     * 项目编号以及标段编号都是以AG开头
     * 编号增长为项目表的主键ID作为增长值
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "创建委托代理项目")
    @Log(title = "创建代理项目", businessType = BusinessType.INSERT)
    @RedisLock(name = "createAgentCreate")
    @PostMapping(value = "/createAgentCreate")
    public R<Long> createAgentCreate(@RequestBody AgentProjectDto dto) {
        return R.ok(projectService.createAgentCreate(dto));
    }

    /**
     * 修改委托代理项目
     * 项目编号以及标段编号都是以AG开头
     * 编号增长为项目表的主键ID作为增长值
     *
     * @param dto 需要的修改信息
     * @return
     */
    @ApiOperation(value = "修改委托代理项目")
    @Log(title = "修改代理项目信息", businessType = BusinessType.UPDATE)
    @RedisLock(name = "modifyAgentInfo")
    @PostMapping(value = "/modifyAgentInfo")
    public R<Boolean> modifyAgentInfo(@RequestBody AgentProjectDto dto) {
        return R.ok(projectService.modifyAgentInfo(dto));
    }


    /**
     * 查看委托代理项目详情
     *
     * @param id 委托代理项目id
     * @return
     */
    @ApiOperation(value = "查询委托代理项目")
    @ApiImplicitParam(name = "id", value = "委托代理项目id", paramType = "path", dataTypeClass = Long.class, required = true)
    @GetMapping(value = "/queryAgentProject")
    public R<AgentProjectInfoVo> queryAgentProject(@NotNull(message = "id必填") Long id) {
        return R.ok(projectService.queryAgentProject(id));
    }


    /**
     * 查看委托代理项目列表
     * <p>
     * 查询条件：项目名称、使用科室、代理机构名称、项目审核状态、项目完成状态
     *
     * @param dto 查询参数
     * @return
     */
    @ApiOperation(value = "查看委托代理项目列表")
    @PostMapping(value = "/projectList")
    public R<PageResult<AgentProjectInfoVo>> projectList(@RequestBody QueryProjectDto dto) {
        return R.ok(projectService.projectList(dto));
    }

    /**
     * 审核委托代理项目
     * 审核通过后，项目状态变为进行中
     *
     * @param dto 审核
     * @return
     */
    @ApiOperation(value = "审核委托代理项目")
    @PostMapping(value = "/audit")
    public R<Boolean> audit(@RequestBody AuditDto dto) {
        return R.ok(projectService.audit(dto));
    }


    /**
     * 保存招标准备信息
     *
     * @param dto 需要的保存信息
     * @return
     */
    @ApiOperation(value = "保存招标准备信息")
    @Log(title = "保存招标准备信息", businessType = BusinessType.UPDATE)
    @RedisLock(name = "savePrepareInfo")
    @PostMapping(value = "/savePrepareInfo")
    public R<Boolean> savePrepareInfo(@RequestBody AgentProjectDto dto) {
        return R.ok(projectService.savePrepareInfo(dto));
    }

    /**
     * 选择中标人
     *
     * @param dto 中标人信息
     * @return
     */
    @ApiOperation(value = "选择中标人")
    @Log(title = "选择中标人", businessType = BusinessType.INSERT)
    @RedisLock(name = "chooseWinBid")
    @PostMapping(value = "/chooseWinBid")
    public R<Boolean> chooseWinBid(@RequestBody WinBidProVo dto) {
        return R.ok(projectService.chooseWinBid(dto));
    }

    /**
     * 查询中标人页面
     *
     * @param projectCode 委托项目code
     * @return
     */
    @ApiOperation(value = "查询中标人页面")
    @ApiImplicitParam(name = "projectCode", value = "委托代理项目code", paramType = "path", dataTypeClass = String.class,
            required = true)
    @GetMapping(value = "/queryWinPage")
    public R<WinBidProVo> queryWinPage(@NotBlank(message = "projectCode必填") String projectCode) {
        return R.ok(projectService.queryWinPage(projectCode));
    }

    /**
     * 导出已完成的委托招标项目的excel
     *
     * @return
     */
    @ApiOperation("导出已完成的委托招标项目的excel")
    @PostMapping(value = "/exportExcelOfAgent")
    public ResponseEntity<byte[]> exportExcelOfAgent(@RequestBody ExportConditions conditions) {

        List<AgentProjectExcel> voList = projectService.excelInfoList(conditions);

        JxlsHelper jxlsHelper = JxlsHelper.getInstance();
        Path tmpExcelFilePath = HtmlUtil.getTmpPdfFilePath(HtmlUtil.getBusinessFileName("委托项目报表",
                "委托项目报表", ".xlsx"));
        try (OutputStream out = Files.newOutputStream(tmpExcelFilePath)) {
            Transformer transformer = jxlsHelper.createTransformer(getClass().getClassLoader()
                    .getResourceAsStream("templates/jxls/委托项目报表.xlsx"), out);
            Context context = new Context();
            context.putVar("tableHead", "委托项目报表");
            context.putVar("voList", voList);
            jxlsHelper.processTemplate(context, transformer);
            out.flush();
            out.close();
            byte[] bytes = IOUtils.toByteArray(Files.newInputStream(tmpExcelFilePath.toFile().toPath()));
            return new ResponseEntity<>(bytes, setExcelResponseHeaders(tmpExcelFilePath), HttpStatus.OK);
        } catch (IOException e) {
            log.error("导出已完成的委托招标项目的excel，tmpExcelFilePath:{},voList:{} e:{}", tmpExcelFilePath, voList, e);
            return new ResponseEntity<>("导出已完成的委托项目报表失败".getBytes(StandardCharsets.UTF_8), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    public HttpHeaders setExcelResponseHeaders(Path tmpExcelFilePath) throws UnsupportedEncodingException {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/vnd.ms-excel;charset=UTF-8");
        headers.setContentDispositionFormData("attachment", URLEncoder.encode(tmpExcelFilePath.toFile().getName(), StandardCharsets.UTF_8.name()));
        return headers;
    }

    /**
     * 委托文件展示
     *
     * @param projectCode 委托项目code
     * @return
     */
    @ApiOperation(value = "委托文件展示")
    @ApiImplicitParam(name = "projectCode", value = "委托代理项目code", paramType = "path", dataTypeClass = String.class,
            required = true)
    @GetMapping(value = "/showFile")
    public R<AgentFileVo> showFile(@NotBlank(message = "projectCode必填") String projectCode) {
        return R.ok(projectService.showFile(projectCode));
    }

    /**
     * 点击项目完成按钮
     *
     * @param projectCode 委托项目code
     */
    @ApiOperation(value = "完成项目")
    @Log(title = "完成项目", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/complete")
    public R<Boolean> complete(@NotBlank(message = "projectCode必填") String projectCode) {
        projectService.complete(projectCode);
        return R.ok();
    }

    /**
     * 查询委托项目是否已经完成
     *
     * @param projectCode 委托招标项目code
     * @return
     */
    @ApiOperation(value = "查询是否完成项目[true-完成，false-未完成]")
    @GetMapping(value = "/isComplete")
    public R<StatusVo> isComplete(@NotBlank(message = "projectCode必填") String projectCode) {
        return R.ok(projectService.isComplete(projectCode));
    }

    /**
     * 删除委托项目
     * 删除委托项目信息
     * 包含文件信息以及供应商信息
     *
     * @param projectCode 委托招标项目code
     * @return
     */
    @ApiOperation(value = "删除委托项目")
    @GetMapping(value = "/del")
    R<Boolean> del(@NotBlank(message = "projectCode必填") String projectCode) {
        return R.ok(projectService.del(projectCode));
    }
}
