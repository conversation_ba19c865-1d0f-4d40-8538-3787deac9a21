package com.epcos.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.agent.api.domain.AgentBidSection;
import com.epcos.agent.mapper.BidSectionMapper;
import com.epcos.agent.service.BidSectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 代理项目标段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Service
public class BidSectionServiceImpl extends ServiceImpl<BidSectionMapper, AgentBidSection> implements BidSectionService {

    @Autowired
    private BidSectionMapper bidSectionMapper;
    /**
     * 查询当前最新的标段的id
     *
     * @return 标段主键id
     */
    @Override
    public Long sectionCount() {
        return bidSectionMapper.sectionCount();
    }
}
