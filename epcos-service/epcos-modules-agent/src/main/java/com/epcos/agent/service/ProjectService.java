package com.epcos.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.agent.api.domain.AgentProject;
import com.epcos.agent.api.params.dto.*;
import com.epcos.agent.api.params.vo.AgentFileVo;
import com.epcos.agent.api.params.vo.AgentProjectInfoVo;
import com.epcos.agent.api.params.vo.StatusVo;
import com.epcos.agent.api.params.vo.WinBidProVo;
import com.epcos.common.core.utils.PageResult;

import java.util.List;

/**
 * <p>
 * 代理项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface ProjectService extends IService<AgentProject> {

    /**
     * 创建委托代理项目
     *
     * @param dto 创建信息
     * @return
     */
    Long createAgentCreate(AgentProjectDto dto);

    /**
     * 修改委托代理项目
     *
     * @param dto 需要的修改信息
     * @return
     */
    Boolean modifyAgentInfo(AgentProjectDto dto);


    /**
     * 保存招标准备信息
     *
     * @param dto 需要的保存信息
     * @return
     */
    Boolean savePrepareInfo(AgentProjectDto dto);

    /**
     * 查询委托代理项目详情
     *
     * @param id 委托代理项目id
     * @return
     */
    AgentProjectInfoVo queryAgentProject(Long id);

    /**
     * 查询委托项目列表
     *
     * @param dto 查询条件
     * @return
     */
    PageResult<AgentProjectInfoVo> projectList(QueryProjectDto dto);

    /**
     * 审核委托代理项目
     *
     * @param dto 审核
     * @return
     */
    Boolean audit(AuditDto dto);

    /**
     * 保存中标人信息
     *
     * @param dto 项目及中标人信息
     * @return
     */
    Boolean chooseWinBid(WinBidProVo dto);

    /**
     * 查询中标人页面
     *
     * @param projectCode 委托项目code
     * @return
     */
    WinBidProVo queryWinPage(String projectCode);

    /**
     * 导出已完成的委托代理项目信息的list
     *
     * @return
     */
    List<AgentProjectExcel> excelInfoList(ExportConditions conditions);

    /**
     * 委托系统文件
     *
     * @param projectCode 委托项目code
     * @return
     */
    AgentFileVo showFile(String projectCode);

    /**
     * 点击项目完成按钮
     *
     * @param projectCode 委托项目code
     */
    void complete(String projectCode);

    /**
     * 查询项目是否完成
     *
     * @param projectCode 委托项目code
     * @return
     */
    StatusVo isComplete(String projectCode);

    /**
     * 删除委托项目
     * 删除委托项目信息
     * 包含文件信息以及供应商信息
     *
     * @param projectCode 委托招标项目code
     * @return
     */
    Boolean del(String projectCode);
}
