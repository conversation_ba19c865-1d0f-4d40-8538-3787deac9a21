package com.epcos.agent;

import com.epcos.common.core.utils.LogPrint;
import com.epcos.common.security.annotation.EnableCustomConfig;
import com.epcos.common.security.annotation.EnableRyFeignClients;
import com.epcos.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
public class AgentApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(AgentApplication.class, args);
        LogPrint.log(context.getEnvironment());
    }
}
