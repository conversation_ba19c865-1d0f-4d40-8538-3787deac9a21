package com.epcos.agent.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2023-02-16 14:03
 */
public class AgentConst {

    public static Map<String, String> PURCHASE_CLASS_MAP = new HashMap() {{
        put("1", "公开招标");
        put("2", "资格预审招标");
        put("3", "邀请招标");
        put("4", "竞争性磋商");
        put("5", "竞争性谈判");
        put("6", "医院自采");
    }};

    public static Map<Integer, String> RANK_MAP = new HashMap() {{
        put(1, "第一候选人");
        put(2, "第二候选人");
        put(3, "第三候选人");
        put(4, "第四候选人");
    }};

    public static Map<String, String> FOUND_MAP = new HashMap() {{
        put("1", "科研资金");
        put("2", "自筹资金");
        put("3", "财政资金");
        put("4", "工会资金");
        put("5", "其他资金");
    }};

    public static Map<String, String> PROJECT = new HashMap() {{
        put("A000000", "工程类");
        put("B000000", "货物类");
        put("C000000", "服务类");
        put("E000000", "医疗设备");
    }};
}
