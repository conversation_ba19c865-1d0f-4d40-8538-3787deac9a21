package com.epcos.agent.aspect;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.agent.api.domain.AgentProject;
import com.epcos.agent.api.params.dto.AgentProjectDto;
import com.epcos.agent.api.params.dto.AuditDto;
import com.epcos.agent.api.params.vo.WinBidProVo;
import com.epcos.agent.service.ProjectService;
import com.epcos.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * @Title:
 * @Description:TODO
 * @author:moyu
 * @version:1.0
 * @since 2023-02-02 14:46
 */
@Slf4j
@Aspect
@Component
@Order(6)
public class BeforeAspect {
    @Autowired
    private ProjectService projectService;

    @Before(value = "execution(* com.epcos.agent.controller.ProjectController.modifyAgentInfo(..))")
    public void auditNoticeAop(JoinPoint point) {
        AgentProjectDto dto = (AgentProjectDto) point.getArgs()[0];
        AgentProject project = projectService.getById(dto.getId());
        if (Objects.nonNull(project.getAuditStatus()) && project.getAuditStatus() == 2) {
            throw new ServiceException("审核已通过，不可修改");
        }
    }

    @Before(value = "execution(* com.epcos.agent.controller.ProjectController.chooseWinBid(..))")
    public void chooseAop(JoinPoint point) {
        WinBidProVo dto = (WinBidProVo) point.getArgs()[0];
        if (dto.getWinBidSectionVoList().stream().allMatch(b -> CollectionUtils.isEmpty(b.getSupplierList()))) {
            throw new ServiceException("最少选择一个供应商");
        }
    }

    @Before(value = "execution(* com.epcos.agent.controller.ProjectController.audit(..))")
    public void auditAop(JoinPoint point) {
        AuditDto dto = (AuditDto) point.getArgs()[0];
        AgentProject project = projectService.getById(dto.getId());
        if (Objects.nonNull(project.getAuditStatus()) && project.getAuditStatus() != 1) {
            throw new ServiceException("项目已被审核");
        }
    }

    @Before(value = "execution(* com.epcos.agent.controller.ProjectController.complete(..))")
    public void completeAop(JoinPoint point) {
        String projectCode = (String) point.getArgs()[0];
        //查询是否完成招标页面以及中标结果的信息
        AgentProject project = projectService.getOne(Wrappers.lambdaUpdate(AgentProject.class)
                .eq(AgentProject::getProjectCode, projectCode));
        if (project.getEnterPrepare() == 0 || project.getSaveResult() == 0) {
            throw new ServiceException("请检查红色必填项是否完整");
        }
    }
}
