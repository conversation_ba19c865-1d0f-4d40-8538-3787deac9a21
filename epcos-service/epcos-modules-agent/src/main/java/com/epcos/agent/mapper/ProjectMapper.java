package com.epcos.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.agent.api.domain.AgentProject;
import com.epcos.agent.api.params.dto.QueryProjectDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 代理项目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface ProjectMapper extends BaseMapper<AgentProject> {

    Long projectCount();

    List<AgentProject> list(@Param(value = "dto") QueryProjectDto dto);
}
