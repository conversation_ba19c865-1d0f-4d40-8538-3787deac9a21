package com.epcos.agent.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.agent.api.domain.AgentBidSection;
import com.epcos.agent.api.domain.AgentProject;
import com.epcos.agent.api.domain.AgentProjectOperator;
import com.epcos.agent.api.domain.AgentSupplier;
import com.epcos.agent.api.params.dto.*;
import com.epcos.agent.api.params.vo.*;
import com.epcos.agent.mapper.ProjectMapper;
import com.epcos.agent.service.AgentProjectOperatorService;
import com.epcos.agent.service.BidSectionService;
import com.epcos.agent.service.ProjectService;
import com.epcos.agent.service.SupplierService;
import com.epcos.agent.utils.AgentConst;
import com.epcos.common.core.constant.AgentConstants;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.PageResult;
import com.epcos.common.core.utils.purchase.GeneratorCodeUtil;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.epcfile.api.RemoteAgencyService;
import com.epcos.epcfile.api.domain.dto.AgencyFileVerificationDto;
import com.epcos.epcfile.api.domain.vo.AgencyProjectFileVo;
import com.epcos.system.api.RemoteUserService;
import com.epcos.common.core.web.domain.user.SysUser;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.core.constant.AgentConstants.Audit.WAIT;


/**
 * <p>
 * 代理项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Slf4j
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, AgentProject> implements ProjectService {


    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private BidSectionService bidSectionService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private AgentProjectOperatorService projectOperatorService;
    @Autowired
    private RemoteUserService remoteUserService;
    @Autowired
    private RemoteAgencyService remoteFileService;

    /**
     * 创建委托代理项目
     * <p>
     * 查询委托项目以及委托项目下的标段
     * 返回这两张表的主键id作为委托项目和委托项目下标段的边编号
     *
     * @param dto 创建信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAgentCreate(AgentProjectDto dto) {

        //查询当前项目个数
        Long count = projectMapper.projectCount();
        long number = number(count);
        //当前标段个数
        Long sectionCunt = bidSectionService.sectionCount();
        long num = number(sectionCunt);
        AgentProject agentProject = new AgentProject();
        BeanUtils.copyProperties(dto, agentProject);
        agentProject.setProjectName(dto.getProjectName());
        agentProject.setYearMonthSplit(DateUtils.getDateToM());
        agentProject.setRepresentative(dto.getRepresentative());
        agentProject.setCreateUserId(SecurityUtils.getUserId());
        agentProject.setCreateByName(SecurityUtils.getNickName());
        agentProject.setCreateTime(DateUtils.getNowDate());
        //生成委托项目code
        agentProject.setProjectCode(GeneratorCodeUtil.generatorAgentProjectCode(++number));
        boolean save = save(agentProject);
        //保存科室代表
        saveAgency(agentProject.getProjectCode(), dto.getAgencyId());
        if (!save) {
            log.error("保存委托项目失败。dto:{}", dto);
            throw new ServiceException("保存委托项目失败，请重新保存");
        }
        List<AgentBidSection> sectionList = new ArrayList<>();
        for (AgentSectionDto b : dto.getAgentSectionList()) {
            AgentBidSection section = new AgentBidSection();
            section.setBidSectionName(b.getBidSectionName());
            section.setProjectCode(agentProject.getProjectCode());
            section.setCreateBy(SecurityUtils.getNickName());
            section.setBidSectionCode(GeneratorCodeUtil.generatorAgentSectionCode(++num));
            sectionList.add(section);
        }
        bidSectionService.saveBatch(sectionList);
        return agentProject.getId();
    }

    /**
     * 返回 long类型的 一个数字
     * 如果传入的参数为bull，则返回 0
     * 否则返回该值
     *
     * @param count
     * @return
     */
    private long number(Long count) {
        if (Objects.isNull(count)) {
            return 0;
        }
        return count;
    }

    /**
     * 创建委托项目时，要保存与委托项目相关的科室代表
     * 每个委托项目科室代表可能有多个
     *
     * @param projectCode 委托项目code
     * @param agencyId    指定代理机构id
     */
    private void saveAgency(String projectCode, Long agencyId) {
        //删除
        projectOperatorService.remove(Wrappers.lambdaUpdate(AgentProjectOperator.class)
                .eq(AgentProjectOperator::getProjectCode, projectCode));
        AgentProjectOperator operator = new AgentProjectOperator();
        R<SysUser> r = remoteUserService.getInfoById(agencyId, SecurityConstants.INNER);
        if (r.hasFail()) {
            throw new ServiceException(r.getMsg());
        }
        operator.setProjectCode(projectCode);
        operator.setOperatorName(r.getData().getNickName());
        operator.setOperatorId(agencyId);
        operator.setOperatorCategory("0");
        projectOperatorService.save(operator);
    }

    /**
     * 修改信息
     *
     * @param dto 需要的修改信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyAgentInfo(AgentProjectDto dto) {

        AgentProject project = new AgentProject();
        BeanUtils.copyProperties(dto, project);
        project.setRepresentative(dto.getRepresentative());
        project.setAuditStatus(WAIT);
        project.setUpdateTime(DateUtils.getNowDate());
        boolean b = updateById(project);
        if (!b) {
            log.error("修改项目信息失败. dto:{}", dto);
            throw new ServiceException("修改项目信息失败，请稍后重试");
        }
        //修改关联表
        saveAgency(dto.getProjectCode(), dto.getAgencyId());

        List<AgentBidSection> sectionList = dto.getAgentSectionList().stream().map(s -> {
            AgentBidSection bidSection = new AgentBidSection();
            BeanUtils.copyProperties(s, bidSection);
            return bidSection;
        }).collect(Collectors.toList());
        //修改标段
        updateInfo(dto, sectionList);
        return Boolean.TRUE;
    }


    /**
     * 委托项目详情页面的标段修改
     *
     * @param dto         项目信息及标段信息
     * @param sectionList 传入的标段信息
     */
    private void updateInfo(AgentProjectDto dto, List<AgentBidSection> sectionList) {

        long count = number(bidSectionService.sectionCount());
        for (AgentBidSection b : sectionList) {
            b.setBidSectionCode(GeneratorCodeUtil.generatorAgentSectionCode(++count));
            b.setProjectCode(dto.getProjectCode());
        }
        bidSectionService.remove(Wrappers.lambdaUpdate(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, dto.getProjectCode()));
        bidSectionService.saveBatch(sectionList);

    }

    /**
     * 保存招标准备信息
     *
     * @param dto 需要的保存信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePrepareInfo(AgentProjectDto dto) {

        AgentProject project = new AgentProject();
        BeanUtils.copyProperties(dto, project);
        boolean b = update(Wrappers.lambdaUpdate(AgentProject.class)
                .set(AgentProject::getIntentionTime, dto.getIntentionTime())
                .set(AgentProject::getIntentionUrl, dto.getIntentionUrl())
                .set(AgentProject::getEnterPrepare, 1)
                .eq(AgentProject::getId, dto.getId()));
        if (!b) {
            log.error("修改项目信息失败. dto:{}", dto);
            throw new ServiceException("修改项目信息失败，请稍后重试");
        }
        dto.getAgentSectionList().forEach(s -> {
            AgentBidSection bidSection = new AgentBidSection();
            BeanUtils.copyProperties(s, bidSection);
            bidSectionService.update(Wrappers.lambdaUpdate(AgentBidSection.class)
                    .set(AgentBidSection::getBidOpeningTime, s.getBidOpeningTime())
                    .set(AgentBidSection::getDeadlineBidEvaluation, s.getDeadlineBidEvaluation())
                    .set(AgentBidSection::getAnnouncementTime, s.getAnnouncementTime())
                    .set(AgentBidSection::getResultPublicityTime, s.getResultPublicityTime())
                    .set(AgentBidSection::getRegistrationDeadline, s.getRegistrationDeadline())
                    .eq(AgentBidSection::getBidSectionCode, s.getBidSectionCode()));
        });
        return Boolean.TRUE;
    }


    /**
     * 根据 委托代理项目id 查询委托招标项目信息
     *
     * @param id 委托代理项目id
     * @return
     */
    @Override
    public AgentProjectInfoVo queryAgentProject(Long id) {

        AgentProject project = getById(id);
        List<AgentBidSection> sectionList = bidSectionService.list(Wrappers.lambdaQuery(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, project.getProjectCode()));
        AgentProjectOperator operator = projectOperatorService.getOne(Wrappers.lambdaUpdate(AgentProjectOperator.class)
                .eq(AgentProjectOperator::getProjectCode, project.getProjectCode())
                .eq(AgentProjectOperator::getOperatorId, project.getAgencyId())
        );

        AgentProjectInfoVo vo = new AgentProjectInfoVo();
        BeanUtils.copyProperties(project, vo);
        vo.setProjectName(project.getProjectName());
        vo.setRepresentative(project.getRepresentative());
        vo.setAgencyName(operator.getOperatorName());
        List<AgentSectionDto> sectionDtoList = new ArrayList<>();
        sectionList.forEach(b -> {
            AgentSectionDto section = new AgentSectionDto();
            BeanUtils.copyProperties(b, section);
            sectionDtoList.add(section);
        });
        vo.setAgentSectionList(sectionDtoList);
        return vo;
    }

    /**
     * 查询委托代理招标目列表
     *
     * @param dto 查询条件
     * @return 分页数据
     */
    @Override
    public PageResult<AgentProjectInfoVo> projectList(QueryProjectDto dto) {
        Set<String> permissions = SecurityUtils.getLoginUser().getPermissions();
        Page<AgentProjectInfoVo> page = PageMethod.startPage(dto.getPageNum(), dto.getPageSize());
        //如果包含entrust:project:queryAll权限字符，则查询所有
        //如果包不含entrust:project:queryAll权限字符，则只能查询自己创建或含有项目的科室代表中含有自己的项目
        if (!permissions.contains("entrust:project:queryAll")) {
            dto.setCreateUserId(SecurityUtils.getUserId());
            dto.setAgencyId(SecurityUtils.getUserId());
        }
        List<AgentProject> list = projectMapper.list(dto);
        List<AgentProjectInfoVo> voList = list.stream().map(l -> {
            AgentProjectInfoVo vo = new AgentProjectInfoVo();
            BeanUtils.copyProperties(l, vo);
            AgentProjectOperator operator = projectOperatorService.getOne(Wrappers.lambdaQuery(AgentProjectOperator.class)
                    .eq(AgentProjectOperator::getProjectCode, l.getProjectCode()));
            vo.setAgencyName(operator.getOperatorName());
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<>(page, voList);
    }

    /**
     * 审核委托代理项目
     *
     * @param dto 审核
     * @return
     */
    @Override
    public Boolean audit(AuditDto dto) {
        return updateById(new AgentProject() {{
            setId(dto.getId());
            setAuditStatus(dto.getStatus());
            setReason(dto.getReason());
            if (dto.getStatus() == AgentConstants.Audit.PASS) {
                setProjectStatus(AgentConstants.Status.ING);
            }
        }});
    }

    /**
     * 保存中标人信息
     *
     * @param dto 项目及中标人信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean chooseWinBid(WinBidProVo dto) {
        //先删除
        dto.getWinBidSectionVoList().forEach(b -> supplierService.remove(Wrappers.lambdaUpdate(AgentSupplier.class)
                .eq(AgentSupplier::getBidSectionCode, b.getBidSectionCode())));
        //后增加
        for (WinBidSectionVo b : dto.getWinBidSectionVoList()) {
            List<AgentSupplier> supplierList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(b.getSupplierList())) {
                for (AgentSupplier s : b.getSupplierList()) {
                    AgentSupplier supplier = new AgentSupplier();
                    BeanUtils.copyProperties(s, supplier);
                    supplier.setBidderId(null);
                    supplier.setBidSectionCode(b.getBidSectionCode());
                    supplierList.add(supplier);
                }
                supplierService.saveBatch(supplierList);
            }
        }
        //修改项目状态
        update(Wrappers.lambdaUpdate(AgentProject.class)
                .set(AgentProject::getSaveResult, 1)
                .eq(AgentProject::getProjectCode, dto.getProjectCode()));
        return Boolean.TRUE;
    }

    /**
     * 查询中标人页面详细信息
     *
     * @param projectCode 委托项目code
     * @return
     */
    @Override
    public WinBidProVo queryWinPage(String projectCode) {
        AgentProject project = getOne(Wrappers.lambdaUpdate(AgentProject.class).eq(AgentProject::getProjectCode, projectCode));
        if (Objects.isNull(project)) {
            throw new ServiceException("无法查询该项目的中标人");
        }
        List<AgentBidSection> sectionList = bidSectionService.list(Wrappers.lambdaUpdate(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, projectCode));
        List<String> codeList = sectionList.stream().map(AgentBidSection::getBidSectionCode).collect(Collectors.toList());
        List<AgentSupplier> supplierList = supplierService.list(Wrappers.lambdaUpdate(AgentSupplier.class)
                .in(CollectionUtils.isNotEmpty(codeList), AgentSupplier::getBidSectionCode, codeList));
        //组装信息
        return assemblyInfo(project, sectionList, supplierList);
    }

    /**
     * 组装中标人信息
     *
     * @param project      项目信息
     * @param sectionList  标段信息
     * @param supplierList 中标人信息
     * @return
     */
    private WinBidProVo assemblyInfo(AgentProject project, List<AgentBidSection> sectionList, List<AgentSupplier> supplierList) {
        WinBidProVo vo = new WinBidProVo();
        BeanUtils.copyProperties(project, vo);
//        vo.setProjectName(project.getProjectName());
//        vo.setProjectCode(project.getProjectCode());
//        vo.setSaveResult(project.getSaveResult());
        List<WinBidSectionVo> sectionVoList = new ArrayList<>();
        sectionList.forEach(b -> {
            WinBidSectionVo sectionVo = new WinBidSectionVo();
            sectionVo.setBidSectionName(b.getBidSectionName());
            sectionVo.setBidSectionCode(b.getBidSectionCode());
            if (CollectionUtils.isNotEmpty(supplierList)) {
                List<AgentSupplier> supplierVoList = new ArrayList<>();
                supplierList.forEach(s -> {
                    if (b.getBidSectionCode().equals(s.getBidSectionCode())) {
                        supplierVoList.add(s);
                    }
                });
                sectionVo.setSupplierList(supplierVoList);
            }
            sectionVoList.add(sectionVo);
        });
        vo.setWinBidSectionVoList(sectionVoList);
        return vo;
    }

    /**
     * 查询所有已完成项目下的标段信息以及标段下的中标人信息
     *
     * @return
     */
    @Override
    public List<AgentProjectExcel> excelInfoList(ExportConditions conditions) {

        conditions.setWhetherPurchaser(remoteUserService.isPurchaser(SecurityUtils.getUserId()).getData());
        conditions.setUserId(SecurityUtils.getUserId());

        List<AgentProject> projectList = list(Wrappers.lambdaUpdate(AgentProject.class)
                .eq(AgentProject::getProjectStatus, AgentConstants.Status.COMPLETE)
                .eq(!conditions.getWhetherPurchaser(), AgentProject::getAgencyId, conditions.getUserId())
                .between(AgentProject::getCompleteTime, conditions.getBeginTime(), conditions.getEndTime()));
        if (CollectionUtils.isEmpty(projectList)) {
            return Collections.EMPTY_LIST;
        }

        List<String> projectCodeList = projectList.stream().map(AgentProject::getProjectCode).collect(Collectors.toList());
        List<AgentBidSection> sectionList = bidSectionService.list(Wrappers.lambdaUpdate(AgentBidSection.class)
                .in(AgentBidSection::getProjectCode, projectCodeList));

        List<String> sectionCodeList = sectionList.stream().map(AgentBidSection::getBidSectionCode).collect(Collectors.toList());
        List<AgentSupplier> supplierList = supplierService.list(Wrappers.lambdaUpdate(AgentSupplier.class)
                .in(AgentSupplier::getBidSectionCode, sectionCodeList));
        //组装返回
        List<AgentProjectExcel> voList = assemblyExcelInfo(projectList, sectionList, supplierList);
        if (org.springframework.util.CollectionUtils.isEmpty(voList)) {
            throw new ServiceException("暂无数据");
        }
        return voList;
    }

    /**
     * 组装返回需要导出excel的数据
     *
     * @param projectList  已完成的委托项目信息
     * @param sectionList  标段信息
     * @param supplierList 供应商信息
     * @return
     */
    private List<AgentProjectExcel> assemblyExcelInfo(List<AgentProject> projectList, List<AgentBidSection> sectionList,
                                                      List<AgentSupplier> supplierList) {
        List<AgentProjectExcel> voList = new ArrayList<>();
        projectList.forEach(p -> {
            AgentProjectExcel vo = new AgentProjectExcel();
            BeanUtils.copyProperties(p, vo);
            AgentProjectOperator operator = projectOperatorService.getOne(Wrappers.lambdaQuery(AgentProjectOperator.class)
                    .eq(AgentProjectOperator::getOperatorId, p.getAgencyId())
                    .eq(AgentProjectOperator::getProjectCode, p.getProjectCode()));
            vo.setIntentionTime(Objects.nonNull(p.getIntentionTime()) ? DateUtils.getTimeFormat(DateUtils.YYYY_MM_DD, p.getIntentionTime()) : "/");
            vo.setProcurementMethod(AgentConst.PURCHASE_CLASS_MAP.get(p.getProcurementMethod()));
            vo.setProjectAmount(p.getProjectAmount().toString());
            vo.setIndustriesType(AgentConst.PROJECT.get(p.getIndustriesType()));
            vo.setAgencyName(operator.getOperatorName());
            vo.setEvaluationMethod(p.getEvaluationMethod() == 1 ? "是" : "否");
            vo.setImPort(p.getImPort() == 1 ? "是" : "否");
            int a = 0;
            List<AgentSectionExcel> sectionVoList = new ArrayList<>();
            for (AgentBidSection b : sectionList) {
                if (b.getProjectCode().equals(p.getProjectCode())) {
                    AgentSectionExcel sectionVo = new AgentSectionExcel();
                    sectionVo.setBidSectionName(b.getBidSectionName());
                    sectionVo.setBidOpeningTime(Objects.nonNull(b.getBidOpeningTime())
                            ? DateUtils.getTimeFormat(DateUtils.YYYY_MM_DD, b.getBidOpeningTime()) : "/");
                    sectionVo.setDeadlineBidEvaluation(Objects.nonNull(b.getDeadlineBidEvaluation())
                            ? DateUtils.getTimeFormat(DateUtils.YYYY_MM_DD, b.getDeadlineBidEvaluation()) : "/");
                    sectionVo.setAnnouncementTime(Objects.nonNull(b.getAnnouncementTime())
                            ? DateUtils.getTimeFormat(DateUtils.YYYY_MM_DD, b.getAnnouncementTime()) : "/");
                    sectionVo.setResultPublicityTime(Objects.nonNull(b.getResultPublicityTime())
                            ? DateUtils.getTimeFormat(DateUtils.YYYY_MM_DD, b.getResultPublicityTime()) : "/");
                    sectionVo.setRegistrationDeadline(Objects.nonNull(b.getRegistrationDeadline())
                            ? DateUtils.getTimeFormat(DateUtils.YYYY_MM_DD, b.getRegistrationDeadline()) : "/");

                    List<AgentSupplierExcel> supplierVoList = new ArrayList<>();
                    supplierList.forEach(s -> {
                        if (s.getBidSectionCode().equals(b.getBidSectionCode())) {
                            supplierInfo(s, supplierVoList);
                        }
                    });
                    if (supplierVoList.size() == 0) {
                        supplierInfo(new AgentSupplier(), supplierVoList);
                    }
                    a += supplierVoList.size();
                    sectionVo.setSupplierExcelList(supplierVoList);
                    sectionVo.setMergeRows(supplierVoList.size() == 1 ? 0 : supplierVoList.size());
                    sectionVoList.add(sectionVo);
                }
            }
            vo.setAgentSectionList(sectionVoList);
            vo.setMergeRows(a == 1 ? 0 : a);
            voList.add(vo);
        });
        return voList;
    }

    private void supplierInfo(AgentSupplier s, List<AgentSupplierExcel> supplierVoList) {
        AgentSupplierExcel supplierExcel = new AgentSupplierExcel();
        supplierExcel.setBidderName(StringUtils.isNotBlank(s.getBidderName()) ? s.getBidderName() : "/");
        supplierExcel.setBidWinningPrice(Objects.nonNull(s.getBidWinningPrice()) ? s.getBidWinningPrice().toString() : "/");
        supplierExcel.setInfoReporterName(StringUtils.isNotBlank(s.getInfoReporterName()) ? s.getInfoReporterName() : "/");
        supplierExcel.setInfoReporterContactNumber(StringUtils.isNotBlank(s.getInfoReporterContactNumber()) ? s.getInfoReporterContactNumber() : "/");
        supplierExcel.setBrandModel(StringUtils.isNotBlank(s.getBrandModel()) ? s.getBrandModel() : "/");
        supplierExcel.setCandidateRanking(Objects.nonNull(s.getCandidateRanking()) ? AgentConst.RANK_MAP.get(s.getCandidateRanking()) : "/");
        supplierVoList.add(supplierExcel);
    }


    /**
     * 查询委托系统资料归档文件
     *
     * @param projectCode 委托项目code
     * @return
     */
    @Override
    public AgentFileVo showFile(String projectCode) {
        //只要创建就查出来
        AgentProject project = getOne(Wrappers.lambdaQuery(AgentProject.class)
                .eq(AgentProject::getProjectCode, projectCode));
        if (Objects.isNull(project)) {
            throw new ServiceException("未找到该项目信息");
        }
        List<AgentBidSection> sectionList = bidSectionService.list(Wrappers.lambdaQuery(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, projectCode));
        return assemblyFile(project, sectionList);
    }

    /**
     * 调用文件服务并组装信息
     *
     * @param project     项目信息
     * @param sectionList 标段信息
     * @return
     */
    private AgentFileVo assemblyFile(AgentProject project, List<AgentBidSection> sectionList) {

        R<List<AgencyProjectFileVo>> projectR = remoteFileService.agencyProjectFileList(project.getYearMonthSplit(), project.getProjectCode());
        log.error("已经调用委托系统文件服务==>>项目文件列表agencyProjectFileList()。 yearMonthSplit:{},pCode:{}", project.getYearMonthSplit(), project.getProjectCode());
        if (projectR.hasFail()) {
            log.error("委托系统文件服务调用失败==>>项目文件列表agencyProjectFileList(). yearMonthSplit:{},pCode:{}", project.getYearMonthSplit(), project.getProjectCode());
            throw new ServiceException(projectR.getMsg());
        }
        AgentFileVo vo = new AgentFileVo();
        vo.setProjectName(project.getProjectName());
        vo.setProjectCode(project.getProjectCode());
        vo.setProjectFileList(projectR.getData());

        List<AgentFileSectionVo> sectionVoList = new ArrayList<>();
        sectionList.forEach(s -> {
            AgentFileSectionVo sectionVo = new AgentFileSectionVo();
            R<List<AgencyProjectFileVo>> sectionFileR = remoteFileService.agencyProjectSectionFileList(project.getYearMonthSplit(),
                    project.getProjectCode(), s.getBidSectionCode());
            if (sectionFileR.hasFail()) {
                log.error("委托系统文件服务调用失败==>>标段下的文件列表agencyProjectSectionFileList(). yearMonthSplit:{},pCode:{},bidSectionCode:{}",
                        project.getYearMonthSplit(), project.getProjectCode(), s.getBidSectionCode());
                throw new ServiceException(sectionFileR.getMsg());
            }
            sectionVo.setBidSectionName(s.getBidSectionName());
            sectionVo.setBidSectionCode(s.getBidSectionCode());
            sectionVo.setSectionFileList(sectionFileR.getData());
            sectionVoList.add(sectionVo);
        });
        vo.setSectionVoList(sectionVoList);
        return vo;
    }

    /**
     * 如果文件服务返回为false，则表示文件数量未上传达标
     * 如果文件服务返回为true，则表示文件数量未上传达标
     *
     * @param projectCode 委托招标项目code
     * @return
     */
    @Override
    public void complete(String projectCode) {

        List<AgentBidSection> sections = bidSectionService.list(Wrappers.lambdaUpdate(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, projectCode));
        if (CollectionUtils.isEmpty(sections)) {
            throw new ServiceException("该项目没有标段");
        }
        List<String> codeList = sections.stream().map(AgentBidSection::getBidSectionCode).collect(Collectors.toList());
        R<Boolean> r = remoteFileService.agencyFileNumberVerification(new AgencyFileVerificationDto() {{
            setBuyItemCode(projectCode);
            setSubpackageCode(codeList);
        }});
        if (r.hasFail()) {
            log.error("调用文件接接口agencyFileNumberVerification()失败，r:{}, pCode:{},bidSectionCode:{}", r, projectCode, codeList);
            throw new ServiceException("请上传所有必传文件");
        }
        if (!r.getData()) {
            log.error("调用文件接接口agencyFileNumberVerification()返回为false，r:{}, pCode:{},bidSectionCode:{}", r, projectCode, codeList);
            throw new ServiceException("请上传所有必传文件");
        }
        //修改项目状态为已完成
        update(Wrappers.lambdaUpdate(AgentProject.class)
                .set(AgentProject::getProjectStatus, AgentConstants.Status.COMPLETE)
                .set(AgentProject::getCompleteTime, new Date())
                .eq(AgentProject::getProjectCode, projectCode));
    }

    /**
     * 查询委托项目是否已经完成
     * 是否保存招标准备[0-未保存，1-已保存]',
     * 是否保存中标结果[0-未保存，1-已保存]',
     *
     * @param projectCode 委托招标项目code
     * @return
     */
    @Override
    public StatusVo isComplete(String projectCode) {

        AgentProject agentProject = getOne(Wrappers.lambdaUpdate(AgentProject.class)
                .eq(AgentProject::getProjectCode, projectCode));
        return new StatusVo() {{
            if (agentProject.getProjectStatus() == 3) {
                setComplete(true);
            }
            if (agentProject.getEnterPrepare() == 1) {
                setEnterPrepare(true);
            }
            if (agentProject.getSaveResult() == 1) {
                setSaveResult(true);
            }
        }};
    }

    /**
     * 删除委托项目
     * 删除委托项目信息
     * 包含文件信息以及供应商信息
     *
     * @param projectCode 委托招标项目code
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean del(String projectCode) {
        //删除项目信息
        remove(Wrappers.lambdaUpdate(AgentProject.class)
                .eq(AgentProject::getProjectCode, projectCode));
        //删除项目与参与使用科室代表
        projectOperatorService.remove(Wrappers.lambdaUpdate(AgentProjectOperator.class)
                .eq(AgentProjectOperator::getProjectCode, projectCode));
        //删除标段
        List<AgentBidSection> sectionList = bidSectionService.list(Wrappers.lambdaUpdate(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, projectCode));
        bidSectionService.remove(Wrappers.lambdaUpdate(AgentBidSection.class)
                .eq(AgentBidSection::getProjectCode, projectCode));
        //删除代理供应商
        sectionList.forEach(b -> supplierService.remove(Wrappers.lambdaUpdate(AgentSupplier.class)
                .eq(AgentSupplier::getBidSectionCode, b.getBidSectionCode())));
        //删除项目相关文件
        R<Boolean> r = remoteFileService.deleteAgencyProjectFileAll(projectCode);
        if (r.hasFail()) {
            log.error("删除委托项目文件失败 r:{}, pCode：{}", r, projectCode);
            throw new ServiceException("删除项目失败");
        }
        return Boolean.TRUE;
    }
}
