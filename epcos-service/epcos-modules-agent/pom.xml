<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.epcos</groupId>
        <artifactId>epcos-service</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>epcos-modules-agent</artifactId>
    <description>委托项目管理系统</description>

    <dependencies>
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-api-agent</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-file</artifactId>
        </dependency>


        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- epcos Common DataSource -->
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-log</artifactId>
        </dependency>

        <!-- epcos Common Swagger -->
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jxls</groupId>
            <artifactId>jxls</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jxls</groupId>
            <artifactId>jxls-poi</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>application-*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>bootstrap.yml</include>
                    <include>application.yml</include>
                    <include>application-${epc}.yml</include>
                    <include>application-sz.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <epc>dev</epc>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <epc>test</epc>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <epc>prod</epc>
            </properties>
        </profile>
        <profile>
            <id>sz</id>
            <properties>
                <epc>sz</epc>
            </properties>
        </profile>
        <profile>
            <id>th</id>
            <properties>
                <epc>th</epc>
            </properties>
        </profile>
        <profile>
            <id>zl</id>
            <properties>
                <epc>zl</epc>
            </properties>
        </profile>
        <profile>
            <id>pr</id>
            <properties>
                <epc>pr</epc>
            </properties>
        </profile>
        <profile>
            <id>xk</id>
            <properties>
                <epc>xk</epc>
            </properties>
        </profile>

        <profile>
            <id>lg</id>
            <properties>
                <epc>lg</epc>
            </properties>
        </profile>

        <profile>
            <id>xy</id>
            <properties>
                <epc>xy</epc>
            </properties>
        </profile>
        <profile>
            <id>cq</id>
            <properties>
                <epc>cq</epc>
            </properties>
        </profile>
        <profile>
            <id>ws</id>
            <properties>
                <epc>ws</epc>
            </properties>
        </profile>
        <profile>
            <id>fj</id>
            <properties>
                <epc>fj</epc>
            </properties>
        </profile>
    </profiles>

</project>
