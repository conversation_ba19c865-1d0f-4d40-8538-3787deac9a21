spring:
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    database: ${redis.database}
    lettuce:
      pool:
        enabled: true
        max-active: 50
        max-idle: 50
        min-idle: 2
        max-wait: 1000ms
        time-between-eviction-runs: 60000ms
  cloud:
    nacos:
      discovery:
        register-enabled: true
        group: DEFAULT_GROUP
        service: ${spring.application.name}
        username: ${nacos.username}
        password: ${nacos.password}
        server-addr: ${nacos.server-addr}
        namespace: ${nacos.namespace}
    gateway:
      discovery:
        locator:
          lower-case-service-id: true
          enabled: true
      routes:
        - id: epcos-auth
          uri: lb://epcos-auth
          predicates:
            - Path=/auth/**
          filters:
            # 验证码处理
            - CacheRequestFilter
            - ValidateCodeFilter
            - StripPrefix=1
        - id: epcos-system
          uri: lb://epcos-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
        - id: epcos-epcfile
          uri: lb://epcos-epcfile
          predicates:
            - Path=/epcfile/**
          filters:
            - StripPrefix=1
        - id: epcos-seal
          uri: lb://epcos-seal
          predicates:
            - Path=/seal/**
          filters:
            - StripPrefix=1
        - id: epcos-pay
          uri: lb://epcos-pay
          predicates:
            - Path=/pay/**
          filters:
            - StripPrefix=1
        - id: epcos-bidding
          uri: lb://epcos-bidding
          predicates:
            - Path=/bidding/**
          filters:
            - StripPrefix=1
        - id: epcos-bidder
          uri: lb://epcos-bidder
          predicates:
            - Path=/bidder/**
          filters:
            - StripPrefix=1
        - id: epcos-review
          uri: lb://epcos-review
          predicates:
            - Path=/review/**
          filters:
            - StripPrefix=1
        - id: epcos-agent
          uri: lb://epcos-agent
          predicates:
            - Path=/agent/**
          filters:
            - StripPrefix=1

security:
  ignore:
    whites:
      - /**/druid/**
      - /**/swagger-ui*/**
      - /**/api-docs/**
      - /**/public/**
      - /auth/logout
      - /auth/login
      - /auth/register
      - /auth/smsSend
      - /csrf
      - /**/download/**
      - /system/dict/data/type
      - /system/notice/list
      - /system/stomp/**
      - /system/config/configKey
      - /system/user/profile/forgetPassword
      - /bidding/supplier/pay/redirectPageUrl
      - /pay/order/pay/notify
      - /pay/order/pay/notifyPage
      - /pay/order/refund/notify
      - /seal/signSealBack/receiveAuthorizationResult
  captcha:
    enabled: true
    type: math
  xss:
    enabled: true
    excludeUrls:
      - /**/notice/**

rsa:
  request:
    enabled: true
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCC3Lb0O4zgEakDfJ4XJO5zadXep9bQeWyJ6pa0e328PYQYZgLNP7eVrAP7mVZgG+8D4MicIcStTQnBxF8AEyJKrh/M/3WSSK2zDvrZn1paWf4SA8zFIn5cuYlcUH+WuxghQn3kKRUW2qtBY9eaGF5qntascctNgQTHmW3eqQzDBQIDAQAB
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAILctvQ7jOARqQN8nhck7nNp1d6n1tB5bInqlrR7fbw9hBhmAs0/t5WsA/uZVmAb7wPgyJwhxK1NCcHEXwATIkquH8z/dZJIrbMO+tmfWlpZ/hIDzMUifly5iVxQf5a7GCFCfeQpFRbaq0Fj15oYXmqe1qxxy02BBMeZbd6pDMMFAgMBAAECgYAeNo07CEC3PvyRpITvfQRcuzO4q3GKQm6PzQyscNKP0ngcFflRoANn2AY3YaiymeBuOci3W4iIJoA5L3hrkP9agBhW8EQIPl/hSm16aq7/m14dtnHPlOdwLL8VmK+lsG2qujmn3DdPROqMorX48UBshnC+2qwa6Z18tFTeX7jkJQJBALzQpkKKI83U1APPiopzZh6dw2tKjm8Fv1iEnig+73XrDU1EHIrB1OQAoSR0/lR5gmOKBCQgDKJFpOaevcctZIMCQQCxbRSOi6DKMdp8u5iTHDW4BcFy30RpAr1I8EPfClIIDxhTJyriRMimsaNSi0U89a11KFPao1NRAppNIjjhV/PXAkBEsVg7jwxVnx9/P2t00WUOsHDfQOGu7JVfu+faVoNCEEDnomcL6FbumHgHznSVARv54MV+6xYNl27V5FzA8PfHAkEAgGZ0bgRWRH13pLUOYcgUiCyCJuO02loFKffW5l7Nps9lxyedPUqR+zCrBV2MqeURtquMo7l23jWH/TI6/7lq2QJAT4CCSX3RAHRz1pk4Z0/lJV1SN5pMfy8Botpko3Bj7qYFImfkZhqacWtJ6X7VxumgCLK3t1953nQSALtAwQ6GvA==
    whiteList: /**/stomp/**,/**/public/webservice/**
  response:
    enabled: true
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0Tqinlrrvhus+le5XrGgCAvBVlifKmEUcuB222pCQR/jhFnw/tPzJjJ2dvGjIld6g8t+Ei33XQ8b1iaP0kfIi/G43qjhEMXp7vDoHnPtvdMOBfCFuiKbP5GeaZ5IP01J9RgEzklfkWwfdKJLspGwRGximjahnMyf5SO0aXzbwqQIDAQAB
    privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALROqKeWuu+G6z6V7lesaAIC8FWWJ8qYRRy4HbbakJBH+OEWfD+0/MmMnZ28aMiV3qDy34SLfddDxvWJo/SR8iL8bjeqOEQxenu8Ogec+290w4F8IW6Ips/kZ5pnkg/TUn1GATOSV+RbB90okuykbBEbGKaNqGczJ/lI7RpfNvCpAgMBAAECgYA9RzJYaoizmRXgGlJ7Z3Odo2QMolB5sRBj90rZ9yQEdQFndh3aBOeYk/qJPhwad5zG9GP0hvfIrhczIYkgOG2i1ZvBAFBP7IZiGJz5PxS9QOFPg926sI6Mv3nBIS0+U88IyzPL/fQWNvhc3b9Y95kYp4p0Wk4zzNe9HNNUMQHdUQJBAOwA6EoVSlxlpNivoAGrMynLlnHmZ7fEpXXQINUbhpX8+I3fazoWcRaYpfLmVKa82DJXHUe8URFX3oir3kAocVUCQQDDlahWFmYmtNYqLitJdIdltTcmQtAgHlfshdYnq6Gg8jSjwh40sXF8MgZfG03+sfdmKbSG3e+7Ihb/X5P/odIFAkEAlz3Rn0BbojDlXpPWN5uOMzesFxwv1Z3o50JU+B0mt9IhO1I1dklRecijeLFRCHW3GzOmqQUu8q1cCDwUNwtz7QJBAJ3BT8coR/q+b+QT20xjVnaeBT6yM2dEskyP4x2aXUMROY5Am9aKrWuseeEqh+2ApHld+EO0LZJ2O7B96kUNw/UCQHhXTTBHc2HkyU84U2+OAB2hJtJBmj+eGl0iqNfOq3JyiIemC/bV74sASLa+NN9CJRotBh9jzmzNpwEi24Y8KHE=
    whiteList: /**/v2/api-docs,/**/stomp/**,/**/download*/**,/**/filePreview,/**/genExcel,/**/generate,/**/export*,/**/*Export*,/**/importTemplate
