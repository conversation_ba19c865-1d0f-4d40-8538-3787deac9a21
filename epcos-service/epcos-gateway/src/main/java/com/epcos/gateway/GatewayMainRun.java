package com.epcos.gateway;

import com.epcos.common.core.utils.LogPrint;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.util.pattern.PathPatternParser;

import java.time.Duration;


/**
 * 网关启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class GatewayMainRun {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(GatewayMainRun.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  网关启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        LogPrint.log(context.getEnvironment());
    }


//    @Bean
    public CorsWebFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        //请求方法 *支持所有方法
        config.addAllowedMethod("*");
        //跨域处理 *允许所有的域
        config.addAllowedOrigin("*");
        //请求头 *支持所有
        config.addAllowedHeader("*");
        config.setMaxAge(Duration.ofMinutes(6));
         //config.setAllowedOriginPatterns(Arrays.asList("http://localhost:[*]", "http://epcos.epc1688.com"));
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource(new PathPatternParser());
        //匹配所有请求
        source.registerCorsConfiguration("/**", config);
        return new CorsWebFilter(source);
    }

}
