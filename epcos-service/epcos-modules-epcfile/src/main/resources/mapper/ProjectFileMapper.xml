<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.file.mapper.ProjectFileMapper">

    <resultMap type="com.epcos.file.domain.ProjectFile" id="FileStorageInfoResult">
        <result property="id" column="id"/>
        <result property="fileCode" column="file_code"/>
        <result property="buyItemCode" column="buy_item_code"/>
        <result property="subpackageCode" column="subpackage_code"/>
        <result property="supplierNumber" column="supplier_number"/>
        <result property="fileTypeName" column="file_type_name"/>
        <result property="yearMonthSplit" column="year_month_split"/>
        <result property="fileStoragePath" column="file_storage_path"/>
        <result property="createAt" column="create_at"/>
        <result property="updateAt" column="update_at"/>
    </resultMap>

    <sql id="selectFileStorageInfoVo">
        select id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split, file_storage_path, create_at, update_at
        from epcfile_project
    </sql>


    <select id="selectFileStorageInfoList" parameterType="com.epcos.file.domain.ProjectFile" resultMap="FileStorageInfoResult">
        <include refid="selectFileStorageInfoVo"/>
        <where>
            <if test="fileCode != null  and fileCode != ''">and file_code = #{fileCode}</if>
            <if test="buyItemCode != null  and buyItemCode != ''">and buy_item_code = #{buyItemCode}</if>
            <if test="subpackageCode != null  and subpackageCode != ''">and subpackage_code = #{subpackageCode}</if>
            <if test="supplierNumber != null ">and supplier_number = #{supplierNumber}</if>
            <if test="fileTypeName != null  and fileTypeName != ''">and file_type_name like concat('%', #{fileTypeName}, '%') </if>
            <if test="yearMonthSplit != null  and yearMonthSplit != ''">and year_month_split = #{yearMonthSplit}</if>
            <if test="fileStoragePath != null  and fileStoragePath != ''">and file_storage_path = #{fileStoragePath} </if>
        </where>
    </select>

    <select id="selectFileList" parameterType="com.epcos.file.domain.ProjectFile" resultMap="FileStorageInfoResult">
        select
            id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split, file_storage_path, create_at, update_at
        from
            epcfile_project
        where
            buy_item_code = #{buyItemCode}  and  (subpackage_code = #{subpackageCode} or subpackage_code = '' ) and file_status != 1
    </select>


    <select id="selectProjectFileAll" parameterType="com.epcos.file.domain.ProjectFile" resultMap="FileStorageInfoResult">
        select
            id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split, file_storage_path, create_at, update_at
        from
            epcfile_project
        where
            buy_item_code = #{buyItemCode}
    </select>

    <select id="fileArchivingListBySection" parameterType="com.epcos.file.domain.ProjectFile" resultMap="FileStorageInfoResult">
        select id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split, file_storage_path, create_at, update_at from epcfile_project
        where  buy_item_code = #{buyItemCode} and subpackage_code = #{subpackageCode} and file_status != 1
    </select>


    <select id="fileArchivingListByProject" parameterType="com.epcos.file.domain.ProjectFile" resultMap="FileStorageInfoResult">
        select id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split, file_storage_path, create_at, update_at from epcfile_project
        where  buy_item_code = #{buyItemCode} and subpackage_code = '' and file_status != 1
    </select>


    <select id="selectByProjectFileList" parameterType="com.epcos.file.domain.ProjectFile" resultMap="FileStorageInfoResult">
        select
        id, file_code, buy_item_code, subpackage_code, supplier_number, file_type_name, year_month_split,
        file_storage_path, create_at, update_at
        from
        epcfile_project
        <where>
            <if test="buyItemCode != null  and buyItemCode != ''">and buy_item_code = #{buyItemCode}</if>
            <if test="yearMonthSplit != null  and yearMonthSplit != ''">and year_month_split = #{yearMonthSplit}</if>
        </where>
    </select>


    <select id="selectFileStorageInfoById" parameterType="Long" resultMap="FileStorageInfoResult">
        <include refid="selectFileStorageInfoVo"/>
        where id = #{id}
    </select>

    <select id="selByFileKey" parameterType="String" resultMap="FileStorageInfoResult">
        <include refid="selectFileStorageInfoVo"/>
        where file_code = #{fileKey}
    </select>

    <select id="selectByzipFileStorage" parameterType="String" resultMap="FileStorageInfoResult">
        <include refid="selectFileStorageInfoVo"/>
        where file_storage_path = #{fileStoragePath}
    </select>

    <insert id="insertFileStorageInfo" parameterType="com.epcos.file.domain.ProjectFile" useGeneratedKeys="true" keyProperty="id">
        insert into epcfile_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileCode != null and fileCode != ''">file_code,</if>
            <if test="buyItemCode != null and buyItemCode != ''">buy_item_code,</if>
            <if test="subpackageCode != null and subpackageCode != ''">subpackage_code,</if>
            <if test="supplierNumber != null">supplier_number,</if>
            <if test="fileTypeName != null and fileTypeName != ''">file_type_name,</if>
            <if test="yearMonthSplit != null and yearMonthSplit != ''">year_month_split,</if>
            <if test="fileStatus != null">file_status,</if>
            <if test="fileStoragePath != null and fileStoragePath != ''">file_storage_path,</if>
            <if test="createAt != null">create_at,</if>
            <if test="updateAt != null">update_at,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileCode != null and fileCode != ''">#{fileCode},</if>
            <if test="buyItemCode != null and buyItemCode != ''">#{buyItemCode},</if>
            <if test="subpackageCode != null and subpackageCode != ''">#{subpackageCode},</if>
            <if test="supplierNumber != null">#{supplierNumber},</if>
            <if test="fileTypeName != null and fileTypeName != ''">#{fileTypeName},</if>
            <if test="yearMonthSplit != null and yearMonthSplit != ''">#{yearMonthSplit},</if>
            <if test="fileStatus != null">#{fileStatus},</if>
            <if test="fileStoragePath != null and fileStoragePath != ''">#{fileStoragePath},</if>
            <if test="createAt != null">#{createAt},</if>
            <if test="updateAt != null">#{updateAt},</if>
        </trim>
    </insert>

    <update id="updateFileStorageInfo" parameterType="com.epcos.file.domain.ProjectFile">
        update epcfile_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileCode != null and fileCode != ''">file_code = #{fileCode},</if>
            <if test="buyItemCode != null and buyItemCode != ''">buy_item_code = #{buyItemCode},</if>
            <if test="subpackageCode != null and subpackageCode != ''">subpackage_code = #{subpackageCode},</if>
            <if test="supplierNumber != null">supplier_number = #{supplierNumber},</if>
            <if test="fileTypeName != null and fileTypeName != ''">file_type_name = #{fileTypeName},</if>
            <if test="yearMonthSplit != null and yearMonthSplit != ''">year_month_split = #{yearMonthSplit},</if>
            <if test="fileStoragePath != null and fileStoragePath != ''">file_storage_path = #{fileStoragePath},</if>
            <if test="createAt != null">create_at = #{createAt},</if>
            <if test="updateAt != null">update_at = #{updateAt},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="upFileStoragePath"   parameterType="com.epcos.file.domain.ProjectFile" >
        update
            epcfile_project
        set
            file_storage_path = #{fileStoragePath} where file_code = #{fileCode}
    </update>

    <delete id="deleteFileStorageInfoById" parameterType="Long">
        delete from epcfile_project where id = #{id}
    </delete>

    <delete id="deleteFileStorageInfoByIds" parameterType="String">
        delete from epcfile_project where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByFileKey"  parameterType="String">
        delete from epcfile_project where file_code = #{fileCode}
    </delete>

    <select id="selectByBidSectionCode" parameterType="String" resultMap="FileStorageInfoResult">
        <include refid="selectFileStorageInfoVo"/>
        where subpackage_code = #{subpackageCode}
    </select>


    <select id="selectByFileKeys" resultType="com.epcos.file.domain.ProjectFile">
        <include refid="selectFileStorageInfoVo"/>
        where file_code in
        <foreach item="fileKey" collection="fileKeyList" open="(" separator="," close=")">
            #{fileKey}
        </foreach>
    </select>
    <select id="selFileZip" resultType="com.epcos.file.domain.ProjectFile">
        <include refid="selectFileStorageInfoVo"/>
        where buy_item_code = #{buyItemCode} and file_type_name = #{fileType}
    </select>


    <delete id="deleteFileBysubpackageCode" parameterType="Long">
        delete from  epcfile_project  where subpackage_code = #{subpackageCode}
    </delete>


    <delete id="deleteFileZip">
        delete from epcfile_project where buy_item_code = #{buyItemCode} and file_type_name = #{fileType}
    </delete>

</mapper>
