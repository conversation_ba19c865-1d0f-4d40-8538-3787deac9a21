package com.epcos.file.service.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.epcfile.api.domain.vo.FileDataVo;
import com.epcos.file.common.file.FileHelper;
import com.epcos.file.domain.ProjectFile;
import com.epcos.file.mapper.ProjectFileMapper;
import com.epcos.file.service.IFileService;
import com.epcos.file.service.INonProjectFileService;
import com.epcos.file.service.IProjectFileService;
import com.epcos.system.api.RemoteUserService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购文件存储Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-26
 */
@Log4j2
@Service
public class ProjectFileServiceImpl implements IProjectFileService {

    private final ProjectFileMapper fileStorageInfoMapper;

    @Autowired
    private IFileService fileService;
    @Autowired
    private INonProjectFileService nonProjectFileService;

    @Autowired
    private RemoteUserService remoteUserService;

    public ProjectFileServiceImpl(ProjectFileMapper fileStorageInfoMapper) {
        this.fileStorageInfoMapper = fileStorageInfoMapper;
    }

    /**
     * 根据文件file_code查询epcfile_project表
     *
     * @param fileKey
     * @return
     */
    @Override
    public ProjectFile selectFileStorageInfoByFileKey(String fileKey) {
        return fileStorageInfoMapper.selByFileKey(fileKey);
    }

    /**
     * 所有项目归档文件列表
     *
     * @param projectDocument
     * @return
     */
    @Override
    public List<ProjectFile> selectFileList(ProjectFile projectDocument) {
        List<ProjectFile> fileList = fileStorageInfoMapper.selectFileList(projectDocument);
        return fileListUpdateFileName(fileList);
    }

    /**
     * 修改文件列表中的文件名
     *
     * @param fileList
     * @return
     */
    public List<ProjectFile> fileListUpdateFileName(List<ProjectFile> fileList) {
        if (fileList == null || fileList.isEmpty()) {
            log.error("文件信息列表为空");
            return null;
        }
        for (ProjectFile pd : fileList) {
            String fileName;
            if (pd.getSupplierNumber() != null) {
                R<SysUser> r = remoteUserService.getInfoById(pd.getSupplierNumber(), SecurityConstants.INNER);
                if (r.hasFail() || r.getData() == null || r.getData().getNickName() == null) {
                    fileName = FileHelper.pathToFileName(pd.getFileStoragePath());
                } else {
                    fileName = r.getData().getNickName() + "-" + FileHelper.pathToFileName(pd.getFileStoragePath());
                }
            } else {
                fileName = FileHelper.pathToFileName(pd.getFileStoragePath());
            }
            pd.setFileStoragePath(fileName);
        }
        return fileList;
    }

    /**
     * 项目归档文件列表
     *
     * @param projectDocument
     * @return
     */
    @Override
    public List<ProjectFile> fileArchivingListByProject(ProjectFile projectDocument) {
        List<ProjectFile> fileList = fileStorageInfoMapper.fileArchivingListByProject(projectDocument);
        return fileListUpdateFileName(fileList);
    }

    /**
     * 按标段查项目归档文件列表
     *
     * @param projectDocument
     * @return
     */
    @Override
    public List<ProjectFile> fileArchivingListBySection(ProjectFile projectDocument) {
        List<ProjectFile> fileList = fileStorageInfoMapper.fileArchivingListBySection(projectDocument);
        return fileListUpdateFileName(fileList);
    }

    /**
     * 数据库中修改文件存储路径
     *
     * @param fileKey         文件key
     * @param fileStoragePath 文件路径
     * @return
     */
    @Override
    public R<Boolean> updateProjectFilePath(String fileKey, String fileStoragePath) {

        ProjectFile projectDocument = new ProjectFile();
        projectDocument.setFileCode(fileKey);
        projectDocument.setFileStoragePath(fileStoragePath);
        int i = fileStorageInfoMapper.upFileStoragePath(projectDocument);
        if (i > 0) {
            return R.ok(true, "修改成功");
        } else {
            log.error("数据库中修改文件存储路径失败,fileKey:{},fileStoragePath:{}", fileKey, fileStoragePath);
            return R.fail(false, "修改失败");
        }
    }


    /**
     * 归档文件打包
     *
     * @param fileKeyList    文件key集合
     * @param buyItemCode    项目code
     * @param yearMonthSplit
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<Boolean> projectFilesByzip(List<String> fileKeyList, String buyItemCode, String yearMonthSplit) {
        FilesToZip(fileKeyList, buyItemCode, FileTypeNameConstants.FILE_ZIP);
        return R.ok(true, "打包成功");
    }

    /**
     * 项目内归档文件批量下载
     *
     * @param fileKeyList 文件key集合
     * @param buyItemCode 项目code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseEntity<Resource> downloadFileList(List<String> fileKeyList, String buyItemCode) {
        // 文件打包
        String fileCode = FilesToZip(fileKeyList, buyItemCode, FileTypeNameConstants.FILE_ZIP);
        return fileService.download(fileCode, 1);
    }

    /**
     * 文件打包
     *
     * @param fileKeyList 文件key集合
     * @param buyItemCode 项目code
     * @param fileType    文件类型
     * @return zip文件路径
     */
    private String FilesToZip(List<String> fileKeyList, String buyItemCode, String fileType) {
        // 年月
        String yearMonthSplit = FileHelper.getYearMonthSplit();
        ProjectFile projectDocument = new ProjectFile();
        projectDocument.setYearMonthSplit(yearMonthSplit);
        projectDocument.setBuyItemCode(buyItemCode);

        List<ProjectFile> fileList;
        // 根据项目code查询
        if (fileKeyList == null || fileKeyList.isEmpty()) {
            fileList = fileStorageInfoMapper.selectByProjectFileList(projectDocument);
        } else { // 根据fileKey批量查询
            fileList = fileStorageInfoMapper.selectByFileKeys(fileKeyList);
        }
        if (fileList == null || fileList.isEmpty()) {
            log.error("文件查询为空,buyItemCode={}", buyItemCode);
            throw new ServiceException("文件查询为空");
        }
        List<String> filePathList = fileList.stream()
                .filter(path -> path.getFileStoragePath() != null
                        && !path.getFileStoragePath().isEmpty()
                        && !FileTypeNameConstants.FILE_ZIP.equals(path.getFileTypeName()))
                .map(ProjectFile::getFileStoragePath)
                .collect(Collectors.toList());
        String zipFilePath = fileService.fileToZip(filePathList, buyItemCode);

        // 删除旧文件
        List<ProjectFile> projectFiles = fileStorageInfoMapper.selFileZip(buyItemCode, fileType);
        if (projectFiles != null && !projectFiles.isEmpty()) {
            for (ProjectFile pf : projectFiles) {
                fileService.delFileToLocal(pf.getFileStoragePath());
            }
        }
        // 删除旧的压缩文件存储地址
        fileStorageInfoMapper.deleteFileZip(buyItemCode, fileType);
        String fileCode = FileHelper.getUuid();
        projectDocument.setFileTypeName(fileType);
        projectDocument.setFileStoragePath(zipFilePath);
        projectDocument.setFileCode(fileCode);
        projectDocument.setCreateAt(DateTime.now());
        // 新增采购文件存储
        int i = fileStorageInfoMapper.insertFileStorageInfo(projectDocument);
        if (i < 1) {
            log.error("添加zip文件路径存储失败,projectDocument={}", projectDocument.toString());
            throw new ServiceException("添加zip文件路径存储失败");
        }
        return fileCode;
    }

//    /**
//     * 文件打包
//     *
//     * @param fileKeyList    文件key集合
//     * @param buyItemCode    项目code
//     * @param fileType       文件类型
//     * @param yearMonthSplit 项目年月
//     * @return zip文件路径
//     */
//    private String FilesToZip(List<String> fileKeyList, String buyItemCode, String fileType, String yearMonthSplit) {
//
//        ProjectFile projectDocument = new ProjectFile();
//        projectDocument.setBuyItemCode(buyItemCode);
//        projectDocument.setYearMonthSplit(yearMonthSplit);
//
//        List<ProjectFile> fileList;
//        // 根据项目code查询
//        if (fileKeyList == null || fileKeyList.isEmpty()) {
//            fileList = fileStorageInfoMapper.selectByProjectFileList(projectDocument);
//        } else { // 根据fileKey批量查询
//            fileList = fileStorageInfoMapper.selectByFileKeys(fileKeyList);
//        }
//        if (fileList == null || fileList.isEmpty()) {
//            log.error("文件查询为空,buyItemCode={},fileType={},yearMonthSplit={}", buyItemCode, fileType, yearMonthSplit);
//            throw new ServiceException("文件查询为空");
//        }
//        Map<String, String> files = new HashMap<>();
//        for (ProjectFile file : fileList) {
//            String fileTypeName = "projectZip";
//            if (file.getFileTypeName().contains(fileTypeName)) {
//                log.error("文件中包含projectZip类型的文件,fileKey={},fileTypeName:{}", file.getFileCode(), file.getFileTypeName());
//                throw new ServiceException("文件中包含projectZip类型的文件,不可打包");
//            }
//
//            File locFile = new File(file.getFileStoragePath());
//            if (locFile.exists()) {
//                String fileName = locFile.getName();
//                String filePath = file.getFileStoragePath();
//                files.put(fileName, filePath);
//            }
//        }
//        if (CollectionUtils.isEmpty(files)) {
//            log.error("文件查询异常,数量不对,fileKeyList={}", fileList.toString());
//            throw new ServiceException("未找到对应文件");
//        }
//        // 组装zip文件路径
//        String zipPath = FileHelper.filePath(yearMonthSplit, buyItemCode, null, null, fileType);
//        // 编辑压缩文件地址,压缩文件
//        String filePath = fileService.projectFilesZip(files, zipPath, buyItemCode);
//        // 删除旧的压缩文件存储地址
//        fileStorageInfoMapper.deleteFileZip(buyItemCode, fileType);
//        String fileCode = FileHelper.getUuid();
//        projectDocument.setFileTypeName(fileType);
//        projectDocument.setFileStoragePath(filePath);
//        projectDocument.setFileCode(fileCode);
//        projectDocument.setCreateAt(DateTime.now());
//        // 新增采购文件存储
//        int i = fileStorageInfoMapper.insertFileStorageInfo(projectDocument);
//        if (i < 1) {
//            log.error("添加zip文件路径存储失败,projectDocument={}", projectDocument.toString());
//            throw new ServiceException("添加zip文件路径存储失败");
//        }
//        return filePath;
//    }

    /**
     * 新增采购文件存储
     *
     * @param projectDocument
     * @return
     */
    @Override
    public int insertFileStorageInfo(ProjectFile projectDocument) {
        return fileStorageInfoMapper.insertFileStorageInfo(projectDocument);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByFileKey(String fileCode) {
        ProjectFile filedStorage = fileStorageInfoMapper.selByFileKey(fileCode.trim());
        if (filedStorage == null) {
            log.error("文件不存在,fileCode:{}", fileCode);
            return true;
//            throw new ServiceException("文件不存在");
        }
        int i = fileStorageInfoMapper.deleteByFileKey(fileCode);
        if (i < 1) {
            log.error("文件不存在,fileCode:{}", fileCode);
//            throw new ServiceException("文件不存在");
        }
        delFile(filedStorage.getFileStoragePath());
        return true;
    }

    /**
     * 项目内归档文件批量删除
     *
     * @param buyItemCode
     * @param subpackageCode
     * @param fileTypeName
     * @param supplierNumber
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBiddingFileList(String buyItemCode, String subpackageCode, String fileTypeName, Long supplierNumber) {
        if (StringUtils.isEmpty(fileTypeName) || (StringUtils.isEmpty(subpackageCode) && StringUtils.isEmpty(buyItemCode))) {
            log.error("传入信息为空,buyItemCode={},subpackageCode={},fileTypeName={}", buyItemCode, subpackageCode, fileTypeName);
            throw new ServiceException("传入信息不能为空");
        }
        ProjectFile projectFile = new ProjectFile();
        projectFile.setBuyItemCode(buyItemCode);
        projectFile.setSubpackageCode(subpackageCode);
        projectFile.setFileTypeName(fileTypeName);
        projectFile.setSupplierNumber(supplierNumber);
        List<ProjectFile> projectDocuments = fileStorageInfoMapper.selectFileStorageInfoList(projectFile);
        if (projectDocuments == null || projectDocuments.isEmpty()) {
            log.error("查询到的文件为空,buyItemCode={},subpackageCode={},fileTypeName={}", buyItemCode, subpackageCode, fileTypeName);
            return true;
        }
        for (ProjectFile fi : projectDocuments) {
            fileStorageInfoMapper.deleteByFileKey(fi.getFileCode());
            delFile(fi.getFileStoragePath());
        }
        return true;
    }

    @Override
    public Boolean deleteProjectFileAll(String buyItemCode) {
        ProjectFile init = new ProjectFile();
        init.setBuyItemCode(buyItemCode);
        List<ProjectFile> projectDocuments = fileStorageInfoMapper.selectProjectFileAll(init);
        if (!CollectionUtils.isEmpty(projectDocuments)) {
            for (ProjectFile fi : projectDocuments) {
                if (!ObjectUtils.isEmpty(fi)) {
                    fileStorageInfoMapper.deleteByFileKey(fi.getFileCode());
                    delFile(fi.getFileStoragePath());
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByBidSectionCode(String subpackageCode) {
        try {
            List<ProjectFile> fileList = fileStorageInfoMapper.selectByBidSectionCode(subpackageCode);
            if (!CollectionUtils.isEmpty(fileList)) {
                fileStorageInfoMapper.deleteFileBysubpackageCode(subpackageCode);
                for (ProjectFile fsi : fileList) {
                    delFile(fsi.getFileStoragePath());
                }
            }
        } catch (Exception e) {
            log.error("根据标段code删除文件失败,subpackageCode={},e={}", subpackageCode, e);
            throw new RuntimeException("根据标段code删除文件失败");
        }
    }


    /**
     * 异步调用删除物理文件方法
     *
     * @param filePath 文件路径
     */
    @Async
    @Override
    public void delFile(String filePath) {
        FileHelper.delFile(filePath);
    }

    /**
     * 修改文件状态(将临时文件改为正常文件)
     *
     * @param fileKeys
     * @return
     */
    @Override
    public R<Boolean> updateProjectFileStatus(List<String> fileKeys) {

        ProjectFile projectFile = new ProjectFile();
        projectFile.setFileStatus(0);
        QueryWrapper<ProjectFile> qw = new QueryWrapper<>();
        qw.lambda().in(ProjectFile::getFileCode, fileKeys);
        int update = fileStorageInfoMapper.update(projectFile, qw);
        if (update != fileKeys.size()) {
            log.error("将临时文件改为正常文件失败,fileKeys={}", Arrays.toString(fileKeys.toArray()));
            throw new ServiceException("将临时文件改为正常文件失败");
        }
        return R.ok(true);
    }

    /**
     * 删除临时文件
     *
     * @param buyItemCode
     * @param subpackageCode
     * @param supplierNumber
     * @return
     */
    @Override
    public R<Boolean> delTemporaryFile(String buyItemCode, String subpackageCode, Long supplierNumber) {
        if (buyItemCode == null && subpackageCode == null) {
            log.error("项目code和标段code不能同时为空");
            throw new ServiceException("项目code和标段code不能同时为空");
        }
        QueryWrapper<ProjectFile> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjectFile::getBuyItemCode, buyItemCode)
                .eq(ProjectFile::getSubpackageCode, subpackageCode)
                .eq(ProjectFile::getSupplierNumber, supplierNumber)
                .eq(ProjectFile::getFileStatus, 1);
        fileStorageInfoMapper.delete(qw);
        return R.ok(true);
    }

    /**
     * 非项目文件转移到项目中
     *
     * @param fileKeyList
     * @return
     */
    @Override
    public R<Boolean> noFileToProjectFile(List<String> fileKeyList, String buyItemCode) {
        R<List<FileDataVo>> fileInfo = nonProjectFileService.getFileInfo(fileKeyList);

        if (fileInfo.getData() == null || fileInfo.getData().isEmpty()) {
            return R.fail(false, "需要添加的数据为空");
        }

        QueryWrapper<ProjectFile> qw = new QueryWrapper<>();
        qw.lambda().eq(ProjectFile::getFileTypeName, FileTypeNameConstants.PURCHASE_DATA_FILE)
                .eq(ProjectFile::getBuyItemCode, buyItemCode);
        List<ProjectFile> projectFiles = fileStorageInfoMapper.selectList(qw);

        if (projectFiles != null && !projectFiles.isEmpty()) {
            return R.fail(false, "已同步,不可重复同步");
        }
        for (FileDataVo fileDataVo : fileInfo.getData()) {
            // 本地文件复制
            String filePath = FileHelper.fileCopy(fileDataVo.getFileStoragePath());
            ProjectFile projectFile = new ProjectFile();
            BeanUtils.copyProperties(fileDataVo, projectFile);
            projectFile.setYearMonthSplit(FileHelper.getYearMonthSplit());
            projectFile.setCreateAt(DateTime.now());
            projectFile.setFileCode(FileHelper.getUuid());
            projectFile.setFileStoragePath(filePath);
            projectFile.setBuyItemCode(buyItemCode);

            fileStorageInfoMapper.insert(projectFile);
        }
        return R.ok(true);
    }
}

