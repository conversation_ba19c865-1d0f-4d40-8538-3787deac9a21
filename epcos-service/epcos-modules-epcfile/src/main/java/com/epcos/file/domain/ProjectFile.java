package com.epcos.file.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.common.core.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 采购文件存储对象 epcfile_project
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("epcfile_project")
public class ProjectFile extends FileData {
    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    private String buyItemCode;

    /**
     * 标段编号
     */
    @Excel(name = "标段编号")
    private String subpackageCode;

    /**
     * 供应商ID
     */
    @Excel(name = "供应商ID")
    private Long supplierNumber;

    /**
     * 文件状态(0:正常文件, 1:临时文件)
     */
    private Integer fileStatus;

    /**
     * 项目创建年月
     */
    @Excel(name = "项目创建年月")
    private String yearMonthSplit;

}
