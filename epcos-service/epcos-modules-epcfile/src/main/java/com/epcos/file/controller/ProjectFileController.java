package com.epcos.file.controller;

import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.epcfile.api.domain.vo.FileDataVo;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.file.common.file.FileHelper;
import com.epcos.file.domain.EnclosureFile;
import com.epcos.file.domain.ProjectFile;
import com.epcos.file.service.IEnclosureFileService;
import com.epcos.file.service.IFileService;
import com.epcos.file.service.IProjectFileService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.activation.MimetypesFileTypeMap;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.epcos.file.common.file.FileHelper.failToJson;


@Log4j2
@RestController
@AllArgsConstructor //自动生成构造函数
@RequestMapping("/projectFile")
public class ProjectFileController {
    private final IFileService fileService;
    private final IProjectFileService projectFileService;
    private final IEnclosureFileService enclosureFileService;
    final static ExecutorService threadPool = Executors.newCachedThreadPool();

    @Log(title = "项目内归档文件上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload")
    @ApiOperation("项目内归档文件上传")
    public R<SysFileVo> projectFiles(@RequestPart MultipartFile file,
                                     @RequestParam("yearMonthSplit") String yearMonthSplit,
                                     @RequestParam("buyItemCode") String buyItemCode,
                                     @RequestParam(value = "supplierNumber", required = false) String supplierNumber,
                                     @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                     @RequestParam("fileTypeName") String fileTypeName,
                                     @RequestParam(value = "fileStatus", required = false) Integer fileStatus) {
        String filepath = FileHelper.filePath(yearMonthSplit, buyItemCode, subpackageCode, supplierNumber, fileTypeName);
        // 上传并返回访问地址
        SysFileVo sysFileVo = fileService.projectFiles(filepath, file);
        // 生成 UUID
        String fileCode = FileHelper.getUuid();
        createFileStorageInfo(yearMonthSplit, buyItemCode, supplierNumber, fileCode, subpackageCode, fileTypeName, sysFileVo.getUrl(), fileStatus);
        sysFileVo.setUrl(fileCode);
        return R.ok(sysFileVo);
    }

    @Log(title = "项目内非归档文件上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadEnclosure")
    @ApiOperation("项目内非归档文件上传")
    public R<SysFileVo> projectEnclosureFile(@RequestPart(value = "file") MultipartFile file,
                                             @RequestParam(value = "yearMonthSplit", required = false) String yearMonthSplit,
                                             @RequestParam("buyItemCode") String buyItemCode,
                                             @RequestParam(value = "supplierNumber", required = false) String supplierNumber,
                                             @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                             @RequestParam("fileTypeName") String fileTypeName) {
        // 年月
        if (yearMonthSplit == null || yearMonthSplit.isEmpty()) {
            yearMonthSplit = FileHelper.getYearMonthSplit();
        }
        String filepath = FileHelper.filePath(yearMonthSplit, buyItemCode, subpackageCode, supplierNumber, fileTypeName);
        // 上传并返回访问地址
        SysFileVo sysFileVo = fileService.projectEnclosureFile(filepath, file);
        String fileCode = FileHelper.getUuid();
        createEnclosureFile(yearMonthSplit, buyItemCode, supplierNumber, fileCode, subpackageCode, fileTypeName, sysFileVo.getUrl());
        sysFileVo.setUrl(fileCode);
        return R.ok(sysFileVo);
    }


    // 暂无远程调用
    @GetMapping(value = "/fileArchivingListInfo")
    @ApiOperation(value = "所有项目归档文件列表", notes = "所有项目归档文件列表")
    public R<List<ProjectFile>> fileArchivingListInfo(@RequestParam("buyItemCode") String buyItemCode,
                                                      @RequestParam(value = "subpackageCode", required = false) String subpackageCode) {
        ProjectFile projectDocument = new ProjectFile();
        projectDocument.setBuyItemCode(buyItemCode);
        if (!ObjectUtils.isEmpty(subpackageCode)) {
            projectDocument.setSubpackageCode(subpackageCode);
        }
        List<ProjectFile> projectDocuments = projectFileService.selectFileList(projectDocument);
        return R.ok(projectDocuments);
    }

    @GetMapping(value = "/fileArchivingListByProject")
    @ApiOperation(value = "项目归档文件列表", notes = "项目归档文件列表")
    public R<List<ProjectFile>> fileArchivingListByProject(@RequestParam("buyItemCode") String buyItemCode) {
        ProjectFile projectDocument = new ProjectFile();
        projectDocument.setBuyItemCode(buyItemCode);
        List<ProjectFile> projectDocuments = projectFileService.fileArchivingListByProject(projectDocument);
        return R.ok(projectDocuments);
    }

    @GetMapping(value = "/fileArchivingListBySection")
    @ApiOperation(value = "按标段查项目归档文件列表", notes = "按标段查项目归档文件列表")
    public R<List<ProjectFile>> fileArchivingListBySection(@RequestParam("buyItemCode") String buyItemCode,
                                                           @RequestParam("subpackageCode") String subpackageCode) {
        ProjectFile projectDocument = new ProjectFile();
        projectDocument.setBuyItemCode(buyItemCode);
        projectDocument.setSubpackageCode(subpackageCode);
        List<ProjectFile> projectDocuments = projectFileService.fileArchivingListBySection(projectDocument);
        return R.ok(projectDocuments);
    }

    @ApiOperation("项目内归档和不归档文件信息表")
    @GetMapping("/fileInfo")
    public R<ProjectFile> fileInfo(@RequestParam("fileKey") String fileKey) {
        if (!StringUtils.hasText(fileKey)) {
            log.error("fileKey为空,fileKey={}", fileKey);
            return R.fail("fileKey为空");
        }
        // 根据文件file_code查询epcfile_project表
        ProjectFile projectDocument = projectFileService.selectFileStorageInfoByFileKey(fileKey.trim());
        if (ObjectUtils.isEmpty(projectDocument)) {
            EnclosureFile enclosureFile = enclosureFileService.selectEnclosureFileByFileKey(fileKey.trim());
            BeanUtils.copyProperties(enclosureFile, projectDocument);
            log.error("根据文件file_code查询epcfile_project表失败,fileKey={}", fileKey);
            return R.fail("根据文件file_code查询epcfile_project表失败");
        }
        return R.ok(projectDocument);
    }

    @Log(title = "项目下归档文件删除 ，对应epcosfiles上传接口", businessType = BusinessType.DELETE)
    @ApiOperation("项目下归档文件删除 ，对应epcosfiles上传接口")
    @GetMapping("/deleteFileKey")
    public R<Boolean> deleteFileKey(@RequestParam("fileKey") String fileKey) {
        return R.ok(projectFileService.deleteByFileKey(fileKey));
    }


    @Log(title = "项目下非归档文件删除", businessType = BusinessType.DELETE)
    @ApiOperation("项目下非归档文件删除")
    @GetMapping("/deleteEnclosureFileKey")
    public R<Boolean> deleteEnclosureFileKey(@RequestParam("fileKey") String fileKey) {
        return R.ok(enclosureFileService.deleteByFileKey(fileKey));
    }

    @Log(title = "项目内归档文件批量删除", businessType = BusinessType.DELETE)
    @ApiOperation("项目内归档文件批量删除")
    @GetMapping("/deleteBiddingFileList")
    R<Boolean> deleteBiddingFileList(@RequestParam(value = "buyItemCode", required = false) String buyItemCode,
                                     @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                     @RequestParam(value = "fileTypeName", required = false) String fileTypeName,
                                     @RequestParam(value = "supplierNumber", required = false) Long supplierNumber) {
        return R.ok(projectFileService.deleteBiddingFileList(buyItemCode, subpackageCode, fileTypeName, supplierNumber));
    }

    @Log(title = "删除项目所有文件", businessType = BusinessType.DELETE)
    @ApiOperation("删除项目所有文件")
    @GetMapping("/deleteProjectFileAll")
    R<Boolean> deleteProjectFileAll(@RequestParam("buyItemCode") String buyItemCode) {
        projectFileService.deleteProjectFileAll(buyItemCode);
        enclosureFileService.deleteProjectFileAll(buyItemCode);
        return R.ok(true);
    }

    @Log(title = "项目下不归档文件批量删除", businessType = BusinessType.DELETE)
    @ApiOperation("项目下不归档文件批量删除")
    @GetMapping("/deleteTenderDocFileList")
    R<Boolean> deleteTenderDocFileList(@RequestParam("buyItemCode") String buyItemCode,
                                       @RequestParam("supplierNumber") Long supplierNumber,
                                       @RequestParam("subpackageCode") String subpackageCode,
                                       @RequestParam("fileTypeName") String fileTypeName) {
        return R.ok(enclosureFileService.deleteTenderDocFileList(buyItemCode, subpackageCode, supplierNumber, fileTypeName));
    }

    @Log(title = "按包删除该包下面的所有归档文件", businessType = BusinessType.DELETE)
    @GetMapping("/deleteFileBySubpackageCode")
    @ApiOperation("按包删除该包下面的所有归档文件")
    public R<Boolean> deleteFileData(@RequestParam("subpackageCode") String subpackageCode) {
        threadPool.execute(() -> deleteFileByAll(subpackageCode));
        return R.ok(true, "删除成功");
    }


    /**
     * 归档文件打包
     *
     * @param fileKeyList 文件key集合
     * @param buyItemCode 项目code
     * @return
     */
    @Log(title = "归档文件打包", businessType = BusinessType.INSERT)
    @RequiresPermissions("project:archive:pack")
    @RedisLock(second = 60)
    @GetMapping("/projectFilesByzip")
    @ApiOperation("归档文件打包")
    public R<Boolean> projectFilesByzip(@RequestParam(value = "fileKeyList", required = false) List<String> fileKeyList,
                                        @RequestParam("buyItemCode") String buyItemCode,
                                        @RequestParam(value = "yearMonthSplit", required = false) String yearMonthSplit) {
        return projectFileService.projectFilesByzip(fileKeyList, buyItemCode, yearMonthSplit);
    }


    @RequiresPermissions("project:archive:pack")
    @ApiOperation("项目内归档文件批量下载")
    @GetMapping("/downloadFileList")
    public ResponseEntity<Resource> downloadFileList(@RequestParam("fileKeyList") List<String> fileKeyList, @RequestParam("buyItemCode") String buyItemCode) {
        return projectFileService.downloadFileList(fileKeyList, buyItemCode);
    }

    @ApiOperation("项目内归档文件下载 ，对应epcosfiles上传接口")
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(@RequestParam("fileKey") String fileKey) {
        return insideFile(fileKey);
    }

    @ApiOperation("项目下不归档文件下载 ，对应epcosenclosure上传接口")
    @GetMapping("/downloadEnclosure")
    public ResponseEntity<Resource> downloadEnclosure(@RequestParam("fileKey") String fileKey) {
        return insideFile(fileKey);
    }

    @ApiOperation("修改文件状态(将临时文件改为正常文件)")
    @GetMapping("/updateProjectFileStatus")
    R<Boolean> updateProjectFileStatus(@RequestParam("fileKeys") List<String> fileKeys) {
        return projectFileService.updateProjectFileStatus(fileKeys);
    }

    @ApiOperation("删除临时文件")
    @GetMapping("/delTemporaryFile")
    R<Boolean> delTemporaryFile(@RequestParam("buyItemCode") String buyItemCode,
                                @RequestParam(value = "subpackageCode", required = false) String subpackageCode,
                                @RequestParam("supplierNumber") Long supplierNumber) {
        return projectFileService.delTemporaryFile(buyItemCode, subpackageCode, supplierNumber);
    }


    @ApiOperation("非项目文件转移到项目中")
    @GetMapping("/noFileToProjectFile")
    R<Boolean> noFileToProjectFile(@RequestParam("fileKeyList") @NotNull List<String> fileKeyList,
                                   @RequestParam("buyItemCode") @NotBlank String buyItemCode) {
        return projectFileService.noFileToProjectFile(fileKeyList, buyItemCode);
    }


    void deleteFileByAll(String subpackageCode) {
        projectFileService.deleteByBidSectionCode(subpackageCode);
        enclosureFileService.deleteByBidSectionCode(subpackageCode);
    }

    private ResponseEntity<Resource> insideFile(String fileKey) {
        if (!StringUtils.hasText(fileKey)) {
            log.error("fileKey={},不存在", fileKey);
            return FileHelper.returnResource("fileKey不存在");
        }
        ProjectFile filedStorage = projectFileService.selectFileStorageInfoByFileKey(fileKey.trim());
        String filePath = null;
        if (!ObjectUtils.isEmpty(filedStorage)) {
            filePath = filedStorage.getFileStoragePath();
        }
        if (StringUtils.isEmpty(filePath)) {
            EnclosureFile notArchive = enclosureFileService.selectEnclosureFileByFileKey(fileKey.trim());
            if (!ObjectUtils.isEmpty(notArchive)) {
                filePath = notArchive.getFileStoragePath();
            }
        }
        return FileHelper.downloadFile(filePath);
    }


    private void createEnclosureFile(String yearMonthSplit, String buyItemCode, String supplierNumber, String fileCode, String subpackageCode, String fileTypeName, String storagePath) {
        EnclosureFile enclosureFile = new EnclosureFile();
        enclosureFile.setYearMonthSplit(yearMonthSplit);
        enclosureFile.setBuyItemCode(buyItemCode);
        if (StringUtils.hasText(supplierNumber)) {
            enclosureFile.setSupplierNumber(Long.parseLong(supplierNumber));
        }
        enclosureFile.setSubpackageCode(subpackageCode);
        enclosureFile.setFileTypeName(fileTypeName);
        enclosureFile.setFileStoragePath(storagePath);
        enclosureFile.setFileCode(fileCode);
        enclosureFile.setCreateAt(new Date());
        int i = enclosureFileService.insertEnclosureFile(enclosureFile);
        if (i < 1) {
            log.error("项目非归档文件,文件信息添加数据库失败,buyItemCode={},subpackageCode={},fileCode={}", buyItemCode, subpackageCode, fileCode);
            throw new ServiceException("文件信息添加数据库失败");
        }
    }

    private void createFileStorageInfo(String yearMonthSplit, String buyItemCode, String supplierNumber, String fileCode, String subpackageCode, String fileTypeName, String storagePath, Integer fileStatus) {
        ProjectFile projectFile = new ProjectFile();
        projectFile.setYearMonthSplit(yearMonthSplit);
        projectFile.setBuyItemCode(buyItemCode);
        if (StringUtils.hasText(supplierNumber)) {
            projectFile.setSupplierNumber(Long.parseLong(supplierNumber));
        }
        projectFile.setSubpackageCode(subpackageCode);
        projectFile.setFileTypeName(fileTypeName);
        projectFile.setFileStoragePath(storagePath);
        projectFile.setFileCode(fileCode);
        projectFile.setCreateAt(new Date());
        projectFile.setFileStatus(fileStatus);
        int i = projectFileService.insertFileStorageInfo(projectFile);
        if (i < 1) {
            log.error("项目归档文件,文件信息添加数据库失败,buyItemCode={},subpackageCode={},fileCode={}", buyItemCode, subpackageCode, fileCode);
            throw new ServiceException("文件信息添加数据库失败");
        }
    }
}
