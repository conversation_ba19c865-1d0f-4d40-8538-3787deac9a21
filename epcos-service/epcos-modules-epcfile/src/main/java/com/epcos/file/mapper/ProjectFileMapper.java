package com.epcos.file.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.file.domain.ProjectFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购文件存储Mapper接口
 */
@Mapper
public interface ProjectFileMapper extends BaseMapper<ProjectFile> {

    /**
     * 根据文件key 查询文件路径
     *
     * @param fileKey 采购文件存储主键
     * @return 采购文件存储
     */
    ProjectFile selByFileKey(String fileKey);
    /**
     * 查询采购文件存储列表
     *
     * @param projectDocument 采购文件存储
     * @return 采购文件存储集合
     */
    List<ProjectFile> selectFileStorageInfoList(ProjectFile projectDocument);

    /**
     * 归档文件查询
     * @param projectDocument
     * @return
     */
    List<ProjectFile> selectFileList(ProjectFile projectDocument);

    List<ProjectFile> selectProjectFileAll(ProjectFile projectDocument);

    List<ProjectFile> fileArchivingListByProject(ProjectFile projectDocument);

    List<ProjectFile> fileArchivingListBySection(ProjectFile projectDocument);

    /**
     * 根据项目编码和创建项目年月(year_month_split)查询epcfile_project表
     * @param projectDocument
     * @return
     */
    List<ProjectFile> selectByProjectFileList(ProjectFile projectDocument);
    /**
     * 新增采购文件存储
     * @param projectDocument 采购文件存储
     * @return 结果
     */
    int insertFileStorageInfo(ProjectFile projectDocument);

    /**
     * 修改文件存储路径存储
     * @param projectDocument 采购文件存储
     * @return 结果
     */
    int upFileStoragePath(ProjectFile projectDocument);

    /**
     * 删除采购文件存储信息
     * @param fileCode 采购文件存储key
     * @return 结果
     */
    int deleteByFileKey(String fileCode);

    List<ProjectFile> selectByBidSectionCode(String subpackageCode);

    int deleteFileBysubpackageCode(String subpackageCode);

    /**
     * 查询采购文件存储
     *
     * @param id 采购文件存储主键
     * @return 采购文件存储
     */
    ProjectFile selectFileStorageInfoById(Long id);


    List<ProjectFile> selectByzipFileStorage(String fileStoragePath);

    /**
     * 修改采购文件存储
     *
     * @param projectDocument 采购文件存储
     * @return 结果
     */
    int updateFileStorageInfo(ProjectFile projectDocument);

    /**
     * 删除采购文件存储
     *
     * @param id 采购文件存储主键
     * @return 结果
     */
    int deleteFileStorageInfoById(Long id);

    /**
     * 批量删除采购文件存储
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteFileStorageInfoByIds(Long[] ids);

    /**
     * 根据fileKey批量查询
     * @param fileKeyList
     * @return
     */
    List<ProjectFile> selectByFileKeys(@Param("fileKeyList") List<String> fileKeyList);

    /**
     * 删除旧的压缩文件存储地址
     * @param buyItemCode
     * @param fileType
     * @return
     */
    List<ProjectFile> selFileZip(@Param("buyItemCode") String buyItemCode,@Param("fileType") String fileType);

    /**
     * 删除旧的压缩文件存储地址
     * @param buyItemCode
     * @param fileType
     * @return
     */
    int deleteFileZip(@Param("buyItemCode") String buyItemCode,@Param("fileType") String fileType);
}
