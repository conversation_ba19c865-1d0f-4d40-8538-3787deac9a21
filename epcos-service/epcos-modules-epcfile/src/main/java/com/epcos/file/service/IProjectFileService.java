package com.epcos.file.service;

import com.epcos.common.core.domain.R;
import com.epcos.epcfile.api.domain.vo.FileDataVo;
import com.epcos.file.domain.ProjectFile;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.util.List;

/**
 * 采购文件存储Service接口
 *
 * <AUTHOR>
 */
public interface IProjectFileService {

    /**
     * 根据文件file_code查询epcfile_project表
     *
     * @param fileKey
     * @return
     */
    ProjectFile selectFileStorageInfoByFileKey(String fileKey);


    /**
     * 所有项目归档文件列表
     *
     * @param projectDocument
     * @return
     */
    List<ProjectFile> selectFileList(ProjectFile projectDocument);


//    /**
//     * 根据项目编码和创建项目年月(year_month_split)查询epcfile_project表,组装map数据返回
//     * @param projectDocument
//     * @return
//     */
//    Map<String,String> selectByProjectFileList(ProjectFile projectDocument);

    /**
     * 新增采购文件存储
     *
     * @param projectDocument
     * @return
     */
    int insertFileStorageInfo(ProjectFile projectDocument);

    /**
     * 删除采购文件存储信息
     */
    Boolean deleteByFileKey(String fileCode);


    /**
     * 项目内归档文件批量删除
     *
     * @param buyItemCode
     * @param subpackageCode
     * @param fileTypeName
     * @param supplierNumber
     * @return
     */
    Boolean deleteBiddingFileList(String buyItemCode, String subpackageCode, String fileTypeName, Long supplierNumber);

    Boolean deleteProjectFileAll(String buyItemCode);

    void deleteByBidSectionCode(String subpackageCode);

    /**
     * 项目归档文件列表
     *
     * @param projectDocument
     * @return
     */
    List<ProjectFile> fileArchivingListByProject(ProjectFile projectDocument);

    /**
     * 按标段查项目归档文件列表
     *
     * @param projectDocument
     * @return
     */
    List<ProjectFile> fileArchivingListBySection(ProjectFile projectDocument);


    /**
     * 数据库中修改文件存储路径
     *
     * @param fileKey         文件key
     * @param fileStoragePath 文件路径
     * @return
     */
    R<Boolean> updateProjectFilePath(String fileKey, String fileStoragePath);

    /**
     * 归档文件打包
     *
     * @param fileKeyList    文件key集合
     * @param buyItemCode    项目code
     * @param yearMonthSplit
     * @return
     */
    R<Boolean> projectFilesByzip(List<String> fileKeyList, String buyItemCode, String yearMonthSplit);

    /**
     * 项目内归档文件批量下载
     *
     * @param fileKeyList
     * @return
     */
    ResponseEntity<Resource> downloadFileList(List<String> fileKeyList, String buyItemCode);

    /**
     * 异步调用删除物理文件方法
     *
     * @param filePath 文件路径
     */
    void delFile(String filePath);

    /**
     * 修改文件状态(将临时文件改为正常文件)
     *
     * @param fileKeys
     * @return
     */
    R<Boolean> updateProjectFileStatus(List<String> fileKeys);

    /**
     * 删除临时文件
     *
     * @param buyItemCode
     * @param subpackageCode
     * @param supplierNumber
     * @return
     */
    R<Boolean> delTemporaryFile(String buyItemCode, String subpackageCode, Long supplierNumber);


    /**
     * 非项目文件转移到项目中
     *
     * @param fileKeyList
     * @return
     */
    R<Boolean> noFileToProjectFile(List<String> fileKeyList, String buyItemCode);
}
