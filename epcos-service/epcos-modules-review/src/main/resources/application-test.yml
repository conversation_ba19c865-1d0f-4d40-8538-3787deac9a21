nacos:
  server-addr: ************:8848
  namespace: 516b92c8-098c-4ddd-b61e-909cccad3b5a
  username: test
  password: test

redis:
  host: ************
  password:
  port: 6379
  database: 2
  timeout: 5000

mysql:
  url: jdbc:mysql://************:3307/epcos-file-review?characterEncoding=UTF8&autoReconnect=true&serverTimezone=UTC
  username: root
  password: root123

spring:
  datasource:
    druid:
      initial-size: 2
      min-idle: 1
      max-active: 2
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true

swagger:
  enabled: true

environment: ws

---
spring:
  config:
    activate:
      on-profile: polarx
mysql:
  url: *******************************************************************************************************************************************************************************************************************************************************************************
  username: root
  password: root123456
