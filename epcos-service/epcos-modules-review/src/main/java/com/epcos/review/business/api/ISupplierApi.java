package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.review.domain.dao.SupplierDao;
import com.epcos.review.domain.dto.SupplierRankDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface ISupplierApi extends IService<SupplierDao> {


    Boolean eliminateSupplier(String subpackageCode, Long supplierId);

    /**
     * 添加或修改 review_supplier表数据
     *
     * @param dao
     * @return
     */
    Boolean saveUpdateSupplierDao(SupplierDao dao);


    /**
     * 根据标段code删除review_supplier表
     *
     * @param subpackageCode
     * @return
     */
    Boolean deleteSupplier(String subpackageCode);


    /**
     * 查询供应商列表
     *
     * @param subpackageCode
     * @param reviewScore
     * @return
     */
    List<SupplierDao> getSupplierList(String subpackageCode, Integer reviewScore);

    /**
     * 修改review_supplier表(修改供应商排名)
     *
     * @param ranks          供应商排名列表
     * @param subpackageCode 标段code
     * @return
     */
    Boolean setSupplierRank(List<SupplierRankDto> ranks, String subpackageCode);

    /**
     * 根据标段code,供应商id查询review_supplier表,查询到该供应商的评委推荐名次并返回
     *
     * @param subpackageCode
     * @param supplierId
     * @return
     */
    Integer getRank(String subpackageCode, Long supplierId);


    SupplierDao queryInfo(String subpackageCode, Long supplierId);
}
