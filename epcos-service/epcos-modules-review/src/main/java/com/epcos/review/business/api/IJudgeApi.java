package com.epcos.review.business.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.epcos.common.core.domain.R;
import com.epcos.review.domain.bo.ReportUserBo;
import com.epcos.review.domain.dao.JudgeDao;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface IJudgeApi extends IService<JudgeDao> {
   /**
    * review_judges表，设置组长
    * @param subpackageCode 标段
    * @param judgesId       评委ID
    * @return status
    */
   Boolean setJudgesLeader(String subpackageCode, Long judgesId);



   /**
    * 添加或修改review_judges表数据
    * @param dao
    * @return
    */
   Boolean addJudgesDao(JudgeDao dao);


   /**
    * 根据标段code查询review_judges表
    * @param subpackageCode
    * @return
    */
   List<JudgeDao> judgesDtoList(String subpackageCode);


   /**
    * 根据标段code删除review_judges表
    * @param subpackageCode
    * @return
    */
   Boolean deleteJudges(String subpackageCode);


   /**
    * 根据标段code查询review_judges表列表,封装ReportUserBo列表并返回
    * @param subpackageCode
    * @return
    */
   List<ReportUserBo> findReportUserBo(String subpackageCode);

   /**
    * 设置签字状态
    * @param subpackageCode 标段
    * @param judgesId       评委ID
    * @return status
    */
   Boolean setJudgeSign(String subpackageCode, Long judgesId);

   /**
    * 根据标段code和评委id，查询review_judges表单条数据
    * @param subpackageCode
    * @param judgeId
    * @return
    */
   JudgeDao judgeOne(String subpackageCode, Long judgeId);

   /**
    * 查询标段下的组长
    * @param subpackageCode 标段code
    * @return
    */
   JudgeDao judgeLeader(String subpackageCode);


}
