package com.epcos.review.domain.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class ReviewReportDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 包编码
     */
    @ApiModelProperty("包编码")
    @NotBlank(message = "包编码不允许为空")
    private String subpackageCode;

    /**
     * 评审报告html
     */
    @ApiModelProperty("评审报告html")
    @NotBlank(message = "评审报告html不允许为空")
    private String reviewReportHtml;


}
