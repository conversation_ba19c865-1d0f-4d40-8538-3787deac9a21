package com.epcos.review.controller;

import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.InnerAuth;
import com.epcos.review.business.api.ISubpackageApi;
import com.epcos.review.business.api.InitDataApi;
import com.epcos.review.domain.dao.SubpackageDao;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "评审准备与重新评审")
@Slf4j
@RestController
@RequestMapping("/init")
@RequiredArgsConstructor
public class InitDateController {

    private final InitDataApi initReviewImpl;
    private final ISubpackageApi subpackageImpl;


    @InnerAuth
    @Log(title = "初始化评审模块数据", businessType = BusinessType.OTHER)
    @ApiOperation("初始化评审模块数据")
    @GetMapping("/reviewData")
    public R<Boolean> reviewData(@RequestParam("subpackageCode") String subpackageCode,
                                 @RequestParam("isBargaining") Integer isBargaining,
                                 @RequestParam(value = "delegateId", required = false) Long delegateId) {
        // 根据标段code查询review_subpackage表单条数据
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        // 为空
        if (ObjectUtils.isEmpty(subpackage)) {
            return R.ok(initReviewImpl.initReview(subpackageCode, isBargaining, delegateId));
        }
        log.error("确认评委会签失败,subpackageCode={},isBargaining={},delegateId={}", subpackageCode, isBargaining, delegateId);
        return R.fail("确认评委会签失败");
    }

    @Log(title = "采购人确认启动评审", businessType = BusinessType.OTHER)
    @ApiOperation("【采购人确认启动评审】")
    @GetMapping("/isReview")
    public R<Boolean> isReview(@RequestParam("subpackageCodes") String subpackageCode, @RequestParam("isReview") Integer isReview) {
        SubpackageDao dao = new SubpackageDao();
        dao.setIsReview(isReview);
        dao.setSubpackageCode(subpackageCode);

        // 修改标段信息(review_subpackage表)
        Boolean status = subpackageImpl.updateSubpackageDao(dao);
        if (status) {
            return R.ok();
        }
        log.error("采购人确认评审失败,subpackageCode={},isReview={}", subpackageCode, isReview);
        return R.fail("采购人确认评审失败");
    }

    @Log(title = "重新评审", businessType = BusinessType.OTHER)
    @ApiOperation("【重新评审】")
    @GetMapping("/deleteReviewData")
    public R<Boolean> delete(@RequestParam("subpackageCodes") String subpackageCode) {
        // 删除标段相关表数据
        Boolean status = initReviewImpl.deleteReview(subpackageCode);
        if (status) {
            return R.ok();
        }
        log.error("重新评审失败,subpackageCode={}", subpackageCode);
        return R.fail("重新评审失败");
    }


    @Log(title = "采购人确认组长", businessType = BusinessType.OTHER)
    @ApiOperation("【采购人确认组长】")
    @GetMapping("/isLeader")
    public R<Boolean> updateLeader(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("judgeId") Long judgeId) {
        // 采购人确认组长，修改review_leader表、review_judges表、review_subpackage表
        Boolean status = initReviewImpl.setLeader(subpackageCode, judgeId);
        if (!ObjectUtils.isEmpty(status) && status) {
            return R.ok(true, "采购人确认组长成功");
        }
        log.error("采购人确认组长失败,subpackageCode={},judgeId={}", subpackageCode, judgeId);
        return R.fail("采购人确认组长失败");
    }


    @Log(title = "确认评审结果-生成评审报告", businessType = BusinessType.INSERT)
    @ApiOperation("【确认评审结果-生成评审报告】")
    @GetMapping("/confirm")
    public R<Boolean> operatorConfirm(@RequestParam("subpackageCode") String subpackageCode) {
        // 根据标段code查询review_subpackage表单条数据
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        if (subpackage.getRemarksBidRejection() == null || subpackage.getRemarksBidRejection().isEmpty()) {
            return R.fail("评委组长未作评审说明，请组长作完评审说明后再确认评审结果！");
        }
        // 生成评标报告
        initReviewImpl.createReport(subpackageCode);
        return R.ok(true);
    }

    @Log(title = "重新生成评审报告", businessType = BusinessType.INSERT)
    @ApiOperation("重新生成评审报告并盖章")
    @GetMapping("/againConfirm")
    public R<Boolean> againConfirm(@RequestParam("subpackageCode") String subpackageCode) {
        return initReviewImpl.againConfirm(subpackageCode);
    }

}
