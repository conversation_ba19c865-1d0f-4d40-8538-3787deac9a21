package com.epcos.review.domain.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class ChoiceLeaderDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 包编码
     */
    @ApiModelProperty("包编码")
    @NotBlank(message = "包编码不允许为空")
    private String subpackageCode;
    /**
     *  投票专家ID
     */
    @ApiModelProperty("投票专家ID")
    @NotNull(message = "投票专家ID不允许为空")
    private Long  judgeId  ;
    /**
     * 投票专家姓名
     */
    @ApiModelProperty("投票专家姓名")
    @NotBlank(message = "投票专家姓名不允许为空")
    private String judgeName ;
    /**
     * 被提名组长的专家ID
     */
    @ApiModelProperty("被提名组长的专家ID")
    @NotNull(message = "被提名组长的专家ID不允许为空")
    private Long  judgeLeaderId;
    /**
     * 被提名组长的的专家姓名
     */
    @ApiModelProperty("被提名组长的的专家姓名")
    @NotBlank(message = "被提名组长的的专家姓名不允许为空")
    private String  judgeLeaderName ;

    @ApiModelProperty("评委类型：1-科室代表, 0-非代表")
    private Integer delegate;

}
