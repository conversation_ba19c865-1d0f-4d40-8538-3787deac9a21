package com.epcos.review.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class ProcessNodesBo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("流程节点中文名称")
    private String  nodesName;
    @ApiModelProperty("流程节点英文名称")
    private String  nodesType;
    @ApiModelProperty("流程节点是否显示")
    private Integer isShow;
    @ApiModelProperty("流程节点中是否有下一步按钮")
    private Integer nextStep;

    public ProcessNodesBo() {
    }

    public ProcessNodesBo(String nodesName, String nodesType, Integer isShow, Integer nextStep) {
        this.nodesName = nodesName;
        this.nodesType = nodesType;
        this.isShow = isShow;
        this.nextStep= nextStep;
    }
}
