package com.epcos.review.domain.dto;

import com.epcos.common.core.domain.review.ReviewDetailsVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */

@Data
public class SubmitResultDto implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty("评审类型,必填 ：0 = 合格制 1 = 打分制 ")
    @NotNull(message = "评审类型,必填 ：0 = 合格制 1 = 打分制")
    private Integer isType;

    @ApiModelProperty("标段code")
    @NotBlank(message = "标段code不允许为空")
    private String subpackageCode;

    @ApiModelProperty("评委Id")
    @NotNull(message = "评委Id不允许为空")
    private Long judgeId;

    @ApiModelProperty("评委姓名")
    @NotBlank(message = "评委姓名不允许为空")
    private String judgeName;

    @ApiModelProperty("供应商单位名称")
    @NotBlank(message = "供应商单位名称不允许为空")
    private String supplierCompanyName;

    @ApiModelProperty("供应商ID")
    @NotNull(message = "供应商ID不允许为空")
    private Long supplierId;

    @ApiModelProperty("评审结果")
    @NotEmpty(message = "评审结果不允许为空")
    private List<ReviewDetailsVo> resultBoList;
}
