package com.epcos.review.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.review.domain.dao.BaseTableDao;
import com.epcos.review.domain.dao.RecordDao;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecordVo extends RecordDao {

    /**
     * 投票数量/得分
     */
    private Double recordNum;
}
