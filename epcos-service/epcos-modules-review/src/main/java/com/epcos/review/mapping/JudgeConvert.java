package com.epcos.review.mapping;

import com.epcos.review.domain.dao.JudgeDao;
import com.epcos.review.domain.dto.ChoiceLeaderDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/9/3 14:01
 */
@Mapper
public interface JudgeConvert {

    JudgeConvert INSTANCE = Mappers.getMapper(JudgeConvert.class);

    ChoiceLeaderDto convert(JudgeDao judgeDao);

}
