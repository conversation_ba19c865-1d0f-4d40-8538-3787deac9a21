package com.epcos.review.business.api;

import com.epcos.common.core.domain.R;

/**
 * <AUTHOR>
 * @version V1.0
 */
public interface InitDataApi {


    /**
     * 初始化 评审数据
     * @param subpackageCode
     * @param isBargaining
     * @param delegateId
     * @return
     */
    Boolean initReview(String subpackageCode, Integer isBargaining , Long delegateId);


    /**
     * 重新评审
     * @param subpackageCode
     * @return
     */
    Boolean deleteReview(String subpackageCode);

    /**
     * 初始化评委数据和评委选拔组长数据（review_judges表、review_leader表）
     * @param subpackageCode
     * @param delegateId
     * @return
     */
    Boolean saveInitJudge(String subpackageCode , Long delegateId );


    /**
     * 采购人确认组长，修改review_leader表、review_judges表、review_subpackage表
     * @param subpackageCode
     * @param judgeId
     * @return
     */
    Boolean setLeader(String subpackageCode, Long judgeId);

    /**
     * 生成评标报告
     *
     * @param subpackageCode
     * @return
     */
    String createReport(String subpackageCode);

    /**
     * 重新生成评审报告并盖章
     * @param subpackageCode
     * @return
     */
    R<Boolean> againConfirm(String subpackageCode);
}
