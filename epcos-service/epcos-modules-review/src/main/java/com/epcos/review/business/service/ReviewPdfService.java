package com.epcos.review.business.service;

import cn.hutool.core.collection.CollectionUtil;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.review.business.api.IReviewApi;
import com.epcos.review.business.api.ISupplierApi;
import com.epcos.review.domain.dao.SupplierDao;
import com.epcos.review.domain.vo.SupplierItemVo;
import com.epcos.review.export.pdf.vo.ReviewDetailPdfVo;
import com.epcos.review.export.pdf.vo.RspComparePdfVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 9:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewPdfService {

    private final IReviewApi reviewApi;
    private final ISupplierApi supplierApi;

    /**
     * 查询导出响应对照表的数据信息
     *
     * @param type
     * @param supplierId
     * @param subpackageCode
     * @param judgeId
     */
    public RspComparePdfVo queryRspCompareInfo(Integer type, Long supplierId, String subpackageCode, Long judgeId) {

        List<SupplierItemVo> supplierItemVos = reviewApi.queryReviewDetails(type, judgeId, supplierId, subpackageCode);
        if (CollectionUtil.isEmpty(supplierItemVos)) {
            throw new ServiceException("没有查询到数据");
        }
        SupplierDao supplierDao = supplierApi.queryInfo(subpackageCode, supplierId);

        RspComparePdfVo vo = new RspComparePdfVo();
        vo.setSupplierCompanyName(supplierDao.getSupplierCompanyName());
        List<ReviewDetailPdfVo> pdfVoList = supplierItemVos.stream()
                .map(item -> {
                    ReviewDetailPdfVo pdfVo = new ReviewDetailPdfVo();
                    BeanUtils.copyProperties(item, pdfVo);
                    pdfVo.setScoreChapter(item.getScoreChapter().stream().collect(Collectors.joining("、")));
                    pdfVo.setReviewScore(String.valueOf(item.getReviewScore()));
                    return pdfVo;
                }).collect(Collectors.toList());
        vo.setReviewDetailPdfVoList(pdfVoList);
        return vo;
    }
}
