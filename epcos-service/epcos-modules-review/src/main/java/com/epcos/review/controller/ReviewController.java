package com.epcos.review.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.epcos.bidding.purchase.api.RemotePurchaserService;
import com.epcos.bidding.purchase.api.domian.reprot.ReportInfoVo;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.supplier.api.RemoteSupplierService;
import com.epcos.bidding.supplier.api.params.AnswerAttVo;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.*;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.ServletUtils;
import com.epcos.common.core.utils.bean.BeanUtils;
import com.epcos.common.core.web.domain.user.LoginUser;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.common.security.service.TokenService;
import com.epcos.epcfile.api.domain.vo.ProjectDocumentVo;
import com.epcos.epcfile.api.domain.vo.SysFileVo;
import com.epcos.review.business.api.*;
import com.epcos.review.domain.bo.ProcessNodesBo;
import com.epcos.review.domain.dao.JudgeDao;
import com.epcos.review.domain.dao.RecordDao;
import com.epcos.review.domain.dao.SubpackageDao;
import com.epcos.review.domain.dao.SupplierDao;
import com.epcos.review.domain.dto.RemarksRankDto;
import com.epcos.review.domain.dto.SubmitResultDto;
import com.epcos.review.domain.vo.*;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import com.epcos.system.api.model.FUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.log.enums.BusinessType.INSERT;
import static org.springframework.util.ObjectUtils.isEmpty;

@Api(tags = "评委评审")
@RestController
@RequestMapping("/review")
@RequiredArgsConstructor
@Slf4j
public class ReviewController {
    private static final int zero = 0;
    private static final int one = 1;
    private static final int three = 3;
    private final IClaimsApi claimsImpl;
    private final ISupplierChapterApi supplierChapterImpl;
    private final IRecordApi recordImpl;
    private final ISupplierApi supplierImpl;
    private final ISubpackageApi subpackageImpl;
    private final TokenService tokenService;
    private final IJudgeApi judgeApi;
    private final IReviewApi reviewApi;
    private final RemotePurchaserService remotePurchaserService;
    private final RemoteSupplierService remoteSupplierService;


    // ================================================    龙岗特有接口    ================================================

    @ApiOperation("专家获取标段文件")
    @GetMapping("/getProjectFileBySubCode")
    public R<List<SysFileVo>> getProjectFileBySubCode(@RequestParam("buyItemCode") String buyItemCode, @RequestParam("subpackageCode") String subpackageCode) {
        List<ProjectDocumentVo> projectFileBySubCode = FUtil.getProjectFileBySubCode(buyItemCode, subpackageCode);
        List<SysFileVo> sysFileVoList = new ArrayList<>();
        for (ProjectDocumentVo projectDocumentVo : projectFileBySubCode) {
            SysFileVo sysFileVo = new SysFileVo();
            sysFileVo.setName(projectDocumentVo.getFileStoragePath());
            sysFileVo.setUrl(projectDocumentVo.getFileCode());
            sysFileVoList.add(sysFileVo);
        }
        return R.ok(sysFileVoList);
    }


    @ApiOperation("专家端获取投票列表")
    @GetMapping("/getSupListToRecord")
    public R<List<SupplierVo>> getSupListToRecord(@RequestParam("subpackageCode") String subpackageCode) {
        List<SupplierDao> supplierList = supplierImpl.getSupplierList(subpackageCode, 1);
        if (supplierList == null || supplierList.isEmpty()) {
            return R.ok(null, "暂无列表");
        }
        List<SupplierVo> supplierVoList = supplierList.stream().map(supplierDao -> {
            SupplierVo supplierVo = new SupplierVo();
            Boolean record = recordImpl.isRecord(subpackageCode, SecurityUtils.getUserId(), supplierDao.getSupplierId());
            BeanUtils.copyProperties(supplierDao, supplierVo);
            supplierVo.setWhetherRecord(record);
            return supplierVo;
        }).collect(Collectors.toList());
        return R.ok(supplierVoList);
    }

    @Log(title = "专家端投票", businessType = BusinessType.OTHER)
    @RedisLock
    @ApiOperation("专家端投票")
    @GetMapping("/vote")
    public R<Boolean> vote(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("supplierId") Long supplierId) {
        Boolean record = recordImpl.vote(subpackageCode, SecurityUtils.getUserId(), supplierId);
        return R.ok(record);
    }

    @ApiOperation("专家端获取结果列表")
    @GetMapping("/getSupListToEnd")
    public R<ReviewSummaryVo> getSupListToEnd(@RequestParam("subpackageCode") String subpackageCode) {
        SubpackageDao subpackageDao = subpackageImpl.getOne(subpackageCode);
        if (subpackageDao == null) {
            log.error("1根据标段code获取review_subpackage表数据失败={}", subpackageCode);
            return R.fail(null, "结果为空");
        }
        List<SupplierDao> supplierList = supplierImpl.getSupplierList(subpackageCode, 1);
        if (supplierList == null || supplierList.isEmpty()) {
            return R.ok(null, "暂无列表");
        }
        List<SupplierDetailsVo> supplierDetailsVoList = new ArrayList<>();
        for (SupplierDao supplierDao : supplierList) {
            SupplierDetailsVo supplierDetailsVo = recordImpl.getVoters(subpackageCode, supplierDao.getSupplierId());
            BeanUtils.copyProperties(supplierDao, supplierDetailsVo);
            supplierDetailsVo.setRank(supplierDao.getJudgeRanking());
            supplierDetailsVo.setIsType(subpackageDao.getReviewMethod());
            supplierDetailsVoList.add(supplierDetailsVo);
        }
        ReviewSummaryVo rsv = new ReviewSummaryVo();
        rsv.setJudgesSign(toSignVo(subpackageDao.getReportFileKey(), subpackageCode));
        rsv.setSupplierDetailsVoList(supplierDetailsVoList);
        rsv.setReviewMethod(subpackageDao.getReviewMethod());
        rsv.setSubpackageCode(subpackageDao.getSubpackageCode());
        return R.ok(rsv);
    }

    // ================================================    龙岗特有接口    ================================================

    @ApiOperation("专家评审时查询投标附件")
    @GetMapping("/answer/att")
    public R<List<AnswerAttVo>> getAtt(@RequestParam("subpackageCode") String subpackageCode,
                                       @RequestParam("supplierId") Long supplierId) {
        R<List<AnswerAttVo>> r = remoteSupplierService.getAtt(SecurityConstants.INNER, subpackageCode, supplierId);
        if (r.hasFail()) {
            log.error("调用接口查询投标附件失败,subpackageCode={},supplierId={},r={}", subpackageCode, supplierId, r);
            return R.fail(r.getMsg());
        }
        return r;
    }


    @ApiOperation("【评审项目列表】")
    @GetMapping("/subpackageList")
    public R<Page<SubpackageVo>> reviewConformity(@RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum) {
        // 登录校验
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        // 根据评审人id,分页查询review_subpackage列表
        Page<SubpackageVo> subpackageVoPage = subpackageImpl.querySubpackageList(loginUser.getUserId(), pageNum, pageSize);
        return R.ok(subpackageVoPage);
    }


    @ApiOperation("【监标人监标项目列表】")
    @GetMapping("/monitorBid")
    public R<Page<SubpackageVo>> monitorBidList(@RequestParam("pageSize") Integer pageSize, @RequestParam("pageNum") Integer pageNum) {
        // 登录校验
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        // 根据监督人id,分页查询review_subpackage表数据
        Page<SubpackageVo> subpackageVoPage = subpackageImpl.monitorBidList(loginUser.getUserId(), pageNum, pageSize);
        return R.ok(subpackageVoPage);
    }

    @ApiOperation("【评审功能】")
    @GetMapping("/processNodesList")
    public R<List<ProcessNodesBo>> processNodesList(@RequestParam("subpackageCode") String subpackageCode) {
        // 查询是否确认评审
        R<Boolean> booleanR = remotePurchaserService.queryWhetherConfirmReview(SecurityConstants.INNER, subpackageCode);
        if (booleanR.hasFail()) {
            log.error("调用other/to/queryWhetherConfirmReview接口异常,subpackageCode={}", subpackageCode);
        }
        R<BuyItemVo> r = remotePurchaserService.queryBuyItemInfo(SecurityConstants.INNER, null, subpackageCode, null);
        if (r.hasFail()) {
            log.error("调用other/to/queryBuyItemInfo接口异常,subpackageCode={}", subpackageCode);
            return R.fail(r.getMsg());
        }
        Set<String> fvKeySet = r.getData().getFunctionKVList()
                .stream().map(FunctionKV::getPurchaseFunctionKey)
                .collect(Collectors.toSet());
        if (!isEmpty(booleanR) && booleanR.getData()) {
            // 根据标段code查询review_subpackage表单条数据
            SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
            if (subpackage.getIsReview() != one) {
                return R.fail("采购人尚未同意评审");
            }
            List<ProcessNodesBo> processNodesBoList = new ArrayList<>();
            switch (subpackage.getReviewMethod()) {
                case 0:
                    processNodesBoList = subpackageImpl.examineNode(fvKeySet);
                    break;
                case 1:
                    processNodesBoList = JSONArray.parseArray(subpackage.getPurchaseFunctionJson(), ProcessNodesBo.class);
//                    processNodesBoList = subpackageImpl.reviewNode();
                    break;
                case 2:
                    processNodesBoList = subpackageImpl.voteNode();
                    break;
            }
            return R.ok(processNodesBoList);
        }
        return R.fail("采购未确认评审");
    }


    @Log(title = "组长设置调研方式", businessType = BusinessType.OTHER)
    @ApiOperation("【组长设置调研方式】")
    @GetMapping("/examineReview")
    public R<Boolean> setExamineReviewMethod(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("reviewMethod") Integer reviewMethod) {
        // 登录校验
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        if (loginUser.getUserId().longValue() != subpackageImpl.getOne(subpackageCode).getTeamLeaderId()) {
            return R.fail("非组长没有设置权限 ！");
        }
        Boolean flag = subpackageImpl.setExamineReviewMethod(subpackageCode, reviewMethod);
        if (flag) {
            return R.ok(true);
        }
        log.error("组长设置调研方式失败,subpackageCode={}", subpackageCode);
        return R.fail();
    }


    @ApiOperation("【待评审供应商列表】")
    @GetMapping("/supplierList")
    public R<List<ReviewedSupplierVo>> supplierList(@RequestParam("subpackageCode") String subpackageCode) {
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        if (subpackage.getIsReview() != one) {
            return R.fail("采购人尚未同意评审");
        }
        List<SupplierDao> supplierList = supplierImpl.getSupplierList(subpackageCode, zero);
        List<ReviewedSupplierVo> voList = new ArrayList<>();
        for (SupplierDao dao : supplierList) {
            ReviewedSupplierVo vo = new ReviewedSupplierVo();
            BeanUtils.copyProperties(dao, vo);
            voList.add(vo);
        }
        return R.ok(voList);
    }


    @ApiOperation(value = "查询响应文件")
    @RequiresPermissions("expert:process:query")
    @GetMapping("/respFile")
    public R<EpcFileContentVo> queryRespFile(@RequestParam("subpackageCode") String subpackageCode,
                                             @RequestParam("supplierId") Long supplierId) {
        R<EpcFileContentVo> r = remoteSupplierService.queryAllBySupplierId(SecurityConstants.INNER, subpackageCode, supplierId);
        if (r.hasFail()) {
            log.error("调用queryAllBySupplierId接口异常,subpackageCode={},supplierId={},r:{}", subpackageCode, supplierId, r);
            return R.ok();
        }
        return r;
    }


    @Log(title = "提交评审", businessType = INSERT)
    @RedisLock
    @ApiOperation("【提交评审】")
    @PostMapping("/submit")
    public R<Boolean> judgesSubmitResult(@RequestBody @Valid SubmitResultDto submitResultDto) {

        SubpackageDao subpackage = subpackageImpl.getOne(submitResultDto.getSubpackageCode());
        if (subpackage.getPurchaseOk() == one) {
            return R.fail("采购人已经确定评审结果");
        }
        if (subpackage.getLeaderIsReject() == one) {
            return R.fail("本项目已经废标不允许提交");
        }
        if (subpackage.getReviewMode() == three && submitResultDto.getIsType() == zero) {
            if (subpackage.getJudgePreliminaryOk() == one) {
                return R.fail("符合性评审已经确定");
            }
        }
        Map<Long, List<SupplierDao>> resultMap = supplierImpl.getSupplierList(submitResultDto.getSubpackageCode(), one)
                .stream()
                .filter(supplier -> submitResultDto.getSupplierId().longValue() == supplier.getSupplierId())
                .collect(Collectors.groupingBy(SupplierDao::getSupplierId));
        if (CollectionUtils.isEmpty(resultMap)) {
            return R.fail(false, "符合性审查未通过供应商不得参与综合性评审");
        }
        // 添加或修改review_record表信息(添加分数)
        Boolean status = recordImpl.addReviewRecord(submitResultDto);
        // 设置排名
        recordImpl.rankAutomatic(submitResultDto.getSubpackageCode(), subpackage.getReviewMethod());
        if (!status) {
            log.error("提交评审失败,subpackageCode={}", submitResultDto.getSubpackageCode());
            return R.fail("提交评审失败");
        }
        return R.ok(true);
    }


    @ApiOperation("【合格制评审详情】")
    @GetMapping("/nextStep")
    public R<NextStepVo> nextStep(@RequestParam("subpackageCode") String subpackageCode) {
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        if (subpackage.getReviewMode() != three) {
            return R.fail(null, "nextStep不存在");
        }
        if (subpackage.getIsReview() != one) {
            return R.fail(null, "评审尚未开始请稍后");
        }
        NextStepVo nextStepVo = recordImpl.nextStep(subpackageCode);
        if (!isEmpty(nextStepVo)) {
            return R.ok(nextStepVo);
        }
        log.error("一下步失败,subpackageCode={}", subpackageCode);
        return R.fail("一下步失败");
    }

    @ApiOperation("【标段评审组长】")
    @GetMapping("/leader")
    public R<JudgeDao> judgeLeader(@RequestParam("subpackageCode") String subpackageCode) {
        JudgeDao dao = judgeApi.judgeLeader(subpackageCode);
        if (!isEmpty(dao)) {
            return R.ok(dao);
        }
        log.error("根据标段code查询标段评审组长失败,subpackageCode={}", subpackageCode);
        return R.fail("还未选组长");
    }

    @ApiOperation("【评审详情】")
    @GetMapping("/query")
    public R<List<SupplierItemVo>> reviewDetails(@RequestParam("type") Integer type,
                                                 @RequestParam("supplierId") Long supplierId,
                                                 @RequestParam("subpackageCode") String subpackageCode) {
        // 登录校验
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        List<SupplierItemVo> supplierItemVos = reviewApi.queryReviewDetails(type, loginUser.getUserId(), supplierId, subpackageCode);
        if (!CollectionUtils.isEmpty(supplierItemVos)) {
            return R.ok(supplierItemVos);
        }
        log.error("评审详情查询失败,subpackageCode={}", subpackageCode);
        return R.fail("评审详情查询失败");
    }


    @ApiOperation("【调研详情】")
    @GetMapping("/querySurvey")
    public R<List<SupplierItemVo>> reviewSurvey(@RequestParam("supplierId") Long supplierId,
                                                @RequestParam("subpackageCode") String subpackageCode) {
        // 登录校验
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        List<SupplierItemVo> supplierItemVos = reviewApi.querySurvey(supplierId, subpackageCode);
        if (!CollectionUtils.isEmpty(supplierItemVos)) {
            return R.ok(supplierItemVos);
        }
        log.error("评审详情查询失败,supplierId={},subpackageCode={}", supplierId, subpackageCode);
        return R.fail("评审详情查询失败");
    }


    @Log(title = "合格制评审结果确认", businessType = BusinessType.OTHER)
    @ApiOperation("【合格制评审结果确认】")
    @GetMapping("/groupLeaderOk")
    public R<Boolean> groupLeader(@RequestParam("subpackageCode") String subpackageCode,
                                  @RequestParam(value = "supplierIds", required = false) Long[] supplierIds) {
        // 登录校验
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        long leaderId = loginUser.getUserId();
        // 非组长
        if (leaderId != subpackage.getTeamLeaderId()) {
            if (subpackage.getJudgePreliminaryOk() == zero) {
                return R.fail("组长已经确认过评审结果");
            }
            return R.fail("仅组长才有本项操作权限");
        }
        // 组长
        if (leaderId == subpackage.getTeamLeaderId()) {
            if (subpackage.getJudgePreliminaryOk() == one) {
                return R.fail("已经确认过，不可再次修改");
            }
            boolean status = subpackageImpl.teamLeaderOk(subpackage.getPurchaseFunctionJson(), subpackageCode, supplierIds);
            if (!status) {
                log.error("组长确认合格制评审结果失败,subpackageCode={},supplierIds={}", subpackageCode, Arrays.toString(supplierIds));
                return R.fail("组长确认合格制评审结果失败");
            }
        }
        return R.ok(true);
    }


    /**
     * 获取评审结果
     *
     * @param subpackageCode 标段code
     * @return
     */
    @ApiOperation("【获取评审结果】")
    @GetMapping("/reviewSummary")
    public R<ReviewSummaryVo> reviewSummary(@RequestParam("subpackageCode") String subpackageCode) {
        // 根据标段code查询review_subpackage表单条数据
        SubpackageDao subpackageDao = subpackageImpl.getOne(subpackageCode);
        if (subpackageDao == null || subpackageDao.getReviewMethod() == null) {
            log.error("根据标段code获取review_subpackage表数据失败={}", subpackageCode);
            return R.fail(null, "评审结果为空");
        }
        if (subpackageDao.getReviewMethod() == 2) {
            List<SupplierDao> supplierList = supplierImpl.getSupplierList(subpackageCode, 1);
            if (supplierList == null || supplierList.isEmpty()) {
                return R.ok(null, "暂无评审结果");
            }
            List<SupplierDetailsVo> supplierDetailsVoList = new ArrayList<>();
            for (SupplierDao supplierDao : supplierList) {
                SupplierDetailsVo supplierDetailsVo = recordImpl.getVoters(subpackageCode, supplierDao.getSupplierId());
                BeanUtils.copyProperties(supplierDao, supplierDetailsVo);
                supplierDetailsVo.setRank(supplierDao.getJudgeRanking());
                supplierDetailsVo.setIsType(subpackageDao.getReviewMethod());
                supplierDetailsVoList.add(supplierDetailsVo);
            }
            ReviewSummaryVo rsv = new ReviewSummaryVo();
            rsv.setJudgesSign(toSignVo(subpackageDao.getReportFileKey(), subpackageCode));
            rsv.setSupplierDetailsVoList(supplierDetailsVoList);
            rsv.setReviewMethod(subpackageDao.getReviewMethod());
            rsv.setSubpackageCode(subpackageDao.getSubpackageCode());
            return R.ok(rsv);
        } else {
            // 评审结果
            List<SupplierDetailsVo> supplierDetailsVoList = null;
            // 未完成评审的评委名单
            List<IncompleteJudgeVo> incompleteSize;
            if (subpackageDao.getReviewMode() == one) {
                incompleteSize = recordImpl.incompleteStatus(zero, subpackageCode);
                if (CollectionUtils.isEmpty(incompleteSize)) {
                    supplierDetailsVoList = recordImpl.reviewResults(zero, subpackageDao, subpackageCode);
                }
            } else {
                incompleteSize = recordImpl.incompleteStatus(one, subpackageCode);
                if (CollectionUtils.isEmpty(incompleteSize)) {
                    supplierDetailsVoList = recordImpl.reviewResults(one, subpackageDao, subpackageCode);
                }
            }
            ReviewSummaryVo rsv = new ReviewSummaryVo();
            // 存在未完成评审的情况
            if (!CollectionUtils.isEmpty(incompleteSize)) {
                rsv.setIncompleteJudges(incompleteSize);
                rsv.setSupplierDetailsVoList(null);
                rsv.setJudgesSign(null);
            } else {
                rsv.setSubpackageCode(subpackageCode);
                rsv.setSupplierDetailsVoList(supplierDetailsVoList);
                R<ReviewSummaryVo> reviewSummaryVoR = remotePurchaserService.calculatePriceScore(SecurityConstants.INNER, rsv);
                if (reviewSummaryVoR.hasFail()) {
                    log.error("调用接口计算价格分数异常,subpackageCode={},r={}", subpackageCode, reviewSummaryVoR);
                }
                ReviewSummaryVo data = reviewSummaryVoR.getData();
                if (data == null) {
                    rsv.setSupplierDetailsVoList(supplierDetailsVoList);
                } else {
                    rsv.setSupplierDetailsVoList(data.getSupplierDetailsVoList());
                }
                rsv.setIncompleteJudges(null);
                rsv.setJudgesSign(toSignVo(subpackageDao.getReportFileKey(), subpackageCode));
            }
            return R.ok(rsv);
        }
    }


    @Log(title = "废标", businessType = BusinessType.OTHER)
    @ApiOperation("【废标】")
    @PostMapping("/bidRejection")
    public R<Boolean> supplierList(@RequestParam("subpackageCode") String subpackageCode, @RequestParam("remarksBidRejection") String remarksBidRejection) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录 ！");
            }
        }
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        if (!isEmpty(subpackage)) {
            if (subpackage.getPurchaseOk() == one) {
                return R.fail("评审结果经确认后,不允许废标 ！");
            }
            if (loginUser.getUserId().longValue() != subpackage.getTeamLeaderId()) {
                return R.fail("非本项目组长没有废标权限 ！");
            }
        }
        Boolean status = subpackageImpl.bidRejection(subpackageCode, one, remarksBidRejection);
        if (status) {
            return R.ok(true);
        }
        log.error("废标失败,subpackageCode={},remarksBidRejection={}", subpackageCode, remarksBidRejection);
        return R.fail("废标失败");
    }

    @Log(title = "设置供应商排名", businessType = BusinessType.OTHER)
    @ApiOperation("【设置供应商排名】")
    @PostMapping("/setRank")
    public R<Boolean> supplierList(@RequestBody @Valid RemarksRankDto remarksRank) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录 ！");
            }
        }
        SubpackageDao subpackage = subpackageImpl.getOne(remarksRank.getSubpackageCode());
        if (!isEmpty(subpackage)) {
            if (subpackage.getPurchaseOk() == one) {
                return R.fail("评审结果已确定,不允许修改 ！");
            }
            if (loginUser.getUserId().longValue() != subpackage.getTeamLeaderId()) {
                return R.fail("非组长没有设置评审排名权限 ！");
            }
        }
        // 龙岗特有判断
        if (EvUtils.ev().equals(ClientEnum.LG.getCode()) && subpackage.getReviewMethod() == 2) {
            // 该项目评委列表
            List<JudgeDao> judgeDaos = judgeApi.judgesDtoList(remarksRank.getSubpackageCode());
            QueryWrapper<RecordDao> qw = new QueryWrapper<>();
            qw.lambda().eq(RecordDao::getSubpackageCode, remarksRank.getSubpackageCode());
            // 投票数量
            long count = recordImpl.count(qw);
            if (judgeDaos.size() != count) {
                log.error("投票未完成,无法设置排名,subpackageCode={}", remarksRank.getSubpackageCode());
                return R.fail("投票未完成,无法设置排名");
            }
        }
        // 修改供应商排名
        Boolean rankStatus = supplierImpl.setSupplierRank(remarksRank.getRankDtoList(), remarksRank.getSubpackageCode());
        if (rankStatus) {
            // 设置评审备注
            Boolean status = subpackageImpl.bidRejection(remarksRank.getSubpackageCode(), zero, remarksRank.getRemarksBidRejection());
            if (status) {
                return R.ok(true);
            }
        }
        log.error("设置供应商排名失败,subpackageCode={}", remarksRank.getSubpackageCode());
        return R.fail("设置供应商排名失败");
    }

    @ApiOperation("【查询调研信息】")
    @GetMapping("/researchInformation")
    public R<ReportInfoVo> researchInformation(@RequestParam("subpackageCode") String subpackageCode) {
        return remotePurchaserService.judgeView(SecurityConstants.INNER, subpackageCode);
    }

    @Log(title = "修改签字状态", businessType = BusinessType.OTHER)
    @ApiOperation(value = "修改签字状态")
    @GetMapping(value = "/researchSignStatus")
    public R<Boolean> researchSignStatus(@RequestParam(value = "subpackageCode") String subpackageCode) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        return remotePurchaserService.editSignStatus(SecurityConstants.INNER, subpackageCode, loginUser.getUserId());
    }

    @Log(title = "评审组签章", businessType = BusinessType.OTHER)
    @RedisLock
    @ApiOperation("【评审组签章】")
    @GetMapping("/sign")
    @Transactional
    public R<Boolean> judgeSign(@RequestParam("subpackageCode") String subpackageCode,
                                @RequestParam("fileKey") String fileKey,
                                String extrasContents,
                                @RequestParam(value = "flowId", required = false) String flowId,
                                @RequestParam(value = "authCode", required = false) String authCode) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (isEmpty(loginUser)) {
            loginUser = tokenService.getLoginUserByCookie(Objects.requireNonNull(ServletUtils.getRequest()));
            if (isEmpty(loginUser)) {
                return R.fail("请登录");
            }
        }
        return sign(loginUser, subpackageCode, fileKey, loginUser.getUserId() + "epcos", extrasContents, flowId, authCode);
    }


    public R<Boolean> sign(LoginUser loginUser, String subpackageCode, String fileKey, String key, String extrasContents, String flowId, String authCode) {
        // 个人--根据关键字盖章
//        FUtil.psnSealByKeyword(fileKey, key, String.valueOf(loginUser.getUserId()), extrasContents);
        FUtil.startSeal(fileKey, key, String.valueOf(loginUser.getUserId()), extrasContents, UserConstants.PERSON_KEY_SEAL_PARAMETER, flowId, authCode);

        // 根据标段code查询review_subpackage表单条数据
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);

        switch (subpackage.getReviewMethod()) {
            case 0:
                R<Boolean> r = remotePurchaserService.editSignStatus(SecurityConstants.INNER, subpackageCode, loginUser.getUserId());
                if (r.hasFail() || !r.getData()) {
                    log.error("调用/other/to/editSignStatus接口异常,subpackageCode={},userId={},r={}", subpackageCode, loginUser.getUserId(), r);
                    return R.fail(false, r.getMsg());
                }
                break;
            case 1:
            case 2:
                judgeApi.setJudgeSign(subpackageCode, SecurityUtils.getLoginUser().getUserId());
                break;
        }

        return R.ok(true, "签章成功");
    }

    private JudgeSignVo toSignVo(String reportFileKey, String subpackageCode) {
        JudgeSignVo signVo = new JudgeSignVo();
        List<JudgeSignInfoVo> signInfo = new ArrayList<>();
        List<JudgeDao> daos = judgeApi.judgesDtoList(subpackageCode);
        for (JudgeDao dao : daos) {
            JudgeSignInfoVo info = new JudgeSignInfoVo();
            BeanUtils.copyProperties(dao, info);
            signInfo.add(info);
        }
        signVo.setJudgeSignInfo(signInfo);
        signVo.setReportFileKey(reportFileKey);
        return signVo;
    }
}

