package com.epcos.review.controller.pdf;

import com.epcos.common.core.factory.pdf.IGeneratePdf;
import com.epcos.common.core.factory.pdf.PdfFactory;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.poi.ResUtils;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.review.business.service.ReviewPdfService;
import com.epcos.review.export.pdf.impl.RspCompareQualifiedImpl;
import com.epcos.review.export.pdf.impl.RspCompareScoreImpl;
import com.epcos.review.export.pdf.vo.RspComparePdfVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.file.Path;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 9:31
 */
@Api(tags = "评审服务pdf")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/review.pdf")
public class ReviewPdfController {

    private final ReviewPdfService reviewPdfService;


    @ApiOperation("导出评审因素等的pdf")
    @RequiresPermissions(value = "expert:process:operate")
    @Log(title = "导出评审因素等的pdf", businessType = BusinessType.UPDATE)
    @GetMapping("/down.compare")
    public ResponseEntity<byte[]> downCompare(@RequestParam("type") Integer type, @RequestParam("supplierId") Long supplierId,
                                              @RequestParam("subpackageCode") String subpackageCode) {
        RspComparePdfVo vo = reviewPdfService.queryRspCompareInfo(type, supplierId, subpackageCode, SecurityUtils.getUserId());
//        RspComparePdfVo vo = reviewPdfService.queryRspCompareInfo(type, supplierId, subpackageCode, 1233L);
        IGeneratePdf generatePdf;
        if (0 == type) {
            generatePdf = PdfFactory.GeneratePdfFactory(RspCompareQualifiedImpl.class);
        } else {
            generatePdf = PdfFactory.GeneratePdfFactory(RspCompareScoreImpl.class);
        }
        Path path = generatePdf.generatePdf(vo);
        return ResUtils.unify(path);
    }
}
