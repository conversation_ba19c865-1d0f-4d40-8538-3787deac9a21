package com.epcos.review.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class RemarksRankDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("包编码")
    @NotBlank(message = "包编码不允许为空")
    private String subpackageCode;


    @ApiModelProperty("评审说明")
    @NotBlank(message = "评审说明不允许为空")
    private String remarksBidRejection;


    @ApiModelProperty("供应商排名")
    @NotNull(message = "供应商排名不得为空")
    private List<SupplierRankDto> rankDtoList;

}
