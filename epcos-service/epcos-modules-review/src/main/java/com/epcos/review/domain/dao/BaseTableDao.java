package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class BaseTableDao implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 包编码
     */
    @TableField(value = "subpackage_code")
    private String subpackageCode;
    /**
     * 创建时间时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 最后修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;



}
