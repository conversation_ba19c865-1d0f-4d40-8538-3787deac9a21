package com.epcos.review.export.pdf.impl;

import com.epcos.common.core.domain.pdf.Pdf;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.epcos.review.export.pdf.AbGenerateReviewPdf;
import com.epcos.review.export.pdf.vo.ReviewDetailPdfVo;
import com.epcos.review.export.pdf.vo.RspComparePdfVo;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;

import static com.epcos.common.core.factory.pdf.constants.PdfValues.*;

/**
 * pdf流程
 * 合格制导出实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 10:12
 */
@Slf4j
public class RspCompareQualifiedImpl extends AbGenerateReviewPdf {


    @Override
    protected Path generateFilePath(Pdf vo) {
        RspComparePdfVo rspComparePdfVo = (RspComparePdfVo) vo;
        String fileName = HtmlUtil.getBusinessFileName(
                getFileName(rspComparePdfVo.getSupplierCompanyName()),
                String.valueOf(System.currentTimeMillis()),
                ".pdf"
        );
        return HtmlUtil.getTmpPdfFilePath(fileName);
    }

    private String getFileName(String fileName) {
        return REVIEW_RSP_COMPARE_FILE_NAME + fileName + "_合格制_" + DateUtils.getDate();
    }

    @Override
    protected void generateTitle(Document document) {
        document.add(Itext7PdfUtil.paragraphTitle(REVIEW_RSP_COMPARE_HEAD_NAME));
    }

    @Override
    protected void generateContent(Document document, Pdf vo) {
        RspComparePdfVo rspComparePdfVo = (RspComparePdfVo) vo;
        document.add(Itext7PdfUtil.paragraphHeadLeft(rspComparePdfVo.getSupplierCompanyName()));
    }

    @Override
    protected void generateTableContent(Document document, Pdf vo) {

        RspComparePdfVo rspComparePdfVo = (RspComparePdfVo) vo;

        List<String> tableHeads = Lists.newArrayList();
        tableHeads.addAll(REVIEW_RSP_COMPARE_TABLE_HEAD);

        //总列数
        int maxColumnWidth = tableHeads.size();
        //行跨度，即合并几行
        int rowSpan = 1;
        //列跨度，即合并几列
        int columnSpan = 1;
        Table table = Itext7PdfUtil.initTable(maxColumnWidth);

        //表格头
        tableHeads.forEach(head ->
                table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(head))));
        //表内容
        List<ReviewDetailPdfVo> detailPdfVoList = rspComparePdfVo.getReviewDetailPdfVoList();
        for (int i = 0; i < detailPdfVoList.size(); i++) {
            ReviewDetailPdfVo detailPdfVo = detailPdfVoList.get(i);
            table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter((i + 1) + "")));

            Map<String, String> convertedMap = convertToMap(detailPdfVo);
            convertedMap.remove("分值");
            convertedMap.values().forEach(value ->
                    table.addCell(Itext7PdfUtil.cellCenter(rowSpan, columnSpan, Itext7PdfUtil.paragraphHeadCenter(value))));
        }
        document.add(table);
    }
}
