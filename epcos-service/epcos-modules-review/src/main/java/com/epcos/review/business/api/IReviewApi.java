package com.epcos.review.business.api;

import com.epcos.review.domain.vo.SupplierItemVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 9:53
 */
public interface IReviewApi {


    /**
     * 查询评审详情
     *
     * @param type
     * @param userId
     * @param supplierId
     * @param subpackageCode
     * @return
     */
    List<SupplierItemVo> queryReviewDetails(Integer type, Long userId, Long supplierId, String subpackageCode);

    /**
     * 调研详情
     *
     * @param supplierId
     * @param subpackageCode
     * @return
     */
    List<SupplierItemVo> querySurvey(Long supplierId, String subpackageCode);
}
