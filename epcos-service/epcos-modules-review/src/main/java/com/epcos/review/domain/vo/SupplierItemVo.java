package com.epcos.review.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class SupplierItemVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("包编码")
    private String subpackageCode;

    @ApiModelProperty("评审模块编号uuid")
    private String uuid;

    @ApiModelProperty("评审模块名称")
    private String reviewItem;

    @ApiModelProperty("评审模块详细规则及说明")
    private String reviewCriteria;

    @ApiModelProperty("评审类型：1符合性评审 or 评分表")
    private Integer reviewType;

    @ApiModelProperty(" 1-主观分,0-客观分")
    private Integer isSubjective;

    @ApiModelProperty("约束分值")
    private Double reviewScore;

    @ApiModelProperty("供应商id")
    private Long supplierId;

    @ApiModelProperty("响应文件中得分点定位")
    private List<String> scoreChapter;

    @ApiModelProperty("是否合格 （合格：1 不合格：0 ）")
    private Integer qualified;

    @ApiModelProperty("得分")
    private Double score;

}




