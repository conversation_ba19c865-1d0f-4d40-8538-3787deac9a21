package com.epcos.review.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class ClaimsDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 包编码 */
    @ApiModelProperty("包编码")
    private String subpackageCode;
    /**
     * 评审模块编号uuid
     */
    @ApiModelProperty("评审模块编号uuid")
    private String uuid;

    /**
     * 评审模块名称
     */
    @ApiModelProperty("评审模块名称")
    private String reviewItem;

    /**
     * 评审模块详细规则及说明
     */
    @ApiModelProperty("评审模块详细规则及说明")
    private String reviewCriteria;
    /**
     * 评审类型：1符合性评审 or 评分表
     */
    @ApiModelProperty("评审类型：1符合性评审 or 评分表")
    private Integer reviewType;
    /**
     * 1-主观分,0-客观分
     */
    @ApiModelProperty("1-主观分,0-客观分")
    private Integer isSubjective;
    /**
     * 约束分值
     */
    @ApiModelProperty("约束分值")
    private Double reviewScore;

}
