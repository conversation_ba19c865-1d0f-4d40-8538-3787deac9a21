package com.epcos.review.controller;

import com.epcos.common.core.domain.R;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.review.business.api.IChoiceLeaderApi;
import com.epcos.review.domain.dto.ChoiceLeaderDto;
import com.epcos.review.domain.vo.VotingResultsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "选择评委组长(投票选择组长)")
@Slf4j
@RestController
@RequestMapping("/choiceLeader")
@RequiredArgsConstructor
public class ChoiceController {

    private final IChoiceLeaderApi choiceLeaderImpl;

    @Log(title = "评委投票结果", businessType = BusinessType.INSERT)
    @ApiOperation("【评委投票结果】")
    @GetMapping("/list")
    public R<List<VotingResultsVo>> choiceLeaderList(@RequestParam("subpackageCode") String subpackageCode) {
        List<VotingResultsVo> vos = choiceLeaderImpl.getVotingResults(subpackageCode);
        if (!CollectionUtils.isEmpty(vos)) {
            return R.ok(vos);
        }
        log.error("获取评委投票结果失败,subpackageCode={}", subpackageCode);
        return R.fail("获取评委投票结果失败");
    }

    @Log(title = "投票", businessType = BusinessType.INSERT)
    @ApiOperation("【投票】")
    @GetMapping("/add")
    @RedisLock
    public R<List<VotingResultsVo>> choiceLeaderList(ChoiceLeaderDto dto) {
        // 添加/修改 review_leader表(投票)
        Boolean status = choiceLeaderImpl.addChoiceLeader(dto);

        if (!ObjectUtils.isEmpty(status) && status) {
            // 获取评委投票结果
            List<VotingResultsVo> vos = choiceLeaderImpl.getVotingResults(dto.getSubpackageCode());
            return R.ok(vos);
        }
        log.error("投票失败,dto={}", dto.toString());
        return R.fail("投票失败");
    }


    @Log(title = "重新投票", businessType = BusinessType.UPDATE)
    @ApiOperation("【重新投票】")
    @GetMapping("/refresh")
    public R<Boolean> refreshChoiceLeader(@RequestParam("subpackageCode") String subpackageCode) {

        // 根据标段code删除review_leader表信息
        return R.ok(choiceLeaderImpl.cleanRecord(subpackageCode));
    }
}
