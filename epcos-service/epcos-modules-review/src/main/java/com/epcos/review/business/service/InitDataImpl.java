package com.epcos.review.business.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.epcos.bidding.purchase.api.RemotePurchaserService;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.JudgesVo;
import com.epcos.bidding.purchase.api.params.ReviewItemVo;
import com.epcos.bidding.purchase.api.params.SubpackageVo;
import com.epcos.bidding.purchase.api.params.dto.SubSupplier;
import com.epcos.bidding.purchase.api.params.dto.SupplierInfoVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.supplier.api.RemoteSupplierService;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.RoundQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.constant.SecurityConstants;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.domain.review.ReviewDetailsVo;
import com.epcos.common.core.domain.review.ReviewJudgesVo;
import com.epcos.common.core.domain.review.ReviewSummaryVo;
import com.epcos.common.core.domain.review.SupplierDetailsVo;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.web.domain.user.SysUser;
import com.epcos.common.file.html2pdf.HtmlUtil;
import com.epcos.review.business.api.*;
import com.epcos.review.domain.dao.*;
import com.epcos.review.domain.dto.ClaimsDto;
import com.epcos.review.domain.dto.SupplierChapterDto;
import com.epcos.review.domain.vo.SupplierDetailsXyVo;
import com.epcos.review.domain.vo.SupplierItemVo;
import com.epcos.system.api.RemoteUserService;
import com.epcos.system.api.model.FUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.ClientEnum.WS;
import static com.epcos.common.core.enums.ClientEnum.XY;
import static org.springframework.util.ObjectUtils.isEmpty;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InitDataImpl implements InitDataApi {
    private static final int one = 1;
    private static final int zero = 0;
    private static final int two = 2;
    private static final int three = 3;
    private static final int oKCode = 200;
    @Value("${spring.profiles.active}")
    private String activeProfile;

    private final IClaimsApi claimsImpl;
    private final ISupplierApi supplierImpl;
    private final IJudgeApi judgesImpl;
    private final IChoiceLeaderApi choiceLeaderImpl;
    private final IRecordApi reviewRecordImpl;
    private final ISubpackageApi subpackageImpl;
    private final ISupplierChapterApi supplierChapterImpl;
    private final RemoteSupplierService remoteSupplierService;
    private final RemotePurchaserService remotePurchaserService;
    private final RemoteUserService remoteUserService;


    /**
     * 生成评标报告
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    public String createReport(String subpackageCode) {
        try {
            // 查询并封装评审报告数据
            Map<String, Object> map = reportData(subpackageCode);
            Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
            // 获取项目中的/templates/ReportTmp.ftl文件
            configuration.setClassLoaderForTemplateLoading(this.getClass().getClassLoader(), "/templates");
            Template template;
            if (EvUtils.ev().equals(XY.getCode())) {
                template = configuration.getTemplate("ReportTmp_xy.ftl");
            } else if (WS.getCode().equals(EvUtils.ev())) {
                template = configuration.getTemplate("ReportTmp_ws.ftl");
            } else {
                template = configuration.getTemplate("ReportTmp.ftl");
            }
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
            File file = HtmlUtil.toPdfByReport(content, "【专家评审报告】");
            // 根据标段code查询review_subpackage表单条数据
            SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
            // 根据采购项目编号或者包编号查询采购方式与功能 根据采购项目
            R<BuyItemVo> buyItemInfo = remotePurchaserService.queryBuyItemInfo(SecurityConstants.INNER, subpackage.getBuyItemCode(), subpackageCode, null);

            if (isEmpty(buyItemInfo) || buyItemInfo.hasFail() || isEmpty(buyItemInfo.getData())) {
                log.error("调用other/to/queryBuyItemInfo接口失败,buyItemCode={},subpackageCode={},r={}", subpackageCode, subpackageCode, buyItemInfo);
                throw new ServiceException(buyItemInfo.getMsg());
            }
            BuyItemVo buyItem = buyItemInfo.getData();
            // 项目归档文件上传
            String fileCode = FUtil.upFile(buyItem.getBuyItemCode(), buyItem.getYearMonthSplit(), file, FileTypeNameConstants.BUY_REPORT, subpackageCode, SecurityUtils.getUserId());
            subpackageImpl.setReport(subpackageCode, fileCode);

            return fileCode;
        } catch (Exception e) {
            log.error("生成评标报告失败,subpackageCode={}", subpackageCode, e);
            throw new ServiceException("生成评标报告失败");
        }
    }

    /**
     * 重新生成评审报告并盖章
     *
     * @param subpackageCode
     * @return
     */
    @Override
    public R<Boolean> againConfirm(String subpackageCode) {
        // 生成评审报告
        String fileCode = createReport(subpackageCode);
        // 只有签章v2才盖章
        if (!FUtil.getSealVersion()) {
            List<JudgeDao> judgesDtoList = judgesImpl.judgesDtoList(subpackageCode);
            if (judgesDtoList != null && !judgesDtoList.isEmpty()) {
                for (JudgeDao judgeDao : judgesDtoList) {
                    FUtil.startSeal(fileCode, judgeDao.getJudgeId() + "epcos", String.valueOf(judgeDao.getJudgeId()), null, UserConstants.PERSON_KEY_SEAL_PARAMETER, null, null);
                }
            }
        }
        return R.ok(true,"重新生成评审报告成功");
    }

    /**
     * 查询并封装评审报告数据
     *
     * @param subpackageCode 标段code
     * @return
     */
    private Map<String, Object> reportData(String subpackageCode) {
        // 根据标段code查询review_subpackage表单条数据
        SubpackageDao subpackage = subpackageImpl.getOne(subpackageCode);
        // 1-合格制评审 2-打分制评审 3-合格与打分制并存
        Integer reviewMode = subpackage.getReviewMode();
        List<SupplierDetailsVo> qualifiedInfo = new ArrayList<>();
        List<SupplierDetailsVo> resultSummary = new ArrayList<>();
        List<SupplierDetailsVo> supplierDetailsVoList = new ArrayList<>();
        // 襄阳特有参数
        List<SupplierDetailsXyVo> supplierDetailsXyVoList = new ArrayList<>();

        //  reviewMode ： 1 只有合格  2- 只有打分  3 打分和合格并存    isType ： 0-合格  1-打分  2-投票
        if (subpackage.getReviewMethod() == 2) {
            List<SupplierDao> supplierList = supplierImpl.getSupplierList(subpackageCode, one);
            for (SupplierDao supplierDao : supplierList) {
                SupplierDetailsVo supplierDetailsVo = reviewRecordImpl.getVoters(subpackageCode, supplierDao.getSupplierId());
                BeanUtils.copyProperties(supplierDao, supplierDetailsVo);
                supplierDetailsVo.setRank(supplierDao.getJudgeRanking());
                supplierDetailsVo.setIsType(2);
                supplierDetailsVoList.add(supplierDetailsVo);
            }
        } else {
            if (reviewMode == one) {
                // 查询并封装供应商的评审结果列表
                resultSummary = reviewRecordImpl.supplierDetails(reviewMode, zero, supplierImpl.getSupplierList(subpackageCode, zero), subpackageCode);
            } else {
                if (reviewMode == two) {
                    resultSummary = reviewRecordImpl.supplierDetails(reviewMode, one, supplierImpl.getSupplierList(subpackageCode, zero), subpackageCode);
                } else {
                    resultSummary = reviewRecordImpl.supplierDetails(reviewMode, one, supplierImpl.getSupplierList(subpackageCode, one), subpackageCode);
                    qualifiedInfo = reviewRecordImpl.supplierDetails(reviewMode, zero, supplierImpl.getSupplierList(subpackageCode, zero), subpackageCode);
                }
            }
        }
        // 查询项目院内编号
//        R<SubpackageVo> r = remotePurchaserService.buyItemInfo(SecurityConstants.INNER, subpackageCode);
//        if (r.hasFail()) {
//            log.error("调用接口other/to/queryInnerCode失败,subpackageCode={}", subpackageCode);
//        } else {
//            subpackage.setBuyItemCode(r.getData().getBuyItemCode());
//        }
        // 查询流程编号
        R<String> integerR = remotePurchaserService.getInnerCode(SecurityConstants.INNER, subpackage.getBuyItemCode());
        if (integerR.hasFail() || integerR.getData() == null || integerR.getData().isEmpty()) {
            log.error("调用接口xyzy.buyItem/getInnerCode失败,buyItemCode={}", subpackage.getBuyItemCode());
        }
        String innerCode = integerR.getData();
        // 襄阳特有
        if (EvUtils.ev().equals(XY.getCode())) {

            // 查询价格分
            ReviewSummaryVo rsv = new ReviewSummaryVo();
            rsv.setSubpackageCode(subpackageCode);
            rsv.setSupplierDetailsVoList(resultSummary);
            R<ReviewSummaryVo> reviewSummaryVoR = remotePurchaserService.calculatePriceScore(SecurityConstants.INNER, rsv);
            if (reviewSummaryVoR.hasFail() || reviewSummaryVoR.getData() == null) {
                log.error("调用接口计算价格分数异常,subpackageCode={},r={}", subpackageCode, reviewSummaryVoR);
            } else {
                ReviewSummaryVo data = reviewSummaryVoR.getData();
                resultSummary = data.getSupplierDetailsVoList();
            }
//            // 查询最大轮数
//            R<Integer> maxRound = remotePurchaserService.findMaxRound(SecurityConstants.INNER, subpackageCode);
//            if (maxRound.hasFail() || maxRound.getData() == null) {
//                log.error("查询议价最大轮数异常,subpackageCode={},r={}", subpackageCode, maxRound);
//            } else {
//            }
            // 查询总价
            R<MultiSupplierQuoteFormVo> r1 = remoteSupplierService.queryQuoteContent(SecurityConstants.INNER, subpackageCode, null, null);
            if (r1.hasFail() || r1.getData() == null || r1.getData().getSupplierQuoteFormList() == null || r1.getData().getSupplierQuoteFormList().isEmpty()) {
                log.error("以包编码查询报价内容异常,subpackageCode={},r={}", subpackageCode, r1);
            } else {
                for (SupplierDetailsVo supplierDetailsVo : resultSummary) {
                    for (SupplierQuoteFormVo supplierQuoteFormVo : r1.getData().getSupplierQuoteFormList()) {
                        if (supplierDetailsVo.getSupplierId().equals(supplierQuoteFormVo.getSupplierId()) && supplierQuoteFormVo.getRoundQuoteFormList() != null && !supplierQuoteFormVo.getRoundQuoteFormList().isEmpty()) {
                            RoundQuoteFormVo roundQuoteFormVo = supplierQuoteFormVo.getRoundQuoteFormList().get(supplierQuoteFormVo.getRoundQuoteFormList().size() - 1);
                            supplierDetailsVo.setAllRowQuotationTotalPrice(roundQuoteFormVo.getAllRowQuotationTotalPrice());
                        }
                    }
                }
            }
            // 查询评分明细表
            List<ReviewJudgesVo> judgesVoList = resultSummary.get(0).getJudgesVoList();
            if (judgesVoList != null) {
                // 第一层 每次循环都是一个专家
                for (ReviewJudgesVo judgesVo : judgesVoList) {

                    SupplierDetailsXyVo supplierDetailsXyVo = new SupplierDetailsXyVo();
                    supplierDetailsXyVo.setJudgeId(judgesVo.getJudgeId());
                    supplierDetailsXyVo.setJudgeName(judgesVo.getJudgeName());

                    List<List<String>> dataList = new ArrayList<>();
                    List<String> headerData = new ArrayList<>();
                    List<BigDecimal> totalSourceList = new ArrayList<>();
                    headerData.add("评审类型");
                    headerData.add("分值");
                    headerData.add("评审标准");
                    // 第二层第一部分 每次循环都是该表的表头
                    for (SupplierDetailsVo detailsVo : resultSummary) {
                        // 总分
                        totalSourceList.add(detailsVo.getTotalSource());
                        headerData.add(detailsVo.getSupplierCompanyName());
                    }
                    List<SupplierItemVo> supplierItemVos = queryReviewDetails(1, judgesVo.getJudgeId(), resultSummary.get(0).getSupplierId(), subpackageCode);
                    if (!supplierItemVos.isEmpty()) {
                        // 第二.二层 每次循环都是该供应商的评分明细
                        for (int j = 0; j < supplierItemVos.size(); j++) {
                            List<String> bodyData = new ArrayList<>();
                            bodyData.add(supplierItemVos.get(j).getReviewItem());
                            bodyData.add(String.valueOf(supplierItemVos.get(j).getReviewScore()));
                            bodyData.add(supplierItemVos.get(j).getReviewCriteria());
                            // 第三层 每次循环都是一个供应商信息,为了拿到这个评分明细下面不同供应商的得分
                            for (SupplierDetailsVo supplierDetailsVo : resultSummary) {
                                List<SupplierItemVo> supplierItemVosA = queryReviewDetails(1, judgesVo.getJudgeId(), supplierDetailsVo.getSupplierId(), subpackageCode);
                                bodyData.add(String.valueOf(supplierItemVosA.get(j).getScore()));
                            }
                            dataList.add(bodyData);
                        }
                    }
                    supplierDetailsXyVo.setHeaderList(headerData);
                    supplierDetailsXyVo.setDataList(dataList);
                    supplierDetailsXyVo.setTotalSourceList(totalSourceList);
                    supplierDetailsXyVoList.add(supplierDetailsXyVo);
                }
            }
        }
        Map<String, Object> DataMaps = new HashMap<>();
        DataMaps.put("systemType", EvUtils.ev()); //系统类型
        DataMaps.put("reportDate", new Date());
        DataMaps.put("subpackage", subpackage);
        DataMaps.put("innerCode", innerCode);
        // 根据标段code查询review_judges表列表,封装ReportUserBo列表并返回
        DataMaps.put("judgeList", judgesImpl.findReportUserBo(subpackageCode));
        DataMaps.put("resultSummary", resultSummary.isEmpty() ? null : resultSummary);   // 评审结果
        DataMaps.put("qualifiedInfo", qualifiedInfo.isEmpty() ? null : qualifiedInfo);
        DataMaps.put("supplierDetailsXyVoList", supplierDetailsXyVoList.isEmpty() ? null : supplierDetailsXyVoList);      // 襄阳特有
        DataMaps.put("supplierDetailsVoList", supplierDetailsVoList.isEmpty() ? null : supplierDetailsVoList);

        String v = Arrays.stream(ClientEnum.values())
                .filter(f -> Objects.equals(activeProfile, f.getCode()))
                .findFirst() // 找到第一个匹配的元素（如果有的话）
                .map(i -> i.getName())
                .orElse("");
        DataMaps.put("orgName", v); // 如果找到了，就执行这个操作
        return DataMaps;
    }

    private List<SupplierItemVo> queryReviewDetails(Integer type, Long judgesId, Long supplierId, String subpackageCode) {
        List<ClaimsDao> claimsDaoList = claimsImpl.claimsListBySubpackage(type, subpackageCode);
        List<SupplierChapterDao> supplierChapterList = supplierChapterImpl.querySupplierChapter(subpackageCode, supplierId);
        RecordDao recordVo = reviewRecordImpl.queryReviewRecord(type, subpackageCode, judgesId, supplierId);
        List<ReviewDetailsVo> resultBoList = null;
        if (!isEmpty(recordVo)) {
            if (StringUtils.hasLength(recordVo.getResultJson())) {
                resultBoList = JSON.parseObject(recordVo.getResultJson(), new TypeReference<List<ReviewDetailsVo>>() {
                });
            }
        }
        List<SupplierItemVo> itemList = new ArrayList<>();
        for (ClaimsDao dao : claimsDaoList) {
            ReviewDetailsVo detailsVo = null;
            if (!CollectionUtils.isEmpty(resultBoList)) {
                detailsVo = reviewByUuid(resultBoList, dao.getUuid());
            }
            SupplierItemVo itemVo = new SupplierItemVo();
            SupplierChapterDao supplierChapter = chapterByUuid(supplierChapterList, dao.getUuid());
            com.epcos.common.core.utils.bean.BeanUtils.copyProperties(dao, itemVo);
            itemVo.setSupplierId(supplierId);
            if (!isEmpty(supplierChapter)) {
                itemVo.setScoreChapter(JSON.parseObject(supplierChapter.getScoreChapter(), new TypeReference<List<String>>() {
                }));
            }
            if (!isEmpty(detailsVo)) {
                itemVo.setQualified(detailsVo.getQualified());
                itemVo.setScore(detailsVo.getScore());
            }
            itemList.add(itemVo);
        }
        return itemList;
    }

    private ReviewDetailsVo reviewByUuid(List<ReviewDetailsVo> reviewDetailsList, String uuid) {
        if (!CollectionUtils.isEmpty(reviewDetailsList)) {
            List<ReviewDetailsVo> chapter = reviewDetailsList.stream().collect(Collectors.groupingBy(ReviewDetailsVo::getUuid)).get(uuid);
            return chapter.get(chapter.size() - one);
        } else {
            return null;
        }
    }

    private SupplierChapterDao chapterByUuid(List<SupplierChapterDao> supplierChapterList, String uuid) {
        if (!CollectionUtils.isEmpty(supplierChapterList)) {
            List<SupplierChapterDao> chapter = supplierChapterList.stream().collect(Collectors.groupingBy(SupplierChapterDao::getUuid)).get(uuid);
            return chapter.get(chapter.size() - one);
        } else {
            return null;
        }
    }

    /**
     * 采购人确认组长，修改review_leader表、review_judges表、review_subpackage表
     *
     * @param subpackageCode 标段code
     * @param judgeId        供应商id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setLeader(String subpackageCode, Long judgeId) {
        // review_leader表，设置组长
        Boolean status = choiceLeaderImpl.setLeader(subpackageCode, judgeId);
        if (!status) {
            log.error("review_leader表，设置组长失败,subpackageCode={},judgeId={}", subpackageCode, judgeId);
            throw new ServiceException("review_leader表，设置组长失败");
        }
        // review_judges表，设置组长
        status = judgesImpl.setJudgesLeader(subpackageCode, judgeId);
        if (!status) {
            log.error("review_judges表，设置组长失败,subpackageCode={},judgeId={}", subpackageCode, judgeId);
            throw new ServiceException("review_judges表，设置组长失败");
        }
        // review_subpackage表，设置组长
        status = subpackageImpl.setTeamLeader(subpackageCode, judgesImpl.judgeOne(subpackageCode, judgeId));
        if (!status) {
            log.error("review_subpackage表，设置组长失败,subpackageCode={},judgeId={}", subpackageCode, judgeId);
            throw new ServiceException("review_subpackage表，设置组长失败");
        }

        return status;
    }


    /**
     * 初始化 评审数据
     *
     * @param subpackageCode 标段code
     * @param isBargaining   ?
     * @param delegateId     代理人id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean initReview(String subpackageCode, Integer isBargaining, Long delegateId) {

        Boolean status;
        // 根据标段code 查询采购项目、采购项目对应的包、公告等部分信息，并新增review_subpackage表信息
        status = this.saveSubpackage(subpackageCode);
        if (!status) {
            log.error("新增新增review_subpackage表信息失败,subpackageCode={},isBargaining={},delegateId={}", subpackageCode, isBargaining, delegateId);
            throw new ServiceException("新增新增review_subpackage表信息失败");
        }
        // 初始化评委数据和评委选拔组长数据（review_judges表、review_leader表）
        status = this.saveInitJudge(subpackageCode, delegateId);
        if (!status) {
            log.error("初始化评委数据和评委选拔组长数据（新增review_judges表、review_leader表）失败,subpackageCode={},isBargaining={},delegateId={}", subpackageCode, isBargaining, delegateId);
            throw new ServiceException("初始化评委数据和评委选拔组长数据（新增review_judges表、review_leader表）失败");
        }
        // 根据标段code获取供应商信息，添加或修改 review_supplier表数据
        status = this.saveSupplier(subpackageCode);
        if (!status) {
            log.error("根据标段code获取供应商信息，添加或修改 review_supplier表数据失败,subpackageCode={},isBargaining={},delegateId={}", subpackageCode, isBargaining, delegateId);
            throw new ServiceException("根据标段code获取供应商信息，添加或修改 review_supplier表数据失败");
        }
        if (!EvUtils.ev().equals(ClientEnum.LG.getCode())) {
            // 根据标段code 查询评审内容,添加或修改review_claims表(评审规则集)数据
            status = this.saveBachClaims(subpackageCode);
            if (!status) {
                log.error("添加或修改review_claims表(评审规则集)数据失败,subpackageCode={},isBargaining={},delegateId={}", subpackageCode, isBargaining, delegateId);
                throw new ServiceException("添加或修改review_claims表(评审规则集)数据失败");
            }
            // 查询供应商列表(review_supplier表),根据标段code和供应商id查询响应文件评审办法,添加review_supplier_chapter表
            status = this.saveBachSupplierChapter(subpackageCode);
            if (!status) {
                log.error("添加review_supplier_chapter表失败,subpackageCode={}", subpackageCode);
                throw new ServiceException("添加review_supplier_chapter表失败");
            }
            // 评审形式： 1-合格制评审 2-打分制评审 3 合格与打分制并存
            Integer reviewMode = groupReviewType(subpackageCode);
            if (reviewMode > zero) {
                // 修改review_subpackage表(评审标段基础信息列表)
                status = subpackageImpl.createProcessNodes(subpackageCode, reviewMode, isBargaining);
                if (!status) {
                    log.error("修改review_subpackage表失败,subpackageCode={},reviewMode={},isBargaining={}", subpackageCode, reviewMode, isBargaining);
                    throw new ServiceException("修改review_subpackage表失败");
                }
            } else {
                log.error("获取评审形式失败,subpackageCode={}", subpackageCode);
                throw new ServiceException("获取评审形式失败");
            }
        }
        return true;
    }


    /**
     * 重新评审
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteReview(String subpackageCode) {
        Boolean status;
        // 根据标段code删除review_leader表
        status = choiceLeaderImpl.deleteChoiceInfo(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("删除review_leader表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("删除review_leader表失败");
        }
        // 删除旧的评审报告
        SubpackageDao subpackageDao = subpackageImpl.getOne(subpackageCode);
        if (subpackageDao != null && subpackageDao.getReportFileKey() != null && !subpackageDao.getReportFileKey().isEmpty()) {
            FUtil.delFile(subpackageDao.getReportFileKey());
        }

        // 根据标段code删除review_subpackage表
        status = subpackageImpl.deleteSubpackage(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_subpackage表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_subpackage表失败");
        }
        // 根据标段code删除review_judges表
        status = judgesImpl.deleteJudges(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_judges表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_judges表失败");
        }
        // 根据标段code删除review_supplier表
        status = supplierImpl.deleteSupplier(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_supplier表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_supplier表失败");
        }
        // 根据标段code删除review_claims表数据
        status = claimsImpl.deleteClaims(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_claims表数据失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_claims表数据失败");
        }
        // 根据标段code删除review_supplier_chapter表数据
        status = supplierChapterImpl.deleteSupplierChapter(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_supplier_chapter表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_supplier_chapter表失败");
        }
        // 根据标段code删除review_leader表数据
        status = choiceLeaderImpl.refreshChoiceLeader(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_leader表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_leader表失败");
        }
        // 根据标段code删除review_record表数据
        status = reviewRecordImpl.deleteRecord(subpackageCode);
        if (isEmpty(status) && !status) {
            log.error("根据标段code删除review_record表失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code删除review_record表失败");
        }
        return true;
    }

    /**
     * 根据标段code 查询采购项目、采购项目对应的包、公告等部分信息，并新增新增review_subpackage表信息
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSubpackage(String subpackageCode) {
        // 根据标段code 查询采购项目、采购项目对应的包、公告等部分信息
        R<SubpackageVo> r = remotePurchaserService.buyItemInfo(SecurityConstants.INNER, subpackageCode);
        if (r.hasFail() || r.getData() == null) {
            log.error("调用/other/to/buyItemInfo接口异常,subpackageCode={},r={}", subpackageCode, r);
            throw new ServiceException("查询采购项目信息失败");
        }
        SubpackageVo vo = r.getData();
        if (vo == null) {
            log.error("根据标段code 查询采购项目、采购项目对应的包、公告等部分信息失败,subpackageCode={}", subpackageCode);
            throw new ServiceException("根据标段code 查询采购项目、采购项目对应的包、公告等部分信息失败");
        }
        SubpackageDao dao = new SubpackageDao();
        BeanUtils.copyProperties(vo, dao);
        if (EvUtils.ev().equals(ClientEnum.LG.getCode())) {
            dao.setReviewMethod(2);
            dao.setReportFileKey(null);
        }

        // 新增review_subpackage表信息
        return subpackageImpl.saveSubpackage(dao);
    }


    /**
     * 初始化评委数据和评委选拔组长数据（review_judges表、review_leader表）
     *
     * @param subpackageCode 标段code
     * @param delegateId     代理人id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveInitJudge(String subpackageCode, Long delegateId) {
        Boolean status;
        // 查询purchase_extract_judge_inner表（评委抽取表数据）
        R<List<JudgesVo>> r = remotePurchaserService.getJudge(SecurityConstants.INNER, subpackageCode);
        if (r.hasFail()) {
            log.error("调用/other/to/getJudge接口异常,subpackageCode={},delegateId={},r={}", subpackageCode, delegateId, r);
            throw new ServiceException(r.getMsg());
        }
        List<JudgesVo> judgesVoList = r.getData();
        if (!CollectionUtils.isEmpty(judgesVoList)) {
            for (JudgesVo vo : judgesVoList) {
                JudgeDao dao = new JudgeDao();
                LeaderDao leaderDao = new LeaderDao();

                dao.setSubpackageCode(subpackageCode);
                dao.setDelegate(vo.getDeptRepresent());
                if (!isEmpty(delegateId)) {
                    if (delegateId.longValue() == vo.getJudgeId()) {
                        dao.setDelegate(one);
                        leaderDao.setDelegate(one);
                    }
                }
                leaderDao.setSubpackageCode(subpackageCode);
                BeanUtils.copyProperties(vo, dao);
                BeanUtils.copyProperties(vo, leaderDao);
                dao.setInsideIdentity(Objects.requireNonNull(getSysUser(dao.getJudgeId())).getInsideIdentity());
                status = judgesImpl.addJudgesDao(dao);
                if (status) {
                    status = choiceLeaderImpl.addChoiceLeaderDao(leaderDao);
                    if (!status) {
                        log.error("添加或修改review_leader表数据失败,subpackageCode={}", subpackageCode);
                        throw new ServiceException("添加或修改review_leader表数据失败");
                    }
                } else {
                    log.error("添加或修改review_judges表数据失败,subpackageCode={}", subpackageCode);
                    throw new ServiceException("添加或修改review_judges表数据失败");
                }
            }
        }
        return true;
    }

    /**
     * 根据标段code获取供应商信息，添加或修改 review_supplier表数据
     *
     * @param subpackageCode 标段code
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSupplier(String subpackageCode) {
        // 根据标段code获取供应商信息
        R<SubSupplier> r = remotePurchaserService.getSupplier(SecurityConstants.INNER, subpackageCode);
        if (r.hasFail() || r.getData() == null || r.getData().getSupplierInfoVoList() == null || r.getData().getSupplierInfoVoList().isEmpty()) {
            log.error("调用/other/to/getSupplier接口异常,subpackageCode={},r={}", subpackageCode, r);
            throw new ServiceException("暂无供应商");
        }
        // 数据存在
        List<SupplierInfoVo> supplierInfoVoList = r.getData().getSupplierInfoVoList();
        for (SupplierInfoVo vo : supplierInfoVoList) {
            SupplierDao dao = new SupplierDao();
            BeanUtils.copyProperties(vo, dao);
            dao.setSubpackageCode(subpackageCode);
            // 添加或修改 review_supplier表数据
            Boolean status = supplierImpl.saveUpdateSupplierDao(dao);
            if (!status) {
                log.error("添加或修改 review_supplier表数据失败,subpackageCode={}", subpackageCode);
                throw new ServiceException("添加或修改 review_supplier表数据失败");
            }
        }
        return true;
    }

    /**
     * 评审形式： 1-合格制评审 2-打分制评审 3 合格与打分制并存
     */
    private Integer groupReviewType(String subpackageCode) {
        Map<Integer, List<ClaimsDao>> resultMap = claimsImpl.claimsListAll(subpackageCode).stream().collect(Collectors.groupingBy(ClaimsDao::getReviewType));
        if (resultMap.size() == one) {
            if (one == resultMap.entrySet().stream().findFirst().get().getKey()) {
                return one;
            }
            return two;
        }
        if (resultMap.size() > one) {
            if (!isEmpty(resultMap.get(one))) {
                return three;
            }
        }
        return two;
    }

    /**
     * 查询供应商列表(review_supplier表),根据标段code和供应商id查询响应文件评审办法,添加review_supplier_chapter表,并返回是否成功
     *
     * @param subpackageCode 标段code
     * @return
     */
    private Boolean saveBachSupplierChapter(String subpackageCode) {
        // 查询供应商列表(review_supplier表)
        List<SupplierDao> supplierList = supplierImpl.getSupplierList(subpackageCode, zero);
        List<SupplierChapterDto> supplierChapterList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(supplierList)) {
            for (SupplierDao dao : supplierList) {
                // 根据标段code和供应商id查询响应文件评审办法
                R<EvaluationMethodVo> r = remoteSupplierService.evaluationMethod(SecurityConstants.INNER, subpackageCode, dao.getSupplierId());

                if (r.hasFail()) {
                    log.error("调用/supplier/answer/remote/evaluationMethod接口异常,subpackageCode={},supplierId={},r={}", subpackageCode, dao.getSupplierId(), r);
                    throw new ServiceException(r.getMsg());
                }
                EvaluationMethodVo evaluationMethodVo = r.getData();

                if (isEmpty(evaluationMethodVo)) {
                    break;
                }

                // 封装数据
                List<ReviewItemVo> itemList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(evaluationMethodVo.getScoreReview())) {
                    itemList.addAll(evaluationMethodVo.getScoreReview());
                }
                if (!CollectionUtils.isEmpty(evaluationMethodVo.getConformityReview())) {
                    itemList.addAll(evaluationMethodVo.getConformityReview());
                }
                if (!CollectionUtils.isEmpty(itemList)) {
                    for (ReviewItemVo vo : itemList) {
                        SupplierChapterDto dto = new SupplierChapterDto();
                        dto.setUuid(vo.getUuid());
                        dto.setScoreChapter(vo.getScoreChapter());
                        dto.setSubpackageCode(subpackageCode);
                        dto.setSupplierId(dao.getSupplierId());
                        supplierChapterList.add(dto);
                    }
                } else {
                    log.error("根据标段code和供应商id查询响应文件评审办法失败,subpackageCode={},supplierId={}", subpackageCode, dao.getSupplierId());
                    throw new ServiceException(" 根据标段code和供应商id查询响应文件评审办法失败");
                }
            }
            // 添加review_supplier_chapter表,并返回是否成功
            return supplierChapterImpl.addBachChapter(supplierChapterList);
        }
        log.error("查询供应商列表失败,subpackageCode={}", subpackageCode);
        throw new ServiceException("查询供应商列表失败");
    }

    /**
     * 根据标段code 查询评审内容,添加或修改review_claims表(评审规则集)数据
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBachClaims(String subpackageCode) {
        // 根据标段code 查询评审内容
        R<EvaluationMethodVo> r = remotePurchaserService.evaluationMethod(SecurityConstants.INNER, subpackageCode);

        if (r.hasFail()) {
            log.error("调用/purchase/claims/remote/evaluationMethod接口异常,subpackageCode={},r={}", subpackageCode, r);
            throw new ServiceException(r.getMsg());
        }
        EvaluationMethodVo methodVo = r.getData();

        List<ReviewItemVo> reviewItemList = new ArrayList<>();
        List<ClaimsDto> claimsDtoList = new ArrayList<>();

        if (!isEmpty(methodVo)) {
            if (!CollectionUtils.isEmpty(methodVo.getScoreReview())) {
                reviewItemList.addAll(methodVo.getScoreReview());
            }
            if (!CollectionUtils.isEmpty(methodVo.getConformityReview())) {
                reviewItemList.addAll(methodVo.getConformityReview());
            }
            for (ReviewItemVo vo : reviewItemList) {
                ClaimsDto dto = new ClaimsDto();
                BeanUtils.copyProperties(vo, dto);
                dto.setSubpackageCode(subpackageCode);
                claimsDtoList.add(dto);
            }
            // 添加或修改review_claims表(评审规则集)数据
            return claimsImpl.saveOrUpdateBatch(claimsDtoList);
        }
        log.error("根据标段code 查询评审内容失败,subpackageCode={}", subpackageCode);
        throw new ServiceException("根据标段code 查询评审内容失败");
    }

    private SysUser getSysUser(Long userId) {
        try {
            R<SysUser> r = remoteUserService.getInfoById(userId, SecurityConstants.INNER);

            if (r.hasFail()) {
                log.error("调用/user/getInfoById接口异常,userId={},r={}", userId, r);
                throw new ServiceException(r.getMsg());
            }

            if (!isEmpty(r) && r.getCode() == oKCode) {
                return r.getData();
            }
        } catch (Exception e) {
            log.error("获取用户信息失败,userId={}", userId);
            throw new ServiceException("获取用户信息失败");
        }
        return null;
    }

    /**
     * file 转换 fileItem
     */
    private FileItem createFileItem(File file) {
        if (Objects.isNull(file) || !file.exists()) {
            log.error("传入 file 不能为 null, 且文件必须存在");
            throw new ServiceException("传入 file 不能为 null, 且文件必须存在");
        }
        final DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory(50 * 1024 * 1024, new File(System.getProperty("java.io.tmpdir")));
        final FileItem item = diskFileItemFactory.createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, false, file.getName());
        final byte[] buff = new byte[1024 * 10];
        try (
                final FileInputStream fileIn = new FileInputStream(file);
                final OutputStream out = item.getOutputStream()
        ) {
            int n;
            while ((n = fileIn.read(buff)) != -1) {
                out.write(buff, 0, n);
            }
        } catch (IOException e) {
            log.error("file 转换 fileItem IO流异常，e:{}, file:{}", e, file);
            throw new ServiceException("file 转换 fileItem IO流异常");
        }
        return item;
    }

}
