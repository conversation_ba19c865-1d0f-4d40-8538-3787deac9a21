package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("review_leader")
public class LeaderDao extends BaseTableDao implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  投票评委ID
     */
    @TableField(value = "judge_id")
    private Long  judgeId  ;
    /**
     * 投票评委姓名
     */
    @TableField(value = "judge_name")
    private String judgeName ;

    /**
     * 被提名组长的评委ID
     */
    @TableField(value = "judge_leader_id")
    private Long  judgeLeaderId;
    /**
     * 被提名组长的的评委姓名
     */
    @TableField(value = "judge_leader_name")
    private String  judgeLeaderName ;

    /**
     * 是否组长
     */
    @TableField(value = "is_leader")
    private String  isLeader ;
    /**
     * 采购代表：1-代表,  0-非代表
     */
    @TableField(value = "delegate")
    private Integer delegate;

}
