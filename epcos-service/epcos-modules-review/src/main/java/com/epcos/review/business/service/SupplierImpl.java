package com.epcos.review.business.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.review.business.api.ISupplierApi;
import com.epcos.review.domain.dao.SupplierDao;
import com.epcos.review.domain.dto.SupplierRankDto;
import com.epcos.review.mapper.SupplierMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierImpl extends ServiceImpl<SupplierMapper, SupplierDao> implements ISupplierApi {
    private static final int one = 1;
    private static final int zero = 0;


    /**
     * 添加或修改 review_supplier表数据
     *
     * @param dao
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveUpdateSupplierDao(SupplierDao dao) {
        try {
            SupplierDao one = getOne(dao.getSubpackageCode(), dao.getSupplierId());
            if (ObjectUtils.isEmpty(one)) {
                dao.setUpdateTime((LocalDateTime.now()));
                dao.setCreateTime((LocalDateTime.now()));
                this.save(dao);
            } else {
                dao.setUpdateTime((LocalDateTime.now()));
                LambdaUpdateWrapper<SupplierDao> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(SupplierDao::getSubpackageCode, dao.getSubpackageCode());
                wrapper.eq(SupplierDao::getSupplierId, dao.getSupplierId());
                this.update(dao, wrapper);
            }
            return true;
        } catch (Exception e) {
            log.error("保存或更新供应商失败,dao={}", dao.toString(), e);
            throw new ServiceException("保存或更新供应商失败");
        }
    }


    /**
     * 查询供应商列表
     *
     * @param subpackageCode
     * @param reviewScore
     * @return
     */
    @Override
    public List<SupplierDao> getSupplierList(String subpackageCode, Integer reviewScore) {
        try {
            LambdaQueryWrapper<SupplierDao> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SupplierDao::getSubpackageCode, subpackageCode);
            if (reviewScore == one) {
                wrapper.eq(SupplierDao::getReviewScore, one);
            }
            return this.list(wrapper);
        } catch (Exception e) {
            log.error("查询供应商失败,subpackageCode={},reviewScore={}", subpackageCode, reviewScore, e);
            throw new ServiceException("查询供应商失败");
        }
    }

    /**
     * 根据标段code删除review_supplier表
     *
     * @param subpackageCode
     * @return
     */
    @Override
    public Boolean deleteSupplier(String subpackageCode) {
        try {
            LambdaUpdateWrapper<SupplierDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SupplierDao::getSubpackageCode, subpackageCode);
            this.remove(wrapper);
        } catch (Exception e) {
            log.error("清空供应商失败,subpackageCode={}", subpackageCode, e);
            throw new ServiceException("清空供应商失败");
        }
        return true;
    }

    /**
     * 淘汰供应商：1-合格 0-淘汰
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean eliminateSupplier(String subpackageCode, Long supplierId) {
        try {
            LambdaUpdateWrapper<SupplierDao> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SupplierDao::getSubpackageCode, subpackageCode);
            wrapper.eq(SupplierDao::getSupplierId, supplierId);
            wrapper.set(SupplierDao::getReviewScore, zero);
            this.update(wrapper);
        } catch (Exception e) {
            log.error("淘汰供应商失败,subpackageCode={},supplierId={}", subpackageCode, supplierId, e);
            throw new ServiceException("淘汰供应商失败");
        }
        return true;
    }

    /**
     * 修改review_supplier表(修改供应商排名)
     *
     * @param ranks          供应商排名列表
     * @param subpackageCode 标段code
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setSupplierRank(List<SupplierRankDto> ranks, String subpackageCode) {
        if (!CollectionUtils.isEmpty(ranks)) {
            for (SupplierRankDto supplier : ranks) {
                try {
                    LambdaUpdateWrapper<SupplierDao> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.eq(SupplierDao::getSubpackageCode, subpackageCode);
                    wrapper.eq(SupplierDao::getSupplierId, supplier.getSupplierId());
                    wrapper.set(SupplierDao::getJudgeRanking, supplier.getRank());
                    this.update(wrapper);
                } catch (Exception e) {
                    log.error("设置供应商排名失败,subpackageCode={}", subpackageCode, e);
                    throw new ServiceException("设置供应商排名失败");
                }
            }
        }
        return true;
    }

    /**
     * 根据标段code,供应商id查询review_supplier表,查询到该供应商的评委推荐名次并返回
     *
     * @param subpackageCode
     * @param supplierId
     * @return
     */
    @Override
    public Integer getRank(String subpackageCode, Long supplierId) {
        try {
            LambdaQueryWrapper<SupplierDao> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SupplierDao::getSubpackageCode, subpackageCode);
            wrapper.eq(SupplierDao::getSupplierId, supplierId);
            SupplierDao supplierDao = this.getOne(wrapper);
            if (!ObjectUtils.isEmpty(supplierDao) && !ObjectUtils.isEmpty(supplierDao.getJudgeRanking())) {
                return supplierDao.getJudgeRanking();
            }
            return null;
        } catch (Exception e) {
            log.error("供应商排名失败,subpackageCode={},supplierId={}", subpackageCode, supplierId, e);
            throw new ServiceException("供应商排名失败");
        }
    }

    private SupplierDao getOne(String subpackageCode, Long supplierId) {
        try {
            LambdaQueryWrapper<SupplierDao> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SupplierDao::getSubpackageCode, subpackageCode);
            wrapper.eq(SupplierDao::getSupplierId, supplierId);
            return this.getOne(wrapper);
        } catch (Exception e) {
            log.error("供应商数据异常,subpackageCode={},supplierId={}", subpackageCode, supplierId, e);
            throw new ServiceException("供应商数据异常");
        }
    }


    @Override
    public SupplierDao queryInfo(String subpackageCode, Long supplierId) {
        SupplierDao su = getOne(Wrappers.lambdaUpdate(SupplierDao.class)
                .eq(SupplierDao::getSubpackageCode, subpackageCode)
                .eq(SupplierDao::getSupplierId, supplierId));
        return su;
    }
}
