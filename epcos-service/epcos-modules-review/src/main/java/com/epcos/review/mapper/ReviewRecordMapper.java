package com.epcos.review.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.epcos.review.domain.dao.RecordDao;
import com.epcos.review.domain.vo.RecordVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Mapper
public interface ReviewRecordMapper extends BaseMapper<RecordDao> {


    /**
     * 评审方式为投票时 按照从大到小的顺序查询供应商的投票列表
     * @param subpackageCode
     * @return
     */
    List<RecordVo> selRecordNum(String subpackageCode);


    /**
     * 评审方式为打分时 按照从大到小的顺序查询供应商的分数列表
     * @param subpackageCode
     * @return
     */
    List<RecordVo> selRecordSum(String subpackageCode);
}
