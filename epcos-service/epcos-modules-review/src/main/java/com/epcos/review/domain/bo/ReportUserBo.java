package com.epcos.review.domain.bo;
import lombok.Data;
import java.io.Serializable;
@Data
public class ReportUserBo  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 评委ID
     */
    private Long judgeId;
    /**
     * 评委姓名
     */
    private String judgeName;
    /**
     * 所在科室
     */
    private String department;
    /**
     * 证件号码
     */
    private String insideIdentity;
    /**
     * 评委类型： 1-组长,  0-组员
     */
    private Integer judgeType;
    /**
     * 采购代表：1-代表,  0-非代表
     */
    private Integer delegate;

}
