package com.epcos.review.domain.vo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class VotingResultsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("评委ID")
    private Long judgeId;

    @ApiModelProperty("评委姓名")
    private String judgeName;

    @ApiModelProperty("是否投票")
    private Integer voting;

    @ApiModelProperty("票数")
    private Integer numberVotes;

    @ApiModelProperty("投票人")
    private List<String> judgeNames;

    @ApiModelProperty("是否是组长")
    private String  isLeader ;

    @ApiModelProperty("评委类型：1-科室代表, 0-非代表")
    private Integer delegate;

}
