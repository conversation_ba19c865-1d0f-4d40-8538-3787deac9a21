package com.epcos.review.export.pdf;

import com.epcos.common.core.domain.pdf.Pdf;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.factory.pdf.IGeneratePdf;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Path;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/6 9:14
 */
@Slf4j
public abstract class AbGenerateReviewPdf implements IGeneratePdf {

    @Override
    public Path generatePdf(Pdf vo) {
        Path path = generateFilePath(vo);
        try (final PdfWriter pdfWriter = new PdfWriter(path.toFile());
             final PdfDocument pdfDocument = new PdfDocument(pdfWriter);
             final Document document = new Document(pdfDocument, PageSize.A4.rotate())) {
            document.setFont(Itext7PdfUtil.pdfFont());
            generateTitle(document);
            generateContent(document, vo);
            Itext7PdfUtil.blankLinesInTheDocument(document, 1);
            generateTableContent(document, vo);
        } catch (IOException e) {
            log.error("导出pdf失败. e:{}", e);
            throw new ServiceException("导出pdf失败");
        }
        return path;

    }

    /**
     * 生成带有文件名的临时文件
     *
     * @return
     */
    protected abstract Path generateFilePath(Pdf vo);

    /**
     * 生成pdf内部的标题内容
     *
     * @param document
     */
    protected abstract void generateTitle(Document document);

    /**
     * 生成正文内容
     *
     * @param document
     * @param vo
     */
    protected abstract void generateContent(Document document, Pdf vo);

    /**
     * 生成正文等其他内容
     *
     * @param document
     * @param vo
     */
    protected abstract void generateTableContent(Document document, Pdf vo);
}
