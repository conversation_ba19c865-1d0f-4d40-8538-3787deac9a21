package com.epcos.review.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class SubpackageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("包编码")
    private String subpackageCode;

    @ApiModelProperty("包名称")
    private String subpackageName;

    @ApiModelProperty("采购项目编号")
    private String buyItemCode;

    @ApiModelProperty("采购方式名称-英文标识")
    private String purchaseMethodType;

    @ApiModelProperty("采购方式名称-中文名称")
    private String purchaseMethodName;

    @ApiModelProperty("采购项目名称")
    private String buyItemName;

    @ApiModelProperty("评审组长是否废标：  1-废 ， 0-不")
    private Integer leaderIsReject;

    @ApiModelProperty("评审方式：  1-评审 ， 0-调研")
    private Integer reviewMethod;

    @ApiModelProperty("1-合格制评审 2-打分制评审  3-合格与打分制并存")
    private Integer reviewMode;

    @ApiModelProperty("采购文件地址key")
    private String claimsFileKey;

    @ApiModelProperty("评审报告KEY")
    private String reportFileKey;

    @ApiModelProperty("变更文件地址key")
    private String changeFileKeys;

    @ApiModelProperty("功能点json")
    private String purchaseFunctionJson;

    @ApiModelProperty("监督人 ID")
    private Long  monitorBidPersonId;

    @ApiModelProperty("监督人姓名")
    private String monitorBidPerson;

    @ApiModelProperty("组长 ID")
    private Long  teamLeaderId;

    @ApiModelProperty("组长姓名")
    private String teamLeaderName;

    @ApiModelProperty("评审说明")
    private String remarksBidRejection;



}
