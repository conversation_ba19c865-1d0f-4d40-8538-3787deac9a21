package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @version V1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("review_record")
public class RecordDao extends BaseTableDao implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评审类型 ：0合格制 1 打分制  2-投票制
     */
    @TableField(value = "is_type")
    private Integer isType;

    /**
     * 专家ID
     */
    @TableField(value = "judge_id")
    private Long judgeId;

    /**
     * 专家姓名
     */
    @TableField(value = "judge_name")
    private String judgeName;

    /**
     * 供应商单位名字
     */
    @TableField(value = "supplier_company_name")
    private String supplierCompanyName;

    /**
     * 供应商id
     */
    @TableField(value = "supplier_id")
    private Long supplierId;

    /**
     * 合格制评审结果  list<Integer>
     */
    @TableField(value = "result_json")
    private String resultJson;

    /**
     * 打分制评审结果最终分值
     */
    @TableField(value = "score_result")
    private Double scoreResult ;

    /**
     * 合格制评审结果 ：1-合格，0-不合格
     */
    @TableField(value = "qualified_result")
    private Integer qualifiedResult ;
}
