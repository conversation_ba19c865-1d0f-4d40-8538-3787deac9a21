package com.epcos.review.domain.vo;

import com.epcos.common.core.domain.review.IncompleteJudgeVo;
import com.epcos.common.core.domain.review.SupplierDetailsVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class NextStepVo  implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("初步评审详情")
    private List<SupplierDetailsVo> supplierDetailsVoList ;
    @ApiModelProperty("未完评审的专家")
    private List<IncompleteJudgeVo> incompleteJudge;
}
