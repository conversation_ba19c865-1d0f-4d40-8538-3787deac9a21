package com.epcos.review.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class SupplierChapterDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 评审项ID
     */
    @ApiModelProperty("评审项ID")
    private String uuid;
    /**
     * 包code
     */
    @ApiModelProperty("包code")
    private String subpackageCode;
    /**
     * '供应商id
     */
    @ApiModelProperty("供应商id")
    private Long supplierId;
    /**
     * 响应文件中得分点定位
     */
    @ApiModelProperty("响应文件中得分点定位")
    private List<String> scoreChapter;
}
