package com.epcos.review.export.pdf.vo;

import com.epcos.common.core.annotation.Export;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 10:40
 */
@Data
public class ReviewDetailPdfVo {

    @Export(value = "审查因素")
    private String reviewItem;

    @Export(value = "审查因素描述")
    private String reviewCriteria;

    @Export(value = "响应对照页码")
    private String scoreChapter;

    @Export("分值")
    private String reviewScore;

}
