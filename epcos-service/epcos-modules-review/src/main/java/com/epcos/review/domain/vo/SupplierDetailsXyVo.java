package com.epcos.review.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 襄阳特有 评分明细表 内容
 */
@Data
public class SupplierDetailsXyVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("专家ID")
    private Long judgeId;

    @ApiModelProperty("专家姓名")
    private String judgeName;

    @ApiModelProperty("评分明细表内容")
    private List<List<String>> dataList;

    @ApiModelProperty("评分明细表表头内容")
    private List<String> headerList;

    @ApiModelProperty("总分")
    private List<BigDecimal> totalSourceList;
}




