package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */

@EqualsAndHashCode(callSuper = true)
@TableName("review_claims")
@Data
public class ClaimsDao extends BaseTableDao implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评审模块编号uuid
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 评审模块
     */
    @TableField(value = "review_item")
    private String reviewItem;

    /**
     * 评审规则
     */
    @TableField(value = "review_criteria")
    private String reviewCriteria;

    /**
     * 评审类型：1符合性评审 or 评分表
     */
    @TableField(value = "review_type")
    private Integer reviewType;

    /**
     * 1-主观分,0-客观分
     */
    @TableField(value = "subjective")
    private Integer subjective;

    /**
     * 约束分值
     */
    @TableField(value = "review_score")
    private Double reviewScore;


}
