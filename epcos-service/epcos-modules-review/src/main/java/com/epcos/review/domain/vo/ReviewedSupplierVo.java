package com.epcos.review.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 * @version V1.0
 */
@Data
public class ReviewedSupplierVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 供应商id
     */
    @ApiModelProperty("供应商id")
    private Long supplierId;
    /**
     * 供应商单位名字
     */
    @ApiModelProperty("供应商单位名字")
    private String supplierCompanyName;

    /**
     * 是否被淘汰：1-合格 0-淘汰
     */
    @ApiModelProperty("是否被淘汰：1-合格 0-淘汰")
    private Integer reviewScore;

}
