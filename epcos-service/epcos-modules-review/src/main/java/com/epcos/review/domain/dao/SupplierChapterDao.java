package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */
@TableName("review_supplier_chapter")
@Data
public class SupplierChapterDao implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 评审项ID
     */
    @TableField(value = "uuid")
    private String uuid;
    /**
     * 包code
     */
    @TableField(value = "subpackage_code")
    private String subpackageCode;
    /**
     * '供应商id
     */
    @TableField(value = "supplier_id")
    private Long supplierId;

    /**
     * 响应文件中得分点定位
     */
    @TableField(value = "score_chapter")
    private String scoreChapter;

}
