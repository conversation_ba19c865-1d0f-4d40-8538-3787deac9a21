package com.epcos.review.domain.dao;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.system.api.domain.assmble.vo.FunctionKV;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 */
@EqualsAndHashCode(callSuper = true)
@TableName("review_subpackage")
@Data
public class SubpackageDao extends BaseTableDao implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 采购项目编号
     */
    @TableField(value = "buy_item_code")
    private String buyItemCode;
    /**
     * 采购项目名称
     */
    @TableField(value = "buy_item_name")
    private String buyItemName;
    /**
     * 包名称
     */
    @TableField(value = "subpackage_name")
    private String subpackageName;
    /**
     * '采购方式名称-英文标识
     */
    @TableField(value = "purchase_method_type")
    private String purchaseMethodType;
    /**
     * 采购方式名称-中文名称
     */
    @TableField(value = "purchase_method_name")
    private String purchaseMethodName;
    /**
     * 1-合格制评审 2-打分制评审 3-合格与打分制并存
     */
    @TableField(value = "review_mode")
    private Integer reviewMode;
    /**
     * 评审报告KEY
     */
    @TableField(value = "report_file_key")
    private String reportFileKey;
    /**
     * 采购文件地址key
     */
    @TableField(value = "claims_file_key")
    private String claimsFileKey;
    /**
     * 变更文件地址key
     */
    @TableField(value = "change_file_keys")
    private String changeFileKeys;
    /**
     * 采购功能点json
     */
    @TableField(value = "purchase_function_json")
    private String purchaseFunctionJson;

    /**
     * 是否评审 : 1-是  ， 0-不评审
     */
    @TableField(value = "is_review")
    private Integer isReview;

    /**
     * 评审组长是否废标：  1废 ， 0不
     */
    @TableField(value = "leader_reject")
    private Integer leaderIsReject;

    /**
     * 评审类型：  1-评审 ， 0-调研   2-投票
     */
    @TableField(value = "review_method")
    private Integer reviewMethod;

    /**
     * 采购人是否同意评审结论 : 1-同意  ， 0-不同意
     */
    @TableField(value = "purchase_ok")
    private Integer purchaseOk;

    /**
     *评审组长同意 "资格性符合性" 结论 : 1-同意  ， 0-不同意
     */
    @TableField(value = "judge_preliminary_ok")
    private Integer judgePreliminaryOk;

    /**
     *监督人 ID
     */
    @TableField(value = "monitor_bid_person_Id")
    private Long  monitorBidPersonId;
    /**
     *监督人姓名
     */
    @TableField(value = "monitor_bid_person")
    private String monitorBidPerson;
    /**
     * 组长Id
     */
    @TableField(value = "team_leader_id")
    private Long  teamLeaderId;
    /**
     * 组长姓名
     */
    @TableField(value = "team_leader_name")
    private String teamLeaderName;
    /**
     * 评审备注
     */
    @TableField(value = "remarks_bid_rejection")
    private String remarksBidRejection;


    public List<FunctionKV> parsePurchaseFunctionJson() {
        return JSONArray.parseArray(this.purchaseFunctionJson, FunctionKV.class);
    }

}
