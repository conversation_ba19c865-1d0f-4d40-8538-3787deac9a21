package com.epcos.review.domain.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */

@EqualsAndHashCode(callSuper = true)
@TableName("review_judges")
@Data
public class JudgeDao extends BaseTableDao implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评委ID
     */
    @TableField(value = "judge_id")
    private Long judgeId;

    /**
     * 评委姓名
     */
    @TableField(value = "judge_name")
    private String judgeName;

    /**
     * 证件号码
     */
    @TableField(value = "inside_identity")
    private String insideIdentity;

    /**
     * 评委类型： 1-组长,  0-组员
     */
    @TableField(value = "judge_type")
    private Integer judgeType;

    /**
     * 采购代表：1-代表,  0-非代表
     */
    @TableField(value = "delegate")
    private Integer delegate;

    /**
     * 评委是否签字： 0-未签字，1-已签字
     */
    @TableField(value = "is_sign")
    private Integer isSign;


}
