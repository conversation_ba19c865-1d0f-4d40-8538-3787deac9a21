package com.epcos.review.business.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.epcos.common.core.domain.review.ReviewDetailsVo;
import com.epcos.common.core.utils.StringUtils;
import com.epcos.review.business.api.IClaimsApi;
import com.epcos.review.business.api.IRecordApi;
import com.epcos.review.business.api.IReviewApi;
import com.epcos.review.business.api.ISupplierChapterApi;
import com.epcos.review.domain.dao.ClaimsDao;
import com.epcos.review.domain.dao.RecordDao;
import com.epcos.review.domain.dao.SupplierChapterDao;
import com.epcos.review.domain.vo.SupplierItemVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 9:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IReviewImpl implements IReviewApi {

    private final IRecordApi recordApi;
    private final IClaimsApi claimsApi;
    private final ISupplierChapterApi supplierChapterApi;

    private static final int one = 1;

    @Override
    public List<SupplierItemVo> queryReviewDetails(Integer type, Long judgesId, Long supplierId, String subpackageCode) {
        List<ClaimsDao> claimsDaoList = claimsApi.claimsListBySubpackage(type, subpackageCode);
        List<SupplierChapterDao> supplierChapterList = supplierChapterApi.querySupplierChapter(subpackageCode, supplierId);
        RecordDao recordVo = recordApi.queryReviewRecord(type, subpackageCode, judgesId, supplierId);
        List<ReviewDetailsVo> resultBoList = null;
        if (Objects.nonNull(recordVo)) {
            if (StringUtils.hasText(recordVo.getResultJson())) {
                resultBoList = JSON.parseObject(recordVo.getResultJson(), new TypeReference<List<ReviewDetailsVo>>() {
                });
            }
        }
        List<SupplierItemVo> itemList = new ArrayList<>();
        for (ClaimsDao dao : claimsDaoList) {
            ReviewDetailsVo detailsVo = null;
            if (!CollectionUtils.isEmpty(resultBoList)) {
                detailsVo = reviewByUuid(resultBoList, dao.getUuid());
            }
            SupplierItemVo itemVo = new SupplierItemVo();
            SupplierChapterDao supplierChapter = chapterByUuid(supplierChapterList, dao.getUuid());
            BeanUtils.copyProperties(dao, itemVo);
            itemVo.setSupplierId(supplierId);
            if (Objects.nonNull(supplierChapter)) {
                itemVo.setScoreChapter(JSON.parseObject(supplierChapter.getScoreChapter(), new TypeReference<List<String>>() {
                }));
            }
            if (Objects.nonNull(detailsVo)) {
                itemVo.setQualified(detailsVo.getQualified());
                itemVo.setScore(detailsVo.getScore());
            }
            itemList.add(itemVo);
        }
        return itemList;
    }

    private ReviewDetailsVo reviewByUuid(List<ReviewDetailsVo> reviewDetailsList, String uuid) {
        if (!CollectionUtils.isEmpty(reviewDetailsList)) {
            List<ReviewDetailsVo> chapter = reviewDetailsList.stream().collect(Collectors.groupingBy(ReviewDetailsVo::getUuid)).get(uuid);
            return chapter.get(chapter.size() - one);
        } else {
            return null;
        }
    }


    @Override
    public List<SupplierItemVo> querySurvey(Long supplierId, String subpackageCode) {
        List<SupplierChapterDao> supplierChapterList = supplierChapterApi.querySupplierChapter(subpackageCode, supplierId);
        List<SupplierItemVo> itemList = new ArrayList<>();
        for (ClaimsDao dao : claimsApi.claimsListAll(subpackageCode)) {
            SupplierItemVo itemVo = new SupplierItemVo();
            SupplierChapterDao supplierChapter = chapterByUuid(supplierChapterList, dao.getUuid());
            BeanUtils.copyProperties(dao, itemVo);
            itemVo.setSupplierId(supplierId);
            if (Objects.nonNull(supplierChapter)) {
                itemVo.setScoreChapter(JSON.parseObject(supplierChapter.getScoreChapter(), new TypeReference<List<String>>() {
                }));
            }

            itemList.add(itemVo);
        }
        return itemList;
    }


    private SupplierChapterDao chapterByUuid(List<SupplierChapterDao> supplierChapterList, String uuid) {
        if (!CollectionUtils.isEmpty(supplierChapterList)) {
            List<SupplierChapterDao> chapter = supplierChapterList.stream().collect(Collectors.groupingBy(SupplierChapterDao::getUuid)).get(uuid);
            return chapter.get(chapter.size() - one);
        } else {
            return null;
        }
    }

}
