package com.epcos.review;

import com.epcos.common.core.utils.EvUtils;
import com.epcos.common.core.utils.LogPrint;
import com.epcos.common.security.annotation.EnableCustomConfig;
import com.epcos.common.security.annotation.EnableRyFeignClients;
import com.epcos.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;

@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
public class ReviewMainRun {

    @Autowired
    private Environment environment;
    @Value("${environment}")
    private String testEv;

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(ReviewMainRun.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ 评审系统启动成功   ლ(´ڡ`ლ)ﾞ\n");
        LogPrint.log(context.getEnvironment());
    }

    /**
     * 在应用启动后初始化 EvUtils
     */
    @PostConstruct
    public void initEvUtils() {
        EvUtils.init(environment, testEv);
        System.out.println("EvUtils 初始化完成！");
    }
}
