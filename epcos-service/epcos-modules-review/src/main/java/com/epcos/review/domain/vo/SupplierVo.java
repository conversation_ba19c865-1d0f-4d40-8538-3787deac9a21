package com.epcos.review.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.review.domain.dao.BaseTableDao;
import com.epcos.review.domain.dao.SupplierDao;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class SupplierVo extends SupplierDao {

    /**
     * 是否投票 true:已投票,false:未投票
     */
    private boolean whetherRecord;

    /**
     * 投票人姓名
     */
    private String votersNames;

    /**
     * 票数
     */
    private Integer votesNum;


}
