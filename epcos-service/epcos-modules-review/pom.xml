<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.epcos</groupId>
        <artifactId>epcos-service</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>epcos-modules-review</artifactId>
    <description>
        评审模块后端实现
    </description>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    </properties>


    <dependencies>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>


        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- epcos Common DataSource -->
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-datasource</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-log</artifactId>
        </dependency>

        <!-- epcos Common Swagger -->
        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-common-file</artifactId>
        </dependency>

        <dependency>
            <groupId>com.epcos</groupId>
            <artifactId>epcos-api-bidding</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>application-*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>bootstrap.yml</include>
                    <include>application.yml</include>
                    <include>application-${epc}.yml</include>
                    <include>application-sz.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <epc>dev</epc>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <epc>test</epc>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <epc>prod</epc>
            </properties>
        </profile>
        <profile>
            <id>th</id>
            <properties>
                <epc>th</epc>
            </properties>
        </profile>
        <profile>
            <id>zl</id>
            <properties>
                <epc>zl</epc>
            </properties>
        </profile>
        <profile>
            <id>pr</id>
            <properties>
                <epc>pr</epc>
            </properties>
        </profile>
        <profile>
            <id>xk</id>
            <properties>
                <epc>xk</epc>
            </properties>
        </profile>

        <profile>
            <id>lg</id>
            <properties>
                <epc>lg</epc>
            </properties>
        </profile>

        <profile>
            <id>xy</id>
            <properties>
                <epc>xy</epc>
            </properties>
        </profile>

        <profile>
            <id>ws</id>
            <properties>
                <epc>ws</epc>
            </properties>
        </profile>
        <profile>
            <id>fj</id>
            <properties>
                <epc>fj</epc>
            </properties>
        </profile>
        <profile>
            <id>cq</id>
            <properties>
                <epc>cq</epc>
            </properties>
        </profile>
    </profiles>


</project>
