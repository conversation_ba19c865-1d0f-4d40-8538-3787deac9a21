package com.epcos.bidding.supplier.answer.business.service;

import cn.hutool.core.collection.CollUtil;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.GetPurchaseQuoteForm;
import com.epcos.bidding.common.annotaion.GetSimpleItem;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.api.params.ReviewItemVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileEvaluationMethodApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileAttApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileEvaluationMethodApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileMenuApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileEvaluationMethodDao;
import com.epcos.bidding.supplier.api.params.MultiSupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierQuoteFormVo;
import com.epcos.bidding.supplier.api.params.SupplierSignUpStatusDto;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBiddingApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierSignUpApi;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.enums.FileTypeNameEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnswerService {

    private final ISupplierSignUpApi iSupplierSignUpApi;
    private final ISupplierBiddingApi iSupplierBiddingApi;
    private final IAnswerFileMenuApi iAnswerFileMenuApi;
    private final IAnswerFileEvaluationMethodApi iAnswerFileEvaluationMethodApi;
    private final IAnswerFileQuoteFormApi iAnswerFileQuoteFormApi;
    private final IAnswerFileAttApi iAnswerFileAttApi;
    private final IClaimsFileEvaluationMethodApi iClaimsFileEvaluationMethodApi;
    private final ThreadPoolTaskExecutor taskExecutor;

    /**
     * 投标文件是客户端
     */
    @GetPurchaseQuoteForm(common = @GetCommon(subpackageCodeEL = "#subpackageCode"))
    public EpcFileContentVo getEpcFileContentVo(String subpackageCode, Long supplierId) {
        EpcFileContentVo vo = Optional.ofNullable(
                        iSupplierBiddingApi.findOne(subpackageCode, supplierId))
                .map(i ->
                        BidFileUtil.convert(null, i, iSupplierBiddingApi.findDemoVideoUrl(subpackageCode, supplierId))
                ).orElse(new EpcFileContentVo());
        vo.setMenuData(iAnswerFileMenuApi.findVo(subpackageCode, supplierId));
        // 评审办法
        EvaluationMethodVo evaluationMethodVo = iClaimsFileEvaluationMethodApi.findBySubpackageCodeVo(subpackageCode);
        List<AnswerFileEvaluationMethodDao> supplierMethodList = iAnswerFileEvaluationMethodApi.find(subpackageCode, supplierId);
        if (Objects.nonNull(evaluationMethodVo)) {
            evaluationMethodVo.setSubpackageCode(subpackageCode);
            evaluationMethodVo.setSupplierId(supplierId);
            if (!CollectionUtils.isEmpty(evaluationMethodVo.getScoreReview())) {
                fillScoreChapter(evaluationMethodVo.getScoreReview(), supplierMethodList);
            }
            if (!CollectionUtils.isEmpty(evaluationMethodVo.getConformityReview())) {
                fillScoreChapter(evaluationMethodVo.getConformityReview(), supplierMethodList);
            }
            vo.setEvaluationMethod(evaluationMethodVo);
        }
        // 采购人报价表
        PurchaseQuoteFormVo purchaseQuoteFormVo = GetUtil.getPurchaserQuoteForm();
        if (Objects.nonNull(purchaseQuoteFormVo)) {
            vo.setHeads(purchaseQuoteFormVo.getHeads());
            vo.setPurchaseBodyMaps(purchaseQuoteFormVo.getBodyMaps());
        }
        // 供应商报价表
        MultiSupplierQuoteFormVo supplierQuoteFormVo = iAnswerFileQuoteFormApi.findVo(subpackageCode, supplierId, 0);
        if (Objects.nonNull(supplierQuoteFormVo) && !CollectionUtils.isEmpty(supplierQuoteFormVo.getSupplierQuoteFormList())) {
            SupplierQuoteFormVo quoteFormVo = supplierQuoteFormVo.getSupplierQuoteFormList().get(0);
            List<LinkedHashMap<String, String>> supplierBodyMaps = quoteFormVo.queryBodyByRound(0);
            vo.setBodyMaps(supplierBodyMaps);
        }
        return vo;
    }

    // 设置投标得分点定位
    private void fillScoreChapter(List<ReviewItemVo> itemVoList, List<AnswerFileEvaluationMethodDao> supplierMethodList) {
        if (!CollectionUtils.isEmpty(supplierMethodList)) {
            itemVoList.forEach(i -> supplierMethodList.stream()
                    .filter(j -> Objects.equals(i.getUuid(), j.getUuid()))
                    .findAny()
                    .ifPresent(k -> i.setScoreChapter(k.convertScoreChapter())));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void up(File finalZipFile, File mergePdf, Long userId,
                   String buyItemCode, String subpackageCode, String yearMonthSplit,
                   PurchaseQuoteFormVo quoteForm, BidFileJson bidFileJson) {
        try {
            // 上传zip
            CompletableFuture<String> zipFileFuture = BidFileUtil.uploadFileAsync(
                    () -> FUtil.upFileWithAttachment(buyItemCode, yearMonthSplit, finalZipFile,
                            FileTypeNameConstants.ANSWER_FILE, subpackageCode, userId), taskExecutor,
                    "投标文件压缩包上传失败", finalZipFile);
            // 上传pdf
            String pdfFileUrl = FUtil.upFile(buyItemCode, yearMonthSplit, mergePdf,
                    FileTypeNameConstants.TENDER_DOC_ENCLOSURE, subpackageCode, userId);
            iAnswerFileMenuApi.delAttachFileAndCreate(finalZipFile.getParentFile().toPath(),
                    buyItemCode, subpackageCode, yearMonthSplit, userId, bidFileJson.getMenuData());
            iAnswerFileEvaluationMethodApi.delAndCreate(subpackageCode, userId, bidFileJson.getEvaluationMethod());
            if (CollUtil.isNotEmpty(bidFileJson.getQuoteSheet())) {
                iAnswerFileQuoteFormApi.delAndCreate(finalZipFile.getParentFile().toPath(), quoteForm,
                        buyItemCode, subpackageCode, yearMonthSplit, userId, 0, bidFileJson.getQuoteSheet());
            }
            iSupplierSignUpApi.updateSignUpStatus(subpackageCode, new SupplierSignUpStatusDto(userId, null, true));
            String zipFileUrl = zipFileFuture.get(5, TimeUnit.MINUTES);
            iSupplierBiddingApi.createOrUpdate(zipFileUrl, pdfFileUrl, subpackageCode, bidFileJson, userId);
        } catch (ServiceException e) {
            log.error("投标文件上传保存处理失败: zipFile={}, buyItemCode={}, subpackageCode={}, userId={}",
                    finalZipFile, buyItemCode, subpackageCode, userId, e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("投标文件上传保存处理异常: zipFile={}, buyItemCode={}, subpackageCode={}, userId={}",
                    finalZipFile, buyItemCode, subpackageCode, userId, e);
            throw new ServiceException("投标文件上传保存处理异常: " + e.getMessage());
        }
    }

    @GetPurchaseQuoteForm(common = @GetCommon(async = true, subpackageCodeEL = "#subpackageCode"))
    @Transactional(rollbackFor = Exception.class)
    public void revocation(String subpackageCode, Long supplierId) {
        iSupplierBiddingApi.delete(subpackageCode, supplierId);
        iAnswerFileEvaluationMethodApi.delete(subpackageCode, supplierId);
        iAnswerFileQuoteFormApi.delete(GetUtil.getPurchaserQuoteForm(), subpackageCode,
                Collections.singleton(supplierId), Collections.singletonList(0), null);
        iAnswerFileMenuApi.delete(subpackageCode, supplierId);
        iAnswerFileAttApi.del(subpackageCode, supplierId);
    }

    @Transactional(rollbackFor = Exception.class)
    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#buyItemCode", subpackageCodeEL = "#subpackageCode"))
    public void upPdf(MultipartFile file, String buyItemCode, String subpackageCode, Long supplierId) {
        String pdfUrl = FUtil.upFile(buyItemCode, GetUtil.getSimpleItemVo().getYearMonthSplit(), file,
                FileTypeNameEnum.TENDER_DOC_ENCLOSURE.getCode(), subpackageCode, supplierId);
        BidFileJson bidFileJson = new BidFileJson();
        bidFileJson.setAppName("web");
        bidFileJson.setVersion("1.1");
        bidFileJson.setZepcKey("web");
        iSupplierBiddingApi.createOrUpdate(null, pdfUrl, subpackageCode, bidFileJson, supplierId);
    }
}
