package com.epcos.bidding.supplier.answer.domain.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.ListFastjsonTypeHandler;
import com.epcos.bidding.common.SubpackageCodeEntity;
import com.epcos.common.core.web.validator.CreateGroup;
import com.epcos.common.core.web.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;

@Data
@ApiModel(description = "供应商报价表")
@NoArgsConstructor
@TableName(value = "answer_file_quote_form",
        resultMap = "com.epcos.bidding.supplier.answer.repository.AnswerFileQuoteFormMapper.answerFileQuoteFormMap")
public class AnswerFileQuoteFormDao extends SubpackageCodeEntity {

    public AnswerFileQuoteFormDao(String subpackageCode, Long supplierId, Integer round) {
        this.subpackageCode = subpackageCode;
        this.supplierId = supplierId;
        this.round = round;
    }

    @ApiModelProperty("供应商id")
    @NotNull(message = "供应商id，必填")
    private Long supplierId;

    @ApiModelProperty("轮数，递交响应文件中默认是0轮")
    @Range(min = 0, max = 3, message = "轮数【0-3】", groups = {CreateGroup.class, UpdateGroup.class})
    private Integer round;

    @ApiModelProperty("报价表单内容")
    @TableField(value = "body_json", typeHandler = ListFastjsonTypeHandler.class)
    private LinkedHashMap<String, String> bodyJson;

}
