package com.epcos.bidding.supplier.sign.business.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.annotaion.GetBidder;
import com.epcos.bidding.common.annotaion.GetCommon;
import com.epcos.bidding.common.annotaion.HasFunction;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileBidOpeningDecryptDto;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileBidOpeningSignatureDto;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFilePostDemoVideoDto;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileReleaseAndStampDto;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBiddingApi;
import com.epcos.bidding.supplier.sign.business.api.ISupplierBiddingDaoEvent;
import com.epcos.bidding.supplier.sign.business.api.ISupplierDemoVideoApi;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierDemoVideoDao;
import com.epcos.bidding.supplier.sign.repository.SupplierBiddingMapper;
import com.epcos.common.core.constant.UserConstants;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.system.api.model.FUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.epcos.common.core.enums.admin.FunctionEnum.SUPPLIER_RESPONSE_FILE_ENCRYPTION;

@Slf4j
@Service
@AllArgsConstructor
public class SupplierBiddingService extends ServiceImpl<SupplierBiddingMapper, SupplierBiddingDao> implements ISupplierBiddingApi {

    private final ISupplierDemoVideoApi supplierDemoVideoApi;
    private final ApplicationContext applicationContext;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrUpdate(String zipFileUrl, String pdfFileUrl, String subpackageCode,
                               BidFileJson bidFileJson, Long supplierId) {
        SupplierBiddingDao waitingSaveBidding = Optional.ofNullable(findOne(subpackageCode, supplierId))
                .map(i -> {
                    if (StringUtils.hasText(i.getEpcFile())) {
                        FUtil.delNonArchivedFileByFileKey(i.getEpcFile());
                    }
                    if (StringUtils.hasText(i.getPdfFile())) {
                        FUtil.delFile(i.getPdfFile());
                    }
                    return i;
                }).orElseGet(() -> {
                    SupplierBiddingDao supplierBiddingDao = new SupplierBiddingDao();
                    supplierBiddingDao.setSubpackageCode(subpackageCode);
                    supplierBiddingDao.setSupplierId(supplierId);
                    supplierBiddingDao.setAppName(bidFileJson.getAppName());
                    supplierBiddingDao.setAppVersion(bidFileJson.getVersion());
                    supplierBiddingDao.setZepcKey(bidFileJson.getZepcKey());
                    return supplierBiddingDao;
                }).responseFileUpload(zipFileUrl, pdfFileUrl);
        saveOrUpdate(waitingSaveBidding, updateWrapper(subpackageCode, supplierId));
        return waitingSaveBidding.getId();
    }

    /**
     * 根据标段code查询响应文件信息
     *
     * @param subpackageCode
     * @param supplierId
     * @return
     */
    @Override
    public SupplierBiddingDao selBySubpackageCode(String subpackageCode, Long supplierId) {
        QueryWrapper<SupplierBiddingDao> qw = new QueryWrapper<>();
        qw.lambda().eq(SupplierBiddingDao::getSubpackageCode, subpackageCode)
                .last("limit 1");
        return getOne(qw);
    }


    @Override
    public String findDemoVideoUrl(String subpackageCode, Long supplierId) {
        return Optional.ofNullable(supplierDemoVideoApi.findOne(subpackageCode, supplierId))
                .map(SupplierDemoVideoDao::getVideoUrl)
                .orElse(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String subpackageCode, Long supplierId) {
        if (!StringUtils.hasText(subpackageCode) && Objects.isNull(supplierId)) {
            return;
        }
        Optional.ofNullable(findOne(subpackageCode, supplierId))
                .ifPresent(d -> {
//                    Optional.ofNullable(d.getEpcFile()).ifPresent(FUtil::delFile);
                    Optional.ofNullable(d.getPdfFile()).ifPresent(FUtil::delFile);
                    Optional.ofNullable(d.getEpcFile()).ifPresent(FUtil::delNonArchivedFileByFileKey);


                    applicationContext.publishEvent(new ISupplierBiddingDaoEvent.DeleteEvent(
                            subpackageCode, supplierId));
                    removeById(d.getId());
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releaseAndStamp(AnswerFileReleaseAndStampDto dto, Long supplierId) {
        SupplierBiddingDao dao = new SupplierBiddingDao().releaseStatus(dto.getAnswerFileKey());
        update(dao, updateWrapper(dto.getSubpackageCode(), supplierId));
    }

    @Override
    public void postDemoVideo(AnswerFilePostDemoVideoDto dto, Long supplierId) {
        SupplierDemoVideoDao dao = new SupplierDemoVideoDao(dto.getSubpackageCode(), supplierId);
        dao.setVideoUrl(dto.getVideoUrl());
        supplierDemoVideoApi.save(dao);
    }

    @Override
    public void recallDemoVideo(AnswerFilePostDemoVideoDto dto, Long supplierId) {
        FUtil.delNonArchivedFileByFileKey(dto.getVideoUrl());
        supplierDemoVideoApi.del(dto.getSubpackageCode(), supplierId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bidOpeningDecrypt(AnswerFileBidOpeningDecryptDto dto, Long supplierId) {
        SupplierBiddingDao dao = findOne(dto.getSubpackageCode(), supplierId);
        if (Objects.isNull(dao)) {
            throw new ServiceException("没有响应文件");
        }
        if (!Objects.equals(dao.getAnswerFileKey(), dto.getAnswerFileKey())) {
            throw new ServiceException("响应文件密钥错误");
        }
        update(dao.decrypt(), updateWrapper(dto.getSubpackageCode(), supplierId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreInitialBidOpeningStatus(String subpackageCode, Set<Long> supplierIds) {
        supplierIds.forEach(supplierId -> {
            SupplierBiddingDao dao = new SupplierBiddingDao().restoreInitialBidOpeningStatus();
            update(dao, updateWrapper(subpackageCode, supplierId));
        });
    }

    /**
     * 开标记录文件签字
     * @param dto
     * @param supplierId
     */
    @Override
    @GetBidder(common = @GetCommon(subpackageCodeEL = "#dto.subpackageCode"), supplierIdEL = "#supplierId")
    @Transactional(rollbackFor = Exception.class)
    public void bidOpeningSignature(AnswerFileBidOpeningSignatureDto dto, Long supplierId) {
        // 法人-根据关键字
        FUtil.startSeal(dto.getRecordFile(), GetUtil.getBidder().getCertificateName() + "（签）", String.valueOf(SecurityUtils.getUserId()), dto.getExtrasContents(), UserConstants.LEGAL_PERSON_KEY_SEAL_PARAMETER);

        SupplierBiddingDao dao = new SupplierBiddingDao().signature();
        update(dao, updateWrapper(dto.getSubpackageCode(), supplierId));
    }

    /**
     * 查询供应商是否有解密
     * 1.首先判断当前项目是否有配置响应文件加密功能，
     * 如果有，就校验当前供应商是否有解密，
     * 如果没有，默认全都有解密
     */
    @HasFunction(common = @GetCommon(subpackageCodeEL = "#subpackageCode"), belongRole = "2", functionEnum = SUPPLIER_RESPONSE_FILE_ENCRYPTION)
    public Map<Long, Boolean> queryBidOpeningDecrypt(String subpackageCode, Set<Long> supplierIds) {
        if (Boolean.TRUE.equals(GetUtil.getHasFunction())) {
            return supplierIds.stream()
                    .collect(Collectors.toMap(i -> i, i -> {
                        SupplierBiddingDao one = findOne(subpackageCode, i);
                        if (Objects.isNull(one)) {
                            log.error("当前供应商没有响应文件：supplierId={}, supplierBidding={}", i, one);
                            return false;
                        }
                        return Objects.nonNull(one.getBidOpeningDecrypt()) ? one.getBidOpeningDecrypt() : false;
                    }));
        } else {
            return supplierIds.stream().collect(Collectors.toMap(i -> i, i -> Boolean.TRUE));
        }
    }

}
