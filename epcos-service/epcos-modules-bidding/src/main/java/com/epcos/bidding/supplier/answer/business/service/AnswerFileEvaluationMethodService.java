package com.epcos.bidding.supplier.answer.business.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.EvaluationMethod;
import com.epcos.bidding.common.utils.FileEvaluationMethodRemarkEntityShared;
import com.epcos.bidding.common.utils.ReviewItem;
import com.epcos.bidding.purchase.api.params.EvaluationMethodVo;
import com.epcos.bidding.purchase.api.params.ReviewItemVo;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileEvaluationMethodApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileEvaluationMethodRemarkApi;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileEvaluationMethodDao;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileEvaluationMethodRemarkDao;
import com.epcos.bidding.supplier.answer.domain.dto.PacketUnitDto;
import com.epcos.bidding.supplier.answer.repository.AnswerFileEvaluationMethodMapper;
import com.epcos.bidding.supplier.api.params.SubpackageCodeAndUserIdDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnswerFileEvaluationMethodService extends ServiceImpl<AnswerFileEvaluationMethodMapper, AnswerFileEvaluationMethodDao>
        implements IAnswerFileEvaluationMethodApi {

    private final IAnswerFileEvaluationMethodRemarkApi answerFileEvaluationMethodRemarkApi;

    private String getRemark(String subpackageCode, Long supplierId) {
        return Optional.ofNullable(answerFileEvaluationMethodRemarkApi.findOne(subpackageCode, supplierId))
                .map(FileEvaluationMethodRemarkEntityShared::getRemark)
                .orElse(null);
    }

    @Override
    public EvaluationMethodVo findVo(String subpackageCode, Long supplierId) {
        List<EvaluationMethodVo> vos = getVos(subpackageCode, Collections.singleton(supplierId));
        if (CollUtil.isEmpty(vos)) {
            return null;
        }
        return vos.get(0);
    }

    private List<EvaluationMethodVo> getVos(String subpackageCode, Set<Long> supplierIds) {
        List<AnswerFileEvaluationMethodDao> daos = findIn(subpackageCode, supplierIds);
        if (CollUtil.isEmpty(daos)) {
            return Collections.emptyList();
        }
        return daos.stream()
                .collect(Collectors.groupingBy(i -> new PacketUnitDto(i.getSubpackageCode(), i.getSupplierId())))
                .entrySet().stream()
                .map(e -> {
                    PacketUnitDto k = e.getKey();
                    EvaluationMethodVo vo = new EvaluationMethodVo(k.getSubpackageCode(), getRemark(k.getSubpackageCode(), k.getSupplierId()), k.getSupplierId());
                    List<ReviewItemVo> reviewItems = e.getValue().stream().map(i -> {
                        ReviewItemVo v = new ReviewItemVo();
                        v.setUuid(i.getUuid());
                        v.setScoreChapter(i.convertScoreChapter());
                        return v;
                    }).collect(Collectors.toList());
                    vo.setConformityReview(reviewItems);
                    return vo;
                }).collect(Collectors.toList());
    }


    @Override
    public List<EvaluationMethodVo> findVos(Set<SubpackageCodeAndUserIdDto> dto) {
        return dto.stream().flatMap(i -> getVos(i.getSubpackageCode(), i.getSupplierIds()).stream()).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String subpackageCode, Long userId) {
        if (!StringUtils.hasText(subpackageCode) && Objects.isNull(userId)) {
            return;
        }
        remove(queryWrapper(subpackageCode, userId));
        answerFileEvaluationMethodRemarkApi.remove(answerFileEvaluationMethodRemarkApi.queryWrapper(subpackageCode, userId));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAndCreate(String subpackageCode, Long userId, EvaluationMethod evaluationMethod) {
        delete(subpackageCode, userId);
        List<ReviewItem> revirwList = evaluationMethod.getConformityReview();
        revirwList.addAll(evaluationMethod.getScoreReview());
        List<AnswerFileEvaluationMethodDao> list = revirwList.stream().map(i -> {
            AnswerFileEvaluationMethodDao dao = new AnswerFileEvaluationMethodDao();
            dao.setScoreChapter(dao.convertScoreChapter(i.getScoreChapter()));
            dao.setUuid(i.getUuid());
            dao.setSupplierId(userId);
            dao.setSubpackageCode(subpackageCode);
            return dao;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            saveBatch(list);
        }
        if (CharSequenceUtil.isNotEmpty(evaluationMethod.getRemark())) {
            AnswerFileEvaluationMethodRemarkDao remark = new AnswerFileEvaluationMethodRemarkDao();
            remark.setSupplierId(userId);
            remark.setRemark(evaluationMethod.getRemark());
            remark.setSubpackageCode(subpackageCode);
            answerFileEvaluationMethodRemarkApi.save(remark);
        }
    }
}
