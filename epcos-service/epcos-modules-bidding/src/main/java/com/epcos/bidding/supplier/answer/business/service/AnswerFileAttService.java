package com.epcos.bidding.supplier.answer.business.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.file.*;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileAttApi;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileAttDao;
import com.epcos.bidding.supplier.answer.repository.AnswerFileAttMapper;
import com.epcos.bidding.supplier.api.params.AnswerAttDelDto;
import com.epcos.bidding.supplier.api.params.AnswerAttDelWrapper;
import com.epcos.bidding.supplier.api.params.AnswerAttVo;
import com.epcos.common.core.enums.FileTypeNameEnum;
import com.epcos.epcfile.api.RemoteProjectFileService;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商投标文件附件服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnswerFileAttService extends ServiceImpl<AnswerFileAttMapper, AnswerFileAttDao> implements IAnswerFileAttApi {

    private final AnswerFileAttMapper answerFileAttMapper;
    private final ChunkFileUploadUtil chunkFileUploadUtil;
    private final RemoteProjectFileService remoteProjectFileService;

    @Override
//    @EpcosCache(condition = "#subpackageCode!=null && #subpackageCode!='' && #supplierId!=null",
//            key = "#subpackageCode+':'+#supplierId", table = "answer_file_att", async = true,
//            unless = "#result==null",
//            expire = 4, timeUnit = TimeUnit.HOURS)
    public List<AnswerAttVo> list(String subpackageCode, Long supplierId) {
        return list(new AnswerFileAttDao(subpackageCode, supplierId))
                .stream().map(i -> {
                    AnswerAttVo vo = new AnswerAttVo();
                    BeanUtils.copyProperties(i, vo);
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @EpcosCacheEvict(condition = "#subpackageCode!=null && #subpackageCode!='' && #supplierId!=null",
//            key = "#subpackageCode+':'+#supplierId", table = "answer_file_att")
    public void save(String subpackageCode, Long supplierId, String filename, String url) {
        AnswerFileAttDao dao = new AnswerFileAttDao(subpackageCode, supplierId, url, filename);
        boolean save = save(dao);
        if (!save) {
            log.error("投标附件信息存储失败, dao={}", dao);
            throw new RuntimeException("投标附件信息存储失败");
        }
    }

    /**
     * 处理附件上传完成后的逻辑
     *
     * @param sessionId      会话ID
     * @param filename       文件名
     * @param buyItemCode    采购项目代码
     * @param subpackageCode 标段代码
     * @param userId         用户ID
     * @return 文件URL
     */
    @Override
    @Transactional
//    @EpcosCacheEvict(condition = "#subpackageCode!=null && #subpackageCode!='' && #userId!=null",
//            key = "#subpackageCode+':'+#userId", table = "answer_file_att")
    public String upAtt(String sessionId, String filename, String buyItemCode, String subpackageCode, Long userId) {
        // 创建上传配置
        ProjectFileUploadConfig config = new ProjectFileUploadConfig(FileTypeNameEnum.SUPPLIER_ANSWER_UP_ATT,
                buyItemCode, subpackageCode, userId.toString(), filename);
        // 创建上传处理器（返回String，最后一个切片返回URL）
        ChunkUploadHandler<String> uploadHandler = ChunkUploadHandlers.createProjectFileHandler(remoteProjectFileService, config);
        // 创建结果合并器（带成功回调）
        ChunkResultMerger<String> resultMerger = ChunkUploadHandlers.createProjectFileMerger(
                fileUrl -> {
                    // 成功回调：在当前线程中同步存储到数据库
                    save(new AnswerFileAttDao(subpackageCode, userId, fileUrl, filename));
                    log.error("附件信息已存储到answer_file_att表：fileKey={}, fileName={}", fileUrl, filename);
                },
                errorMsg -> log.error("附件上传失败：{}", errorMsg)
        );
        // 并行上传
        return chunkFileUploadUtil.uploadChunksParallel(sessionId, uploadHandler, resultMerger);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @EpcosCacheEvict(condition = "#subpackageCode!=null && #subpackageCode!='' && #supplierId!=null",
//            key = "#subpackageCode+':'+#supplierId", table = "answer_file_att")
    public void del(String subpackageCode, Long supplierId) {
        List<AnswerFileAttDao> daos = list(new AnswerFileAttDao(subpackageCode, supplierId));
        daos.forEach(d -> FUtil.delFile(d.getFileKey()));
        removeByIds(daos.stream()
                .map(AnswerFileAttDao::getId)
                .collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @EpcosCacheEvict(condition = "#subCodeList!=null && !#subCodeList.isEmpty()",
//            table = "answer_file_att", delTable = true)
    public void del(List<String> subCodeList) {
        if (CollectionUtils.isEmpty(subCodeList)) {
            return;
        }
        List<AnswerFileAttDao> daos = list(queryWrapperIn(subCodeList));
        daos.forEach(d -> FUtil.delFile(d.getFileKey()));
        removeByIds(daos.stream()
                .map(AnswerFileAttDao::getId)
                .collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @EpcosCacheEvict(condition = "#dto.subpackageCode!=null && #dto.subpackageCode!='' && #supplierId!=null",
//            key = "#dto.subpackageCode+':'+#supplierId", table = "answer_file_att")
    public void del(AnswerAttDelWrapper dto, Long supplierId) {
        List<Long> ids = new ArrayList<>(dto.getDtoList().size());
        for (AnswerAttDelDto d : dto.getDtoList()) {
            FUtil.delFile(d.getFileKey());
            ids.add(d.getId());
        }
        removeByIds(ids);
    }

}
