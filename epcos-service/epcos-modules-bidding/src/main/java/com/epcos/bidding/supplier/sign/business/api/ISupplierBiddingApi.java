package com.epcos.bidding.supplier.sign.business.api;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.epcos.bidding.common.IBaseService;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileBidOpeningDecryptDto;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileBidOpeningSignatureDto;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFilePostDemoVideoDto;
import com.epcos.bidding.supplier.answer.domain.dto.AnswerFileReleaseAndStampDto;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;
import org.springframework.util.StringUtils;

import java.util.*;

public interface ISupplierBiddingApi extends IBaseService<SupplierBiddingDao> {

    @Override
    default LambdaQueryWrapper<SupplierBiddingDao> queryWrapper(SupplierBiddingDao dao) {
        LambdaQueryWrapper<SupplierBiddingDao> query = Wrappers.lambdaQuery(SupplierBiddingDao.class);
        if (Objects.nonNull(dao.getId())) {
            return query.eq(SupplierBiddingDao::getId, dao.getId());
        }
        return query
                .eq(StringUtils.hasText(dao.getSubpackageCode()), SupplierBiddingDao::getSubpackageCode, dao.getSubpackageCode())
                .eq(Objects.nonNull(dao.getSupplierId()), SupplierBiddingDao::getSupplierId, dao.getSupplierId())
                .eq(Objects.nonNull(dao.getReleaseStatus()), SupplierBiddingDao::getReleaseStatus, dao.getReleaseStatus())
                .eq(StringUtils.hasText(dao.getAnswerFileKey()), SupplierBiddingDao::getAnswerFileKey, dao.getAnswerFileKey())
                .eq(Objects.nonNull(dao.getBidOpeningDecrypt()), SupplierBiddingDao::getBidOpeningDecrypt, dao.getBidOpeningDecrypt())
                .eq(Objects.nonNull(dao.getBidOpeningRecordForm()), SupplierBiddingDao::getBidOpeningRecordForm, dao.getBidOpeningRecordForm())
                .orderByDesc(SupplierBiddingDao::getId);
    }

    default Wrapper<SupplierBiddingDao> queryWrapperIn(Collection<String> subCodeList) {
        return Wrappers.lambdaQuery(SupplierBiddingDao.class)
                .in(SupplierBiddingDao::getSubpackageCode, subCodeList);
    }

    default LambdaQueryWrapper<SupplierBiddingDao> queryWrapper(String subpackageCode, Long supplierId) {
        return queryWrapperIn(subpackageCode, Collections.singleton(supplierId));
    }

    default LambdaQueryWrapper<SupplierBiddingDao> queryWrapperIn(String subpackageCode, Collection<Long> supplierIdList) {
        return Wrappers.lambdaQuery(SupplierBiddingDao.class)
                .eq(StringUtils.hasText(subpackageCode), SupplierBiddingDao::getSubpackageCode, subpackageCode)
                .in(CollUtil.isNotEmpty(supplierIdList), SupplierBiddingDao::getSupplierId, supplierIdList);
    }

    default LambdaUpdateWrapper<SupplierBiddingDao> updateWrapper(String subpackageCode, Long supplierId) {
        return Wrappers.lambdaUpdate(SupplierBiddingDao.class)
                .eq(StringUtils.hasText(subpackageCode), SupplierBiddingDao::getSubpackageCode, subpackageCode)
                .eq(Objects.nonNull(supplierId), SupplierBiddingDao::getSupplierId, supplierId);
    }

    default SupplierBiddingDao findOne(String subpackageCode, Long supplierId) {
        return getOne(queryWrapper(subpackageCode, supplierId));
    }

    default List<SupplierBiddingDao> findBySubpackageCode(String subpackageCode) {
        return list(queryWrapper(subpackageCode, null));
    }

    default List<SupplierBiddingDao> findBySubpackageCodeAndSupplierIds(String subpackageCode, Collection<Long> supplierIdList) {
        return list(queryWrapperIn(subpackageCode, supplierIdList));
    }

    String findDemoVideoUrl(String subpackageCode, Long supplierId);

    Long createOrUpdate(String zipFileUrl, String pdfFileUrl, String subpackageCode, BidFileJson bidFileJson, Long supplierId);

    /**
     * 根据条件查询响应文件
     *
     * @param subpackageCode       必填
     * @param supplierId
     * @param releaseStatus
     * @param answerFileKey
     * @param bidOpeningDecrypt
     * @param bidOpeningRecordForm
     * @return
     */
    default List<SupplierBiddingDao> find(String subpackageCode, Long supplierId, Integer releaseStatus,
                                          String answerFileKey, Boolean bidOpeningDecrypt, Boolean bidOpeningRecordForm) {
        SupplierBiddingDao dao = new SupplierBiddingDao(subpackageCode);
        dao.setSupplierId(supplierId);
        dao.setReleaseStatus(releaseStatus);
        dao.setAnswerFileKey(answerFileKey);
        dao.setBidOpeningDecrypt(bidOpeningDecrypt);
        dao.setBidOpeningRecordForm(bidOpeningRecordForm);
        return list(queryWrapper(dao));
    }

    /**
     * 根据标段code查询响应文件信息
     *
     * @param subpackageCode
     * @param supplierId
     * @return
     */
    SupplierBiddingDao selBySubpackageCode(String subpackageCode, Long supplierId);

    void delete(String subpackageCode, Long supplierId);

    void releaseAndStamp(AnswerFileReleaseAndStampDto dto, Long supplierId);

    void postDemoVideo(AnswerFilePostDemoVideoDto dto, Long supplierId);

    void recallDemoVideo(AnswerFilePostDemoVideoDto dto, Long supplierId);

    void bidOpeningDecrypt(AnswerFileBidOpeningDecryptDto dto, Long supplierId);

    /**
     * 恢复开标初始状态
     */
    void restoreInitialBidOpeningStatus(String subpackageCode, Set<Long> supplierIds);

    /**
     * 开标记录文件签字
     * @param dto
     * @param supplierId
     */
    void bidOpeningSignature(AnswerFileBidOpeningSignatureDto dto, Long supplierId);

    /**
     * 查询供应商是否有解密
     * 1.首先判断当前项目是否有配置响应文件加密功能，
     * 如果有，就校验当前供应商是否有解密，
     * 如果没有，默认全都有解密
     */
    Map<Long, Boolean> queryBidOpeningDecrypt(String subpackageCode, Set<Long> supplierIds);


}
