package com.epcos.bidding.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import com.epcos.bidding.purchase.api.params.ChapterVo;
import com.epcos.bidding.purchase.api.params.EpcFileContentVo;
import com.epcos.bidding.purchase.api.params.MenuDataVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileDao;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileMenuDao;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileMenuDao;
import com.epcos.bidding.supplier.sign.domain.dao.SupplierBiddingDao;
import com.epcos.common.core.constant.Constants;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.DateUtils;
import com.epcos.common.core.utils.bean.BeanValidators;
import com.epcos.common.file.pdf.Itext7PdfUtil;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.domain.SysDictData;
import com.epcos.system.api.model.FUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.itextpdf.kernel.exceptions.BadPasswordException;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.navigation.PdfDestination;
import com.itextpdf.kernel.pdf.navigation.PdfExplicitDestination;
import com.itextpdf.kernel.utils.PdfMerger;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 采购响应文件工具
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BidFileUtil {

    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    public static final String DATA_JSON = "data.json";
    public static final String CLAIM_FILE_SUFFIX = ".zepc";
    public static final String ANSWER_FILE_SUFFIX = ".tepc";
    public static final String PDF_SUFFIX = ".pdf";
    private static final String[] V_PDF = {"pdf"};
    private static final String[] V910_SUPPORTED_EXTENSIONS = {
            "pdf",
            "png", "jpg", "jpeg",
            "doc", "docx", "xls", "xlsx",
            "dwg"
    };

    static {
        // 设置null值不参与序列化(字段不被显示)
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 禁用空对象转换json校验
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 禁用未知字段反序列化
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 允许类型强制转换
        OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
    }

    /**
     * 武商判断文件类型是否是指定类型
     */
    public static boolean isWhwsSupportedAttachment(String filename) {
        return isSupportedAttachment(getFileExtension(filename), V910_SUPPORTED_EXTENSIONS);
    }

    /**
     * 是否是招标文件 zepc
     */
    public static void isTenderFile(String filename) {
        if (!Objects.equals("zepc", fileSuffix(Objects.requireNonNull(filename)))) {
            throw new ServiceException("请上传正确格式文件");
        }
        validOriginalFilename(filename);
    }

    public static String fileSuffix(String originalFilename) {
        final String[] split = originalFilename.split("\\.");
        return split[split.length - 1].toLowerCase();
    }

    /**
     * 是否是投标文件 tepc
     */
    public static void isBidFile(String filename) {
        if (!Objects.equals("tepc", fileSuffix(Objects.requireNonNull(filename)))) {
            throw new ServiceException("请上传正确格式文件");
        }
        validOriginalFilename(filename);
    }

    /**
     * 检查文件长度
     */
    public static void validOriginalFilename(String originalFilename) {
        if (originalFilename.length() > 150) {
            throw new ServiceException("文件名长度过长（大于150个字符）");
        }
    }

    /**
     * 创建并返回存储路径
     */
    public static Path createTmpDirPath(String sectionBidCode, String userId) {
        final Path tmpDirPath = Paths.get(System.getProperty("java.io.tmpdir") + File.separator +
                String.join("_", sectionBidCode, userId, DateUtils.dateTimeNow()));
        if (Files.notExists(tmpDirPath)) {
            try {
                Files.createDirectories(tmpDirPath);
            } catch (IOException e) {
                log.error("创建临时目录失败，tmpDirPath={}", tmpDirPath, e);
                throw new ServiceException("创建临时目录失败");
            }
        }
        return tmpDirPath;
    }

    /**
     * 优化的存储，解压，解析方法
     */
    public static List<File> checkPdfEncrypted(Path tmpDirPath) {
        List<File> files = findFiles(tmpDirPath.toFile());
        files.removeIf(f -> f.getName().equals(DATA_JSON));
        files.removeIf(f -> f.getName().endsWith(CLAIM_FILE_SUFFIX));
        files.removeIf(f -> f.getName().endsWith(ANSWER_FILE_SUFFIX));
        files.parallelStream()
                .filter(f -> f.getName().toLowerCase().endsWith(PDF_SUFFIX))
                .filter(BidFileUtil::isEncrypted)
                .findAny().ifPresent(f -> {
                    throw new ServiceException(String.format("文件被加密，无法解析。文件名：%s", f.getName()));
                });
        return files;
    }

    // 依据客户端版本校验文件类型
    public static void checkFileType(RemoteSystemService remoteSystemService, String version, List<String> filenames) {
        final String[] vs;
        if (Objects.equals("v9.1.0", version)) {
            // 武商【标书制作客户端v2】v9.1.0 版本修改，招标文件附件，投标文件补充内容章节，
            // 上传文件格式改为png, jpg, jpeg, pdf, doc, docx, xlsx, xls, dwg，未限制文件上传大小
            vs = V910_SUPPORTED_EXTENSIONS;
        } else {
            // 只能是pdf
            vs = V_PDF;
        }
        Optional<String> any = filenames.stream().filter(filename -> {
            if (filename.equals(DATA_JSON)
                    || filename.endsWith(CLAIM_FILE_SUFFIX)
                    || filename.endsWith(ANSWER_FILE_SUFFIX)) {
                return false;
            }
            String ext = getFileExtension(filename);
            return !isSupportedAttachment(ext, vs);
        }).findAny();
        if (any.isPresent()) {
            throw new ServiceException(String.format(
                    "客户端【%s】只能传【%s】格式文件，【%s】不符合",
                    version, String.join(", ", vs), any.get()));
        }
        if (Objects.isNull(remoteSystemService)) {
            return;
        }
        R<List<SysDictData>> dictValueR = remoteSystemService.getDictValue(Constants.SYS_CLIENT_VERSION);
        if (dictValueR.hasFail() || dictValueR.getData() == null) {
            throw new ServiceException(dictValueR.getMsg());
        }
        List<SysDictData> activeVersions = dictValueR.getData().parallelStream()
                .filter(f -> Objects.equals(f.getStatus(), "0"))
                .collect(Collectors.toList());
        if (activeVersions.parallelStream().noneMatch(f -> Objects.equals(f.getDictValue(), version))) {
            throw new ServiceException(String.format("无法上传此客户端版本制作的文件，当前版本：%s, 可用配置版本：%s", version,
                    activeVersions.stream().map(SysDictData::getDictValue).collect(Collectors.joining(", "))));
        }
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 检查是否为支持的附件格式
     */
    public static boolean isSupportedAttachment(String extension, String[] supportedExtensions) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        return Arrays.asList(supportedExtensions).contains(extension.toLowerCase());
    }

    /**
     * 使用更大的缓冲区和优化的内存管理
     */
    private static void extractEntry(ZipArchiveInputStream zis, Path entryPath) throws IOException {
        // 使用更大的缓冲区提升大文件处理性能
        try (BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(entryPath), 1024 * 1024)) {
            byte[] buffer = new byte[512 * 1024];
            int bytesRead;
            while ((bytesRead = zis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
        }
    }


    // 判断文件不存在
    public static boolean isNotExistFile(File file) {
        return Objects.isNull(file)
                || Files.notExists(file.toPath())
                || !file.isFile();
    }

    /**
     * 查询目录中所有的文件,按顺序返回
     */
    public static List<File> findFiles(File file) {
        List<File> files = new ArrayList<>();
        findFiles(files, file);
        return files;
    }

    /**
     * 查询目录中所有的文件
     */
    private static void findFiles(List<File> files, File file) {
        if (!file.exists()) {
            return;
        }
        if (file.isFile()) {
            files.add(file);
        } else {
            final File[] listFiles = file.listFiles();
            if (Objects.nonNull(listFiles)) {
                for (File f : listFiles) {
                    findFiles(files, f);
                }
            }
        }
    }

    public static EpcFileContentVo convert(ClaimsFileDao claimsFileDao, SupplierBiddingDao bidding, String videoUrl) {
        if (Objects.nonNull(claimsFileDao)) {
            return getEpcFileContentVo(claimsFileDao, videoUrl);
        }
        if (Objects.nonNull(bidding)) {
            return getEpcFileContentVo(bidding, videoUrl);
        }
        return null;
    }

    private static EpcFileContentVo getEpcFileContentVo(ClaimsFileDao claimsFileDao, String videoUrl) {
        EpcFileContentVo vo = new EpcFileContentVo();
        vo.setAppName(claimsFileDao.getAppName());
        vo.setVersion(claimsFileDao.getAppVersion());
        vo.setZepcKey(claimsFileDao.getZepcKey());
        vo.setEpcFile(claimsFileDao.getEpcFile());
        vo.setPdfFile(claimsFileDao.getPdfFile());
        vo.setReleaseStatus(claimsFileDao.getReleaseStatus());
        return vo;
    }

    private static EpcFileContentVo getEpcFileContentVo(SupplierBiddingDao bidding, String videoUrl) {
        EpcFileContentVo vo = new EpcFileContentVo();
        vo.setAppName(bidding.getAppName());
        vo.setVersion(bidding.getAppVersion());
        vo.setZepcKey(bidding.getZepcKey());
        vo.setEpcFile(bidding.getEpcFile());
        vo.setPdfFile(bidding.getPdfFile());
        vo.setReleaseStatus(bidding.getReleaseStatus());
        Optional.ofNullable(bidding.getIp()).ifPresent(ip -> vo.setIp(Ipv4Util.longToIpv4(ip)));
        vo.setVideoUrl(videoUrl);
        return vo;
    }

    public static List<MenuDataVo> convertClaimsFileMenu(List<ClaimsFileMenuDao> menus) {
        if (CollUtil.isEmpty(menus)) {
            return Collections.emptyList();
        }
        return menus.stream().filter(f -> Objects.isNull(f.getPid())).map(d -> {
            MenuDataVo menuData = new MenuDataVo();
            menuData.setOrder(d.getOrderNum());
            menuData.setChapterName(d.getChapterName());
            List<ChapterVo> chapterVos = menus.stream().filter(fi -> Objects.nonNull(fi.getPid()) &&
                            Objects.equals(d.getId(), fi.getPid()))
                    .map(c -> {
                        ChapterVo chapterVo = new ChapterVo();
                        chapterVo.setOrder(c.getOrderNum());
                        chapterVo.setSectionName(c.getChapterName());
                        chapterVo.setSectionText(c.getChapterContext());
                        chapterVo.setChapterType(c.getChapterType());
                        if (CharSequenceUtil.isNotEmpty(c.getAttachKey())) {
                            chapterVo.setFormatAttachKey(AttributeUtil.asMap(c.getAttachKey()));
                        }
                        return chapterVo;
                    }).collect(Collectors.toList());
            menuData.setChapterList(chapterVos);
            return menuData;
        }).collect(Collectors.toList());
    }

    public static List<MenuDataVo> convertAnswerFileMenu(List<AnswerFileMenuDao> menus) {
        if (CollUtil.isEmpty(menus)) {
            return Collections.emptyList();
        }
        return menus.stream().filter(f -> Objects.isNull(f.getPid())).map(i -> {
            MenuDataVo menuData = new MenuDataVo();
            menuData.setOrder(i.getOrderNum());
            menuData.setChapterName(i.getChapterName());
            List<ChapterVo> chapterVos = menus.stream()
                    .filter(fi -> Objects.nonNull(fi.getPid()) && Objects.equals(fi.getPid(), i.getId()))
                    .map(c -> {
                        ChapterVo chapterVo = new ChapterVo();
                        chapterVo.setOrder(c.getOrderNum());
                        chapterVo.setSectionName(c.getChapterName());
                        chapterVo.setSectionText(c.getChapterContext());
                        chapterVo.setChapterType(c.getChapterType());
                        if (CharSequenceUtil.isNotEmpty(c.getAttachKey())) {
                            chapterVo.setFormatAttachKey(AttributeUtil.asMap(c.getAttachKey()));
                        }
                        return chapterVo;
                    }).collect(Collectors.toList());
            menuData.setChapterList(chapterVos);
            return menuData;
        }).collect(Collectors.toList());
    }


    /**
     * 直接修改ZIP文件中的data.json，避免完全解压重压缩
     * 适用于只需要修改data.json的场景
     */
    public static BidFileJson rewriteDataJsonInZipDirectly(MultipartFile originalZipFile, PurchaseQuoteFormVo quoteForm,
                                                           String zepcKey, File outputZipFile, List<String> filenames) throws IOException {
        long startTime = System.currentTimeMillis();
        BidFileJson bidFileJson = null;

        try (ZipArchiveInputStream zis = new ZipArchiveInputStream(new BufferedInputStream(originalZipFile.getInputStream(), 2 * 1024 * 1024));
             ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(Files.newOutputStream(outputZipFile.toPath()), 4 * 1024 * 1024))) {
            zos.setLevel(0);
            ArchiveEntry entry;
            byte[] buffer = new byte[2 * 1024 * 1024];
            while ((entry = zis.getNextEntry()) != null) {
                zos.putNextEntry(new ZipEntry(entry.getName()));
                // 处理目录条目：目录不需要内容处理
                if (entry.isDirectory()) {
                    zos.closeEntry();
                    continue;
                }
                // 处理文件条目
                filenames.add(new File(entry.getName()).getName());
                // 修改data.json内容
                if (DATA_JSON.equals(entry.getName())) {
                    String dataJsonStr = toStr(zis);
                    JSONObject jsonObject = new JSONObject(dataJsonStr);
                    if (Objects.nonNull(zepcKey)) {
                        // 设置zepcKey
                        jsonObject.set("zepcKey", zepcKey);
                    }
                    // 如果有报价表信息，则设置报价表相关字段
                    if (Objects.nonNull(quoteForm)) {
                        jsonObject.set("quoteSheetType", 2);
                        jsonObject.set("quoteSheetHeaders", AttributeUtil.convertQuoteSheetHeaders(quoteForm.getHeads()));
                        jsonObject.set("quoteSheet", quoteForm.getBodyMaps());
                    }
                    bidFileJson = OBJECT_MAPPER.readValue(jsonObject.toString(), new TypeReference<BidFileJson>() {
                    });
                    final Map<String, String> validateRes = BeanValidators.validate(bidFileJson);
                    if (!CollectionUtils.isEmpty(validateRes)) {
                        throw new ServiceException("文件不满足数据要求：" + validateRes);
                    }
                    byte[] modifiedData = jsonObject.toString().getBytes(StandardCharsets.UTF_8);
                    zos.write(modifiedData);
                } else {
                    // 直接复制其他文件，使用大缓冲区提升性能
                    int bytesRead;
                    while ((bytesRead = zis.read(buffer)) != -1) {
                        zos.write(buffer, 0, bytesRead);
                    }
                }
                zos.closeEntry();
            }
        }
        log.error("切片ZIP完成，耗时={}毫秒, 文件={}MB", (System.currentTimeMillis() - startTime), outputZipFile.length() / 1024 / 1024);
        return bidFileJson;
    }

    private static String toStr(ZipArchiveInputStream zis) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] jsonBuffer = new byte[8192]; // 重命名避免变量冲突
        int bytesRead;
        while ((bytesRead = zis.read(jsonBuffer)) != -1) {
            baos.write(jsonBuffer, 0, bytesRead);
        }
        return new String(baos.toByteArray(), StandardCharsets.UTF_8);
    }

    /**
     * 解压ZIP中的文件用于PDF处理（排除不需要的文件）
     * 排除：data.json（已通过直接修改ZIP处理）、.zepc文件、.tepc文件
     */
    public static void extractZipFiles(File zipFile, Path targetDir) throws IOException {
        long startTime = System.currentTimeMillis();
        int extractedCount = 0;
        int skippedCount = 0;
        long totalExtractedSize = 0;
        try (ZipArchiveInputStream zis = new ZipArchiveInputStream(new BufferedInputStream(Files.newInputStream(zipFile.toPath()), 2 * 1024 * 1024))) {
            ArchiveEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    continue;
                }
                String fileName = entry.getName();
                // 排除不需要的文件：data.json、.zepc文件、.tepc文件
                if (fileName.equals(DATA_JSON) || fileName.endsWith(CLAIM_FILE_SUFFIX) || fileName.endsWith(ANSWER_FILE_SUFFIX)) {
                    log.error("跳过不需要解压文件: {}", fileName);
                    skippedCount++;
                    continue;
                }
                // 解压所有其他文件
                Path entryPath = targetDir.resolve(fileName).normalize();
                if (!entryPath.startsWith(targetDir)) {
                    throw new ServiceException("不安全的文件路径: " + fileName);
                }
                if (entryPath.getParent() != null) {
                    Files.createDirectories(entryPath.getParent());
                }
                extractEntry(zis, entryPath);
                extractedCount++;
                totalExtractedSize += entry.getSize();
            }
        }
        log.error("解压完成，解压文件数量={}, 跳过文件数量={}, 总大小={}MB, 耗时={}毫秒",
                extractedCount, skippedCount, totalExtractedSize / 1024 / 1024, (System.currentTimeMillis() - startTime));
    }

    // 上传响应文件
    public static List<AnswerFileMenuDao> answerFileMenuDaos(Path dirPath, Long pid, String buyItemCode,
                                                             String subpackageCode, String yearMonthSpilt,
                                                             Long supplierId, MenuData menuData) {
        return menuData.getSectionList().stream().map(s -> {
            AnswerFileMenuDao dao = AnswerFileMenuDao.by(pid, subpackageCode, supplierId, s);
            if (CharSequenceUtil.isNotBlank(s.getAttachKey())) {
                File file = dirPath.resolve(s.getAttachKey()).toFile();
                if (file.exists()) {
                    LinkedHashMap<String, String> fileAndNames = upFileAndReturn(
                            file, buyItemCode, yearMonthSpilt,
                            FileTypeNameConstants.TENDER_DOC_PDF_ENCLOSURE, subpackageCode);
                    dao.setAttachKey(AttributeUtil.asJson(fileAndNames));
                }
            }
            return dao;
        }).collect(Collectors.toList());
    }

    public static LinkedHashMap<String, String> upFileAndReturn(File file, String buyItemCode, String yearMonthSplit,
                                                                String tenderDocPdfEnclosure, String subpackageCode) {
        List<File> files = BidFileUtil.findFiles(file);
        return files.parallelStream().collect(Collectors.toMap(
                f -> FUtil.upFileWithAttachment(
                        buyItemCode,
                        yearMonthSplit,
                        f,
                        tenderDocPdfEnclosure,
                        subpackageCode,
                        null),
                File::getName,
                (s1, s2) -> s2,
                LinkedHashMap::new));
    }


    public static Path mergeParallel(boolean isTender, Path tmpDirPath, BidFileJson bidFileJson, ThreadPoolTaskExecutor taskExecutor) {
        try {
            // 创建章节任务
            List<PdfChapterTask> chapterTasks = createChapterTasks(isTender, tmpDirPath, bidFileJson);
            // 提交章节任务
            List<Future<BidFileOutLine>> futures = submitChapterTasks(chapterTasks, taskExecutor);
            // 收集结果
            List<BidFileOutLine> outLines = collectResults(futures);
            // 合并并添加页码
            return mergeWithPageNumbers(isTender, tmpDirPath, outLines);
        } catch (ServiceException e) {
            log.error("PDF生成与合并失败: isTender={}, tmpDirPath={}", isTender, tmpDirPath, e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("PDF生成与合并异常: isTender={}, tmpDirPath={}", isTender, tmpDirPath, e);
            throw new ServiceException("PDF生成与合并异常: " + e.getMessage());
        }
    }

    // 合并并添加页码
    private static Path mergeWithPageNumbers(boolean isTender, Path tmpDirPath, List<BidFileOutLine> outLines) {
        Path finalPath = tmpDirPath.resolve((isTender ? "采购文件_" : "投标文件_") + DateUtils.dateTimeNow() + ".pdf");
        try (PdfDocument pdfDocument = new PdfDocument(new PdfWriter(finalPath.toFile()))) {
            PdfMerger merger = new PdfMerger(pdfDocument);
            PdfOutline rootOutline = pdfDocument.getOutlines(false);
            // 是否有相同的大标签
            Map<String, PdfOutline> bigTitleOutlines = new LinkedHashMap<>();
            int totalPages = 0;
            // 添加大纲
            for (BidFileOutLine outLine : outLines) {
                if (outLine.getBigTitle() != null) {
                    bigTitleOutlines.computeIfAbsent(outLine.getBigTitle(), rootOutline::addOutline);
                }
            }
            // 合并pdf并记录页码信息
            for (BidFileOutLine outLine : outLines) {
                PdfOutline titleOutline = bigTitleOutlines.get(outLine.getBigTitle());
                PdfOutline title = null;
                if (titleOutline != null && outLine.getTitle() != null) {
                    title = titleOutline.addOutline(outLine.getTitle());
                }
                if (outLine.getFile() != null && outLine.getFile().getName().toLowerCase().endsWith(".pdf")) {
                    try (PdfDocument toMerge = new PdfDocument(new PdfReader(outLine.getFile()))) {
                        clearOutlines(toMerge);
                        int numberOfPages = toMerge.getNumberOfPages();
                        // 合并现有的pdf
                        merger.merge(toMerge, 1, numberOfPages);
                        totalPages += numberOfPages;
                        // 添加书签
                        if (title != null) {
                            title.addDestination(PdfExplicitDestination.createFit(
                                    pdfDocument.getPage(totalPages - numberOfPages + 1)));
                        }
                    }
                }
                // 处理子目录附件（独立于主文件处理）
                if (outLine.getChildList() != null && !outLine.getChildList().isEmpty()) {
                    // 子目录中有附件
                    for (BidFileOutLine childOutline : outLine.getChildList()) {
                        if (childOutline.getFile() == null) {
                            continue;
                        }
                        PdfOutline child = null;
                        // 为子目录添加书签
                        if (title != null && childOutline.getTitle() != null) {
                            child = title.addOutline(childOutline.getTitle());
                        }
                        if (!childOutline.getFile().getName().toLowerCase().endsWith(".pdf")) {
                            log.error("非pdf文件，无法合并，只添加书签。file={}", childOutline.getFile());
                            continue;
                        }
                        try (PdfDocument toMerge = new PdfDocument(new PdfReader(childOutline.getFile()))) {
                            clearOutlines(toMerge);
                            int numberOfPages = toMerge.getNumberOfPages();
                            merger.merge(toMerge, 1, numberOfPages);
                            totalPages += numberOfPages;
                            if (child != null) {
                                child.addDestination(PdfExplicitDestination.createFit(pdfDocument.getPage(totalPages - numberOfPages + 1)));
                            }
                        } catch (Exception e) {
                            log.error("合并pdf异常，mergeFile={}, outline={}", childOutline.getFile().getAbsolutePath(), childOutline, e);
                            throw new ServiceException("合并pdf异常，将该文件：" + childOutline.getFile().getName() + "，截图粘贴至word中重新转成pdf后上传");
                        }
                    }
                }
            }
            PdfOutline cover = bigTitleOutlines.get("封面");
            if (cover != null) {
                cover.addDestination(PdfExplicitDestination.createFit(pdfDocument.getPage(1)));
            }
            // 添加所有页码
            addPageNumbersToPdf(pdfDocument, totalPages);
        } catch (IOException e) {
            log.error("合并并添加页码异常: isTender={}, tmpDirPath={}, finalPath={}, outLines={}",
                    isTender, tmpDirPath, finalPath, outLines, e);
            throw new ServiceException("合并并添加页码异常：" + e.getMessage());
        }
        return finalPath;
    }

    // 清除书签
    private static void clearOutlines(PdfDocument pdfDocument) {
        PdfOutline outlines = pdfDocument.getOutlines(false);
        if (outlines != null) {
            while (!outlines.getAllChildren().isEmpty()) {
                outlines.getAllChildren().get(0).removeOutline();
            }
        }
    }

    // 添加所有页码
    private static void addPageNumbersToPdf(PdfDocument pdfDoc, int totalPages) throws IOException {
        PdfFont pdfFont = Itext7PdfUtil.pdfFont();
        try (Document doc = new Document(pdfDoc)) {
            for (int i = 1; i <= totalPages; i++) {
                String text = String.format("第 %s 页 / 共 %s 页", i, totalPages);
                Rectangle pageSize = pdfDoc.getPage(i).getPageSize();
                doc.showTextAligned(
                        new Paragraph(text).setFont(pdfFont),
                        pageSize.getWidth() / 2,
                        10,
                        i,
                        TextAlignment.CENTER,
                        VerticalAlignment.MIDDLE,
                        0
                );
            }
        }
    }

    private static List<BidFileOutLine> collectResults(List<Future<BidFileOutLine>> futures) {
        List<BidFileOutLine> results = new ArrayList<>();
        int timeOutSeconds = 30;
        for (Future<BidFileOutLine> future : futures) {
            try {
                BidFileOutLine outLine = future.get(timeOutSeconds, TimeUnit.SECONDS);
                if (outLine != null) {
                    results.add(outLine);
                }
            } catch (TimeoutException e) {
                log.error("PDF生成任务超时，超时时间:{}秒。future={}", timeOutSeconds, future, e);
                throw new ServiceException("PDF生成任务超时，请稍候重试");
            } catch (Exception e) {
                log.error("PDF生成任务异常 future={}", future, e);
                throw new ServiceException("PDF生成任务异常：" + e.getMessage());
            }
        }
        return results;
    }

    // 提交章节任务
    private static List<Future<BidFileOutLine>> submitChapterTasks(List<PdfChapterTask> chapterTasks,
                                                                   ThreadPoolTaskExecutor taskExecutor) {
        return chapterTasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return task.call();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, taskExecutor))
                .collect(Collectors.toList());
    }

    // 创建章节任务
    private static List<PdfChapterTask> createChapterTasks(boolean isTender, Path tmpDirPath, BidFileJson bidFileJson) {
        List<PdfChapterTask> tasks = new ArrayList<>();
        List<LinkedHashMap<String, String>> quoteSheetList = bidFileJson.getQuoteSheet();
        for (MenuData menuDatum : bidFileJson.getMenuData()) {
            boolean isCover = Objects.equals(0, menuDatum.getOrder());
            String title = isCover ? menuDatum.getChapterName() : String.format("第%d章：%s", menuDatum.getOrder(), menuDatum.getChapterName());
            for (SectionList se : menuDatum.getSectionList()) {
                String nodeName = isCover ? null : menuDatum.getOrder() + "." + se.getOrder() + "：" + se.getSectionName();
                PdfChapterTask task = new PdfChapterTask();
                task.setCover(isCover);
                task.setTmpDirPath(tmpDirPath);
                task.setQuoteSheetHeaders(bidFileJson.getQuoteSheetHeaders());
                task.setQuoteSheet(quoteSheetList);
                task.setEvaluationMethod(bidFileJson.getEvaluationMethod());
                task.setTender(isTender);
                task.setChapterType(se.getChapterType());
                task.setTitle(title);
                task.setNodeName(nodeName);
                task.setOrder(menuDatum.getOrder());
                task.setSectionText(se.getSectionText());
                task.setAttachKey(se.getAttachKey());
                if (se.getChapterType() == 2) {
                    if (isTender) {
                        // 添加采购文件中的评审方法
//                        task.setOrder(1000);
                        tasks.add(task);
                    }
                } else if (se.getChapterType() == 3) {
                    if (!isTender && !CollectionUtils.isEmpty(bidFileJson.getQuoteSheetHeaders())) {
                        // 添加投标文件中的报价表
//                        task.setOrder(1000);
                        task.setTitle("报价表");
                        task.setNodeName("报价表");
                        tasks.add(task);
                    }
                } else {
                    tasks.add(task);
                }
            }
        }
        if (!CollectionUtils.isEmpty(quoteSheetList)) {
            // 外面有报价表
            PdfChapterTask task = new PdfChapterTask();
            task.setTmpDirPath(tmpDirPath);
            task.setQuoteSheetHeaders(bidFileJson.getQuoteSheetHeaders());
            task.setQuoteSheet(quoteSheetList);
            task.setTender(isTender);
            task.setChapterType(3);
            task.setTitle("报价表");
            task.setNodeName("报价表");
            tasks.add(task);
        }
        return tasks;
    }


    /**
     * 构建一个html标签
     */
    public static String buildHtmlTag(boolean center, String tagName, String content) {
        return String.format("<%s %s>%s</%s>",
                tagName,
                center ? "style=\"text-align: center;\"" : "",
                content,
                tagName);
    }

    /**
     * 判断一个pdf是否加密
     * true: 有权限，无法被修改
     */
    public static boolean isEncrypted(File pdfFile) {
        try (PdfReader pdfReader = new PdfReader(pdfFile)) {
            try (PdfDocument pdfDocument = new PdfDocument(pdfReader)) {
                return pdfReader.isEncrypted();
            }
        } catch (BadPasswordException e) {
            log.error("pdf打开需要密码，无法进行相关操作，pdfFile={}", pdfFile);
            return true;
        } catch (IOException e) {
            log.error("判断pdf是否加密异常，pdfFile={}", pdfFile, e);
            throw new ServiceException("判断pdf是否加密异常");
        }
    }

    public static void mergePdfFilesWithBookmarks(List<BidFileOutLine> outlines, Path outputPath) {
        try (PdfDocument pdfDocument = new PdfDocument(new PdfWriter(outputPath.toFile()))) {
            PdfMerger merger = new PdfMerger(pdfDocument);
            PdfOutline rootOutline = pdfDocument.getOutlines(false);
            // 是否有相同的大标签
            Map<String, PdfOutline> bigTitleOutlines = new HashMap<>();
            int i = 0;
            for (BidFileOutLine outline : outlines) {
                // 跳过bigTitle为null的项，这些应该只作为子项被处理
                if (outline.getBigTitle() == null) {
                    continue;
                }
                // 检查是否已经为这个大标题创建了书签
                PdfOutline bigTitleOutline = bigTitleOutlines.get(outline.getBigTitle());
                if (bigTitleOutline == null) {
                    // 如果没有，创建一个新的大标题书签
                    bigTitleOutline = rootOutline.addOutline(outline.getBigTitle());
                    bigTitleOutline.addDestination(
                            PdfDestination.makeDestination(new PdfString(String.format("bigTitle_%02d", i++))));
                    bigTitleOutlines.put(outline.getBigTitle(), bigTitleOutline);
                }
                if (outline.getFile() != null) {
                    if (!outline.getFile().getName().toLowerCase().endsWith(".pdf")) {
                        log.error("主目录，不是pdf文件，无法合并，file={}", outline.getFile());
                        continue;
                    }
                    // 合并大标签下的pdf，并添加书签
                    try (PdfDocument toMerge = new PdfDocument(new PdfReader(outline.getFile().getAbsolutePath()))) {
                        int numberOfPages = toMerge.getNumberOfPages();
                        merger.merge(toMerge, 1, numberOfPages);
                        if (Objects.nonNull(outline.getTitle())) {
                            PdfOutline titleOutline = bigTitleOutline.addOutline(outline.getTitle());
                            titleOutline.addDestination(PdfExplicitDestination.createFit(
                                    pdfDocument.getPage(pdfDocument.getNumberOfPages() - numberOfPages + 1)));
                            // 递归处理子标题
                            if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                                addOutlinesToPdf(outline.getChildList(), merger, titleOutline, pdfDocument);
                            }
                        }
                    } catch (com.itextpdf.io.exceptions.IOException e) {
                        log.error("合并pdf并添加书签异常，mergeFile={}, outline={}", outline.getFile().getAbsolutePath(), outline,
                                e);
                        throw new ServiceException("合并pdf并添加书签异常：" + outline.getFile().getName());
                    }
                } else {
                    // 当主文件不存在或不是PDF时，仍需处理子目录附件
                    if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                        PdfOutline titleOutline = bigTitleOutline.addOutline(outline.getTitle());
                        addOutlinesToPdf(outline.getChildList(), merger, titleOutline, pdfDocument);
                    }
                }
            }
        } catch (IOException e) {
            log.error("合并pdf并添加书签异常，outlines={}, outputPath={}", outlines, outputPath, e);
            throw new ServiceException(e.getMessage());
        }
    }

    private static void addOutlinesToPdf(List<BidFileOutLine> outlines, PdfMerger merger, PdfOutline parentOutline,
                                         PdfDocument pdfDocument) throws IOException {
        for (BidFileOutLine outline : outlines) {
            if (outline.getFile() != null) {
                String fileName = outline.getFile().getName();
                String fileExtension = getFileExtension(fileName).toLowerCase();
                if ("pdf".equals(fileExtension)) {
                    // 处理PDF文件 - 原有逻辑
                    handlePdfFile(outline, merger, parentOutline, pdfDocument);
                } else if (isSupportedAttachment(fileExtension, V910_SUPPORTED_EXTENSIONS)) {
                    // 处理非PDF文件 - 作为附件嵌入
                    handleAttachmentFile(outline, parentOutline, pdfDocument);
                } else {
                    log.error("不支持的文件格式，跳过处理: {}", fileName);
                }
            } else if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                // 当没有主文件时，仍需处理子目录附件
                PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
                addOutlinesToPdf(outline.getChildList(), merger, titleOutline, pdfDocument);
            }
        }
    }

    /**
     * 处理PDF文件合并
     */
    private static void handlePdfFile(BidFileOutLine outline, PdfMerger merger,
                                      PdfOutline parentOutline, PdfDocument pdfDocument) throws IOException {
        try (PdfDocument toMerge = new PdfDocument(new PdfReader(outline.getFile().getAbsolutePath()))) {
            int numberOfPages = toMerge.getNumberOfPages();
            try {
                merger.merge(toMerge, 1, numberOfPages);
            } catch (UnsupportedOperationException e) {
                log.error("无法合并的文件={}，页数={}", outline.getFile(), numberOfPages);
                throw new ServiceException(String.format("无法合并【%s】-页数【%d】", outline.getFile().getName(), numberOfPages));
            }
            PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
            titleOutline.addDestination(PdfExplicitDestination.createFit(
                    pdfDocument.getPage(pdfDocument.getNumberOfPages() - numberOfPages + 1)));
            if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                addOutlinesToPdf(outline.getChildList(), merger, titleOutline, pdfDocument);
            }
        }
    }

    /**
     * 处理非PDF文件作为附件嵌入
     */
    private static void handleAttachmentFile(BidFileOutLine outline, PdfOutline parentOutline, PdfDocument pdfDocument) {
        File file = outline.getFile();
        String fileName = file.getName();
        try {
            // 检查文件大小，决定处理策略
            long fileSize = file.length();
//            byte[] fileContent;
//            // 30MB的文件使用分块读取
//            if (fileSize > 30 * 1024 * 1024) {
//                fileContent = readFileInChunks(file);
//            } else {
//                fileContent = Files.readAllBytes(file.toPath());
//            }
//            // 创建文件附件规范 - 使用字节数组而不是文件路径
//            PdfFileSpec fileSpec = PdfFileSpec.createEmbeddedFileSpec(pdfDocument, fileContent, fileName, fileName, null);
//            // 将附件添加到PDF文档
//            pdfDocument.addFileAttachment(fileName, fileSpec);
            // 创建书签并指向附件信息页面
            PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
            int currentPageNumber = pdfDocument.getNumberOfPages();
            titleOutline.addDestination(PdfExplicitDestination.createFit(pdfDocument.getPage(currentPageNumber)));
            log.error("成功将文件作为附件嵌入PDF并创建信息页面: {} ({}MB)", fileName, fileSize / 1024 / 1024);
            // 递归处理子项
            if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                addOutlinesToPdf(outline.getChildList(), null, titleOutline, pdfDocument);
            }
        } catch (OutOfMemoryError e) {
            log.error("内存不足，无法嵌入大文件: {} ({}MB)", fileName, file.length() / (1024 * 1024));
            throw new ServiceException(String.format("内存不足，无法嵌入大文件: %s", fileName));
        } catch (Exception e) {
            log.error("嵌入附件失败: {}", fileName, e);
            throw new ServiceException(String.format("嵌入附件失败: %s", fileName));
        }
    }

    /**
     * 优化版的递归处理子标题方法，保持与原方法相同的逻辑
     */
    private static void addOutlinesToPdfOptimized(List<BidFileOutLine> outlines, PdfMerger merger,
                                                  PdfOutline parentOutline, PdfDocument pdfDocument) throws IOException {
        for (BidFileOutLine outline : outlines) {
            if (outline.getFile() != null) {
                String fileName = outline.getFile().getName();
                String fileExtension = getFileExtension(fileName).toLowerCase();
                if ("pdf".equals(fileExtension)) {
                    // 处理PDF文件 - 使用优化逻辑
                    handlePdfFileOptimized(outline, merger, parentOutline, pdfDocument);
                } else if (isSupportedAttachment(fileExtension, V910_SUPPORTED_EXTENSIONS)) {
                    // 处理非PDF文件 - 作为附件书签（与原方法保持一致）
                    handleAttachmentFileOptimized(outline, parentOutline, pdfDocument);
                } else {
                    log.warn("不支持的文件格式，跳过处理: {}", fileName);
                }
            } else if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
                addOutlinesToPdfOptimized(outline.getChildList(), merger, titleOutline, pdfDocument);
            }
        }
    }

    /**
     * 优化版PDF文件处理方法
     */
    private static void handlePdfFileOptimized(BidFileOutLine outline, PdfMerger merger,
                                               PdfOutline parentOutline, PdfDocument pdfDocument) throws IOException {
        // 根据文件大小选择处理策略
        if (outline.getFile().length() < 50 * 1024 * 1024) { // 50MB以下
            try (PdfDocument toMerge = new PdfDocument(new PdfReader(outline.getFile().getAbsolutePath()))) {
                int numberOfPages = toMerge.getNumberOfPages();
                try {
                    merger.merge(toMerge, 1, numberOfPages);
                } catch (UnsupportedOperationException e) {
                    log.error("无法合并的文件={}，页数={}", outline.getFile(), numberOfPages);
                    throw new ServiceException(String.format("无法合并【%s】-页数【%d】", outline.getFile().getName(), numberOfPages));
                }
                PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
                titleOutline.addDestination(PdfExplicitDestination.createFit(
                        pdfDocument.getPage(pdfDocument.getNumberOfPages() - numberOfPages + 1)));
                if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                    addOutlinesToPdfOptimized(outline.getChildList(), merger, titleOutline, pdfDocument);
                }
            }
        } else { // 大文件特殊处理
            System.gc(); // 处理前垃圾回收
            try (PdfDocument toMerge = new PdfDocument(new PdfReader(outline.getFile().getAbsolutePath()))) {
                int numberOfPages = toMerge.getNumberOfPages();
                try {
                    merger.merge(toMerge, 1, numberOfPages);
                } catch (UnsupportedOperationException e) {
                    log.error("无法合并的大文件={}，页数={}", outline.getFile(), numberOfPages);
                    throw new ServiceException(String.format("无法合并【%s】-页数【%d】", outline.getFile().getName(), numberOfPages));
                }
                PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
                titleOutline.addDestination(PdfExplicitDestination.createFit(
                        pdfDocument.getPage(pdfDocument.getNumberOfPages() - numberOfPages + 1)));
                if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                    addOutlinesToPdfOptimized(outline.getChildList(), merger, titleOutline, pdfDocument);
                }
            }
            System.gc(); // 处理后垃圾回收
        }
    }

    /**
     * 优化版非PDF附件处理方法，保持与原方法相同的书签创建逻辑
     */
    private static void handleAttachmentFileOptimized(BidFileOutLine outline, PdfOutline parentOutline, PdfDocument pdfDocument) {
        File file = outline.getFile();
        String fileName = file.getName();
        try {
            // 创建书签并指向当前页面（与原方法保持一致）
            PdfOutline titleOutline = parentOutline.addOutline(outline.getTitle());
            int currentPageNumber = pdfDocument.getNumberOfPages();
            if (currentPageNumber > 0) {
                titleOutline.addDestination(PdfExplicitDestination.createFit(pdfDocument.getPage(currentPageNumber)));
            }
            // 递归处理子项（与原方法保持一致）
            if (outline.getChildList() != null && !outline.getChildList().isEmpty()) {
                addOutlinesToPdfOptimized(outline.getChildList(), null, titleOutline, pdfDocument);
            }
        } catch (OutOfMemoryError e) {
            log.error("内存不足，无法处理大文件: {} ({}MB)", fileName, file.length() / (1024 * 1024));
            throw new ServiceException(String.format("内存不足，无法处理大文件: %s", fileName));
        } catch (Exception e) {
            log.error("处理非PDF附件失败: {}", fileName, e);
            throw new ServiceException(String.format("处理附件失败: %s", fileName));
        }
    }


    /**
     * 异步上传文件
     */
    public static CompletableFuture<String> uploadFileAsync(java.util.function.Supplier<String> uploadTask,
                                                            ThreadPoolTaskExecutor taskExecutor,
                                                            String errorMsg, File file) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                long startTime = System.currentTimeMillis();
                String result = uploadTask.get();
                log.error("文件上传成功: file={}, fileSize={}MB, 耗时={}ms",
                        file.getName(), file.length() / 1024 / 1024, System.currentTimeMillis() - startTime);
                return result;
            } catch (Exception e) {
                log.error("异步上传文件异常 errorMsg={}, file={}", errorMsg, file, e);
                throw new ServiceException(errorMsg + ": " + e.getMessage());
            }
        }, taskExecutor);
    }


}
