package com.epcos.bidding.supplier.sign.domain.dao;

import cn.hutool.core.net.Ipv4Util;
import com.baomidou.mybatisplus.annotation.TableName;
import com.epcos.bidding.common.SubpackageCodeAndSupplierIdEntity;
import com.epcos.common.core.utils.ServletUtils;
import com.epcos.common.core.utils.bean.BeanValidators;
import com.epcos.common.core.utils.ip.IpUtils;
import com.epcos.common.core.web.validator.CreateGroup;
import com.epcos.common.core.web.validator.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "响应文件")
@NoArgsConstructor
@TableName("supplier_bidding")
public class SupplierBiddingDao extends SubpackageCodeAndSupplierIdEntity {

    public SupplierBiddingDao(String subpackageCode) {
        this.subpackageCode = subpackageCode;
    }

    public SupplierBiddingDao(String subpackageCode, Long supplierId) {
        this.subpackageCode = subpackageCode;
        this.supplierId = supplierId;
    }

    @NotBlank(message = "响应文件压缩包url,必填", groups = CreateGroup.class)
    @Length(max = 100, message = "响应文件压缩包url【最长：100】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "响应文件url")
    private String epcFile;

    @NotBlank(message = "响应文件url,必填", groups = CreateGroup.class)
    @Length(max = 100, message = "响应文件url【最长：100】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "响应文件url")
    private String pdfFile;

    @NotNull(message = "响应文件上传时ip,必填", groups = CreateGroup.class)
    @ApiModelProperty(value = "响应文件上传时ip")
    private Long ip;

    @NotNull(message = "0已上传 1已确认", groups = CreateGroup.class)
    @Range(max = 1, message = "0已上传 1已确认", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "0已上传 1已确认")
    private Integer releaseStatus;

    @Length(max = 100, message = "响应文件密钥【最长：100】", groups = {CreateGroup.class, UpdateGroup.class})
    @ApiModelProperty(value = "响应文件密钥")
    private String answerFileKey;

    @ApiModelProperty(value = "响应文件递交盖章时间")
    private Date submissionTime;

    @ApiModelProperty(value = "开标解密，0-未解密，1-解密")
    private Boolean bidOpeningDecrypt;

    @ApiModelProperty(value = "开标记录表，0-未签字，1-已签字")
    private Boolean bidOpeningRecordForm;

    @NotBlank(message = "名称，必填")
    @Length(max = 100, message = "名称【最长：100】")
    @ApiModelProperty(value = "名称")
    private String appName;

    @NotBlank(message = "版本，必填")
    @Length(max = 100, message = "版本【最长：100】")
    @ApiModelProperty(value = "版本")
    private String appVersion;

    @NotBlank(message = "文件验证key，必填")
    @Length(max = 100, message = "文件验证key【最长：100】")
    @ApiModelProperty(value = "文件验证key")
    private String zepcKey;

    /**
     * 恢复初始开标时状态
     */
    public SupplierBiddingDao restoreInitialBidOpeningStatus() {
        Optional.ofNullable(this.getBidOpeningDecrypt()).ifPresent(f -> this.setBidOpeningDecrypt(false));
        Optional.ofNullable(this.getBidOpeningRecordForm()).ifPresent(f -> this.setBidOpeningRecordForm(false));
        return this;
    }

    /**
     * 响应文件上传
     */
    public SupplierBiddingDao responseFileUpload(String zipFileUrl, String pdfFileUrl) {
        this.setEpcFile(zipFileUrl);
        this.setPdfFile(pdfFileUrl);
        this.setReleaseStatus(0);
        this.setIp(Ipv4Util.ipv4ToLong(IpUtils.getIpAddr(ServletUtils.getRequest())));
        return this;
    }

    /**
     * 响应文件 1 已确认
     */
    public SupplierBiddingDao releaseStatus(String answerFileKey) {
        Optional.ofNullable(answerFileKey).ifPresent(this::setAnswerFileKey);
        this.setReleaseStatus(1);
        this.setSubmissionTime(new Date());
        return this;
    }


    /**
     * 开标解密
     */
    public SupplierBiddingDao decrypt() {
        this.setBidOpeningDecrypt(true);
        return this;
    }

    /**
     * 签名
     */
    public SupplierBiddingDao signature() {
        this.setBidOpeningRecordForm(true);
        return this;
    }

    // 校验未发布
    public SupplierBiddingDao verifyNotPublished() {
        return BeanValidators.verifyField(this, getReleaseStatus(), i -> i != 1, "投标（响应）文件未提交");
    }

    // 校验文件已发布
    public SupplierBiddingDao verifyPublished() {
        return BeanValidators.verifyField(this, getReleaseStatus(), i -> i == 1, "投标（响应）文件已提交");
    }

    // 校验已解密
    public SupplierBiddingDao verifyDecrypted() {
        return BeanValidators.verifyField(this, getBidOpeningDecrypt(), Boolean.TRUE::equals, "已解密");
    }

    // 校验未解密
    public SupplierBiddingDao verifyNotDecrypted() {
        return BeanValidators.verifyField(this, getBidOpeningDecrypt(), Boolean.FALSE::equals, "未解密");
    }

    // 校验已签字
    public SupplierBiddingDao verifySigned() {
        return BeanValidators.verifyField(this, getBidOpeningRecordForm(), Boolean.TRUE::equals, "已签字");
    }


}
