package com.epcos.bidding.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.PathUtil;
import cn.hutool.json.JSONUtil;
import com.epcos.bidding.audit.api.AuditAttribute;
import com.epcos.bidding.audit.api.BizAndAudit;
import com.epcos.bidding.audit.api.BizAuditRelationDto;
import com.epcos.bidding.audit.api.dto.AuditCreatorDto;
import com.epcos.bidding.biz.business.api.IBizAuditRelationApi;
import com.epcos.bidding.common.AuditTypeEnum;
import com.epcos.bidding.common.PageSortEntity;
import com.epcos.bidding.common.annotaion.*;
import com.epcos.bidding.common.aspects.GetUtil;
import com.epcos.bidding.common.aspects.filter.validator.ClaimsFileIfNotNullAndPublishedValidator;
import com.epcos.bidding.common.aspects.filter.validator.ClaimsFileNotExistsValidator;
import com.epcos.bidding.common.aspects.filter.validator.ClaimsFilePublishedValidator;
import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.common.utils.BidFileJson;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.common.utils.ReviewItem;
import com.epcos.bidding.purchase.api.domian.cliams.ClaimsFileReleaseAndStampDto;
import com.epcos.bidding.purchase.api.params.*;
import com.epcos.bidding.purchase.api.params.dto.BuyItemAndSubPackageDto;
import com.epcos.bidding.purchase.api.params.dto.QuoteFormCreatorDto;
import com.epcos.bidding.purchase.api.params.vo.buyitem.BuyItemVo;
import com.epcos.bidding.purchase.api.params.vo.buyitem.ItemSubpackageVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileApi;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileEvaluationMethodApi;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileQuoteFormApi;
import com.epcos.bidding.purchase.claims.business.service.ClaimsService;
import com.epcos.bidding.purchase.claims.domain.dao.ClaimsFileAttDao;
import com.epcos.bidding.purchase.claims.domain.dto.*;
import com.epcos.bidding.purchase.claims.domain.vo.ClaimsFilePageVo;
import com.epcos.bidding.purchase.project.base.business.api.ISubPackageApi;
import com.epcos.common.core.constant.Constants;
import com.epcos.common.core.constant.TokenConstants;
import com.epcos.common.core.domain.AuditStatusDto;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.enums.ClientEnum;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.common.core.utils.SecurityUtils;
import com.epcos.common.core.utils.ServletUtils;
import com.epcos.common.core.utils.poi.ExcelUtil;
import com.epcos.common.core.utils.sign.RSAUtil;
import com.epcos.common.core.utils.uuid.IdUtils;
import com.epcos.common.core.web.page.TableDataVo;
import com.epcos.common.log.annotation.Log;
import com.epcos.common.log.enums.BusinessType;
import com.epcos.common.redis.annotation.RedisLock;
import com.epcos.common.redis.chunk.ChunkCompleteContext;
import com.epcos.common.redis.chunk.ChunkFileHelper;
import com.epcos.common.redis.chunk.ChunkUploadResponse;
import com.epcos.common.security.annotation.InnerAuth;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.epcos.dingtalk.api.RemoteDingtalk;
import com.epcos.dingtalk.domain.dto.PurchaseFileProcessDto;
import com.epcos.system.api.RemoteSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "采购人采购文件")
@RequiredArgsConstructor
@RestController
@RequestMapping("/purchase/claims")
public class ClaimsFileController {

    private final ClaimsService claimsService;
    private final IClaimsFileApi claimsFileApi;
    private final IClaimsFileEvaluationMethodApi claimsFileEvaluationMethodApi;
    private final IClaimsFileQuoteFormApi claimsFileQuoteFormApi;
    private final ISubPackageApi subPackageApi;
    private final IBizAuditRelationApi auditRelationApi;
    private final RemoteSystemService remoteSystemService;
    private final IBizAuditRelationApi bizAuditRelationApi;
    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Value("${environment}")
    private String environment;
    private final RemoteDingtalk remoteDingtalk;
    private final ChunkFileHelper chunkFileHelper;
    private final ThreadPoolTaskExecutor taskExecutor;


    /**
     * 上传pdf采购文件
     *
     * @param file
     * @param buyItemCode
     * @param subpackageCode
     * @return
     */
    @NotNullUserId
    @RedisLock
    @Log(title = "上传pdf采购文件", businessType = BusinessType.IMPORT)
    @RequiresPermissions("process:purchaseFile:upload")
    @ApiOperation("上传pdf采购文件")
    @PostMapping("/uploadPDF")
    public R<Boolean> uploadPDF(@RequestPart("file") MultipartFile file,
                                @RequestParam("buyItemCode") String buyItemCode,
                                @RequestParam("subpackageCode") String subpackageCode) {
        return claimsService.uploadToPDF(file, buyItemCode, subpackageCode);
    }


    @NotNullUserId
    @RedisLock
    @Log(title = "上传评审项", businessType = BusinessType.INSERT)
    @RequiresPermissions("process:purchaseFile:upload")
    @ApiOperation("上传评审项")
    @PostMapping("/uploadEvaluation")
    public R<Boolean> uploadEvaluation(@RequestBody @Valid ClaimsFileEvaluationMethodDto dto) {
        return claimsService.uploadEvaluation(dto);
    }

    @NotNullUserId
    @Log(title = "采购人上传附件", businessType = BusinessType.INSERT)
    @RequiresPermissions("process:purchaseFile:upload")
    @ApiOperation("采购人上传附件")
    @PostMapping("/answer/up/att")
    public R<Boolean> upAtt(@RequestPart("file") MultipartFile file,
                            @RequestParam("resumableIdentifier") String resumableIdentifier,
                            @RequestParam("filename") String filename,
                            @RequestParam("buyItemCode") String buyItemCode,
                            @RequestParam("subpackageCode") String subpackageCode,
                            @RequestParam("chunkNumber") Integer chunkNumber,
                            @RequestParam("totalChunks") Integer totalChunks) {
        return claimsService.upAtt(file, resumableIdentifier, filename, buyItemCode, subpackageCode, chunkNumber, totalChunks);
    }

    @NotNullUserId
    @RequiresPermissions("process:purchaseFile:upload")
    @ApiOperation("查询附件列表")
    @GetMapping("/answer/att")
    public R<List<ClaimsFileAttDao>> getAtt(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(claimsService.getAtt(subpackageCode));
    }

    @NotNullUserId
    @Log(title = "删除附件", businessType = BusinessType.DELETE)
    @RequiresPermissions("process:purchaseFile:upload")
    @RedisLock(second = 60 * 10)
    @ApiOperation("删除附件")
    @GetMapping("/answer/att/del")
    public R<Boolean> attDel(@RequestParam("ids") List<Long> ids) {
        return claimsService.attDel(ids);
    }


    // 检查文件最大不能超过 154*10MB = 1.5gb
    @Log(title = "采购文件切片上传,每10M一个切片", businessType = BusinessType.IMPORT)
    @ApiOperation("采购文件切片上传,每10M一个切片")
    @PostMapping("/up")
    public R<ChunkUploadResponse> up(@RequestPart("file") @NotNull(message = "文件-file-参数必填") MultipartFile file,
                                     @RequestParam(value = "filename") @NotBlank(message = "文件名-filename-参数必填") String filename,
                                     @RequestParam("buyItemCode") @NotBlank(message = "项目编码-buyItemCode-参数必填") String buyItemCode,
                                     @RequestParam("subpackageCode") @NotBlank(message = "标段编码-subpackageCode-参数必填") String subpackageCode,
                                     @RequestParam(value = "chunkNumber") @NotNull(message = "切片下标-chunkNumber-参数必填") Integer chunkNumber,
                                     @RequestParam(value = "totalChunks") @NotNull(message = "切片数量-totalChunks-参数必填") Integer totalChunks,
                                     @RequestParam(value = "aesKey") @NotBlank(message = "aesKey-参数必填") String aesKey) {
        if (totalChunks > 154) {
            return R.fail("文件过大，超过1.5G，切片总数量" + totalChunks + "片/10MB");
        }
        String privateKey = ServletUtils.getHeader(ServletUtils.getRequest(), TokenConstants.REQUEST_RSA_KEY);
        if (!StringUtils.hasText(privateKey)) {
            return R.fail("rsa-privateKey-必填");
        }
        String decryptedAesKey = RSAUtil.decrypted(aesKey, privateKey, false);
        String sessionId = chunkFileHelper.getSessionId(subpackageCode, SecurityUtils.getUserId(), null);
        try {
            chunkFileHelper.upload(file, sessionId, filename, chunkNumber, totalChunks, true, decryptedAesKey);
            ChunkUploadResponse response = new ChunkUploadResponse();
            response.setSessionId(sessionId);
            response.setChunkNumber(chunkNumber);
            response.setTotalChunks(totalChunks);
            response.setCompleted(chunkFileHelper.hasAllUpload(sessionId, totalChunks));
            response.setCompletedChunks(chunkFileHelper.getUploadCount(sessionId));
            response.calculateProgress();
            return R.ok(response);
        } catch (Exception e) {
            log.error("采购文件切片上传异常：chunkNumber={}, totalChunks={}, buyItemCode={}, subpackageCode={}, filename={}",
                    chunkNumber, totalChunks, buyItemCode, subpackageCode, filename, e);
            return R.fail(e.getMessage());
        }
    }

    @Log(title = "采购文件合并解析", businessType = BusinessType.INSERT)
    @RedisLock(second = 60 * 10, keyEl = "#dto.subpackageCode")
    @GetItem(belongRole = "1", common = @GetCommon(async = true, buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @GetClaimsFile(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = ClaimsFileIfNotNullAndPublishedValidator.class))
    @GetPurchaseQuoteForm(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @ApiOperation("采购文件合并解析")
    @PostMapping("/complete")
    public R<Boolean> complete(@RequestBody @Valid BuyItemAndSubPackageDto dto) {
        String sessionId = chunkFileHelper.getSessionId(dto.getSubpackageCode(), SecurityUtils.getUserId(), null);
        try {
            chunkFileHelper.complete(sessionId, context -> {
                handleTenderFile(context, dto.getBuyItemCode(), dto.getSubpackageCode());
                return null;
            });
            return R.ok();
        } catch (Exception e) {
            log.error("采购文件合并解析异常：dto={}", dto, e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 处理文件完成
     */
    private void handleTenderFile(ChunkCompleteContext context, String buyItemCode, String subpackageCode) {
//        if (context.getTotalSize() > Integer.MAX_VALUE - 500 * 1024 * 1024) {
//            throw new ServiceException("文件过大，超过1.5GB");
//        }
        String originalFilename = context.getOriginalFilename().trim();
        BidFileUtil.isTenderFile(originalFilename);
        Path tmpDirPath = BidFileUtil.createTmpDirPath(subpackageCode, SecurityUtils.getUserId().toString());
        String zepcKey = IdUtils.fastSimpleUUID();
        PurchaseQuoteFormVo quoteForm = GetUtil.getPurchaserQuoteForm();
        try {
            // 直接修改ZIP中的data.json（设置zepcKey + 可选的报价表信息）
            File outputZipFile = tmpDirPath.resolve(originalFilename.substring(0, originalFilename.lastIndexOf('.')) + "_modify.zepc").toFile();
            List<String> filenames = new ArrayList<>();
            BidFileJson bidFileJson = BidFileUtil.rewriteDataJsonInZipDirectly(context.getStreamedFile(), quoteForm, zepcKey, outputZipFile, filenames);
            if (bidFileJson == null) {
                log.error("采购文件中没有 data.json 文件。 tmpDirPath={}", tmpDirPath);
                throw new ServiceException("采购文件不满足要求");
            }
            BuyItemVo buyItemVo = GetUtil.getItemVo().getBuyItemVo();
            String yearMonthSplit = buyItemVo.getYearMonthSplit();
            if (!StringUtils.hasText(yearMonthSplit)) {
                log.error("查询项目创建时间年月为空，异常。buyItemCode={}, subpackageCode={}", buyItemCode, subpackageCode);
                throw new ServiceException("查询项目创建时间年月为空");
            }
            // 检查内容
            BidFileUtil.checkFileType(remoteSystemService, bidFileJson.getVersion(), filenames);
            checkEvaluationMethod(bidFileJson, buyItemVo);
            // 抽取压缩包中的文件，排除采购文件与投标文件压缩包
            BidFileUtil.extractZipFiles(outputZipFile, tmpDirPath);
            // 检查PDF加密和文件类型
            BidFileUtil.checkPdfEncrypted(tmpDirPath);
            // 生成合并PDF
            File mergedPdf = BidFileUtil.mergeParallel(true, tmpDirPath, bidFileJson, taskExecutor).toFile();
            claimsService.up(outputZipFile, mergedPdf, yearMonthSplit, bidFileJson, buyItemCode, subpackageCode);
        } catch (Exception e) {
            log.error("采购文件解析异常：context={}, buyItemCode={}, subpackageCode={}", context, buyItemCode, subpackageCode, e);
            throw new ServiceException(e.getMessage());
        } finally {
            Optional.of(tmpDirPath).ifPresent(PathUtil::del);
        }
    }

    /**
     * 校验 合格制评审 打分制评审
     */
    private void checkEvaluationMethod(BidFileJson bidFileJson, BuyItemVo buyItemVo) {
        // 合格制评审
        buyItemVo.getFunctionKVList().parallelStream()
                .filter(f -> Objects.equals(Constants.PURCHASER_CONFORMITY_SYSTEM, f.getPurchaseFunctionKey()))
                .findAny().ifPresent(i -> {
                    List<ReviewItem> conformityReview = bidFileJson.getEvaluationMethod().getConformityReview();
                    if (CollUtil.isEmpty(conformityReview)) {
                        throw new ServiceException("合格制评审存在于功能中，必须存在");
                    }
                });
        // 打分制评审
        buyItemVo.getFunctionKVList().parallelStream()
                .filter(f -> Objects.equals(Constants.PURCHASER_SCORING_SYSTEM, f.getPurchaseFunctionKey()))
                .findAny()
                .ifPresent(i -> {
                    List<ReviewItem> scoreReview = bidFileJson.getEvaluationMethod().getScoreReview();
                    if (CollUtil.isEmpty(scoreReview)) {
                        throw new ServiceException("打分制评审存在于功能中，必须存在");
                    }
                });
    }

    @Log(title = "采购文件确认发布，盖章", businessType = BusinessType.UPDATE)
    @RedisLock(second = 30)
    @ApiOperation("采购文件确认发布，盖章")
    @GetSimpleItem(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"))
    @GetClaimsFile(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode", afters = {
            ClaimsFileNotExistsValidator.class, ClaimsFilePublishedValidator.class}))
    @GetBizAndAudit(common = @GetCommon(buyItemCodeEL = "#dto.buyItemCode", subpackageCodeEL = "#dto.subpackageCode"),
            auditType = AuditTypeEnum.CLAIMS_FILE, ignoreField = {"subpackageCode", "orgCode"})
    @PostMapping("/releaseAndStamp")
    public R<Boolean> releaseAndStamp(@RequestBody @Valid ClaimsFileReleaseAndStampDto dto) {
        if (Objects.nonNull(dto.getAuditProcessDto())) {
            BizAndAudit bizAndAudit = GetUtil.getBizAndAudit();
            if (environment.equals(ClientEnum.XK.getCode()) || activeProfile.equals(ClientEnum.XK.getCode())) {
                String purchaseFileProcessInstance = remoteDingtalk.createPurchaseFileProcessInstance(
                        new PurchaseFileProcessDto(dto.getAuditProcessDto().getAuditTitle(), dto.getPdfFile()));
                bizAndAudit.getBizAuditRelation().setBusinessType(purchaseFileProcessInstance);
            }
            AuditCreatorDto auditCreatorDto = addEvaluationMethodAndAtt(bizAndAudit.getAuditCreator());
            auditRelationApi.initiateAndRecord(bizAndAudit.getBizAuditRelation(), auditCreatorDto);
        } else {
            claimsFileApi.updateReleaseStatus(dto);
        }
        return R.ok(Boolean.TRUE);
    }


    /**
     * 将评审项和评审附件添加到评审内容中
     *
     * @param dto
     * @return
     */
    public AuditCreatorDto addEvaluationMethodAndAtt(AuditCreatorDto dto) {

        EpcFileContentVo epcFileContentVo = claimsService.query(dto.getAuditInfoDto().getSubpackageCode());
        if (epcFileContentVo != null && !"web".equals(epcFileContentVo.getAppName())) {
            return dto;
        }
        List<AuditAttribute> contentList = dto.getAuditInfoDto().getContentList();
        EvaluationMethodVo evaluation = claimsService.getEvaluation(dto.getAuditInfoDto().getSubpackageCode());
        List<ClaimsFileAttDao> att = claimsService.getAtt(dto.getAuditInfoDto().getSubpackageCode());

        if (evaluation != null && evaluation.getConformityReview() != null) {
            AuditAttribute auditAttribute = new AuditAttribute();
            auditAttribute.setKey("conformityReview");
            auditAttribute.setName("符合性评审");
            auditAttribute.setType("json");
            String json = JSONUtil.toJsonStr(evaluation.getConformityReview());
            auditAttribute.setValue(json);
            contentList.add(auditAttribute);
        }
        if (evaluation != null && evaluation.getScoreReview() != null) {
            AuditAttribute auditAttribute = new AuditAttribute();
            auditAttribute.setKey("scoreReview");
            auditAttribute.setName("技术商务打分");
            auditAttribute.setType("json");
            String json = JSONUtil.toJsonStr(evaluation.getScoreReview());
            auditAttribute.setValue(json);
            contentList.add(auditAttribute);
        }
        if (att != null && !att.isEmpty()) {
            AuditAttribute auditAttribute = new AuditAttribute();
            auditAttribute.setKey("attList");
            auditAttribute.setName("附件列表");
            auditAttribute.setType("json");
            List<Map<String, String>> resultMap = att.stream().map(item -> {
                Map<String, String> map = new HashMap<>();
                map.put("name", item.getFileName());
                map.put("url", item.getFileKey());
                return map;
            }).collect(Collectors.toList());

            String json = JSONUtil.toJsonStr(resultMap);
            auditAttribute.setValue(json);
            contentList.add(auditAttribute);
        }
        dto.getAuditInfoDto().setContentList(contentList);
        return dto;
    }

    @Log(title = "钉钉-审批-修改采购文件审批状态")
    @InnerAuth
    @ApiOperation("钉钉修改采购文件审批状态")
    @PostMapping("/updateAuditStatus")
    public R<Boolean> updateAuditStatus(@RequestBody AuditStatusDto dto) {
        claimsFileApi.updateAuditStatus(dto);
        return R.ok(Boolean.TRUE);
    }

    @NotNullUserId
    @ApiOperation("采购文件查询")
    @GetMapping("")
    public R<List<ItemSubpackageVo<EpcFileContentVo>>> query(@RequestParam("buyItemCode") @NotBlank(message = "采购项目编码必填") String buyItemCode) throws InterruptedException {
        return R.ok(subPackageApi.findByBuyItemCode(buyItemCode).stream()
                .map(i -> {
                    ItemSubpackageVo<EpcFileContentVo> vo = new ItemSubpackageVo<>(i.getSubpackageCode(),
                            i.getSubpackageName());
                    EpcFileContentVo epcFileContentVo = Optional
                            .ofNullable(claimsFileApi.findBySubpackageCodeVo(i.getSubpackageCode()))
                            .orElse(new EpcFileContentVo());
                    epcFileContentVo.setPriceTotalSource(i.getPriceTotalSource());

                    List<ClaimsFileAttDao> att = claimsService.getAtt(i.getSubpackageCode());
                    if (att != null && !att.isEmpty()) {
                        epcFileContentVo.setClaimsFileAttVoList(att.stream()
                                .map(a -> {
                                    ClaimsFileAttVo claimsFileAttVo = new ClaimsFileAttVo();
                                    claimsFileAttVo.setFileKey(a.getFileKey());
                                    claimsFileAttVo.setFileName(a.getFileName());
                                    claimsFileAttVo.setId(a.getId());
                                    return claimsFileAttVo;
                                })
                                .collect(Collectors.toList()));
                    }
                    EvaluationMethodVo evaluation = claimsService.getEvaluation(i.getSubpackageCode());
                    epcFileContentVo.setEvaluationMethod(evaluation);

                    vo.setData(epcFileContentVo);
                    Optional.ofNullable(
                                    bizAuditRelationApi.queryAuditVo(
                                            BizAuditRelationDto.builder()
                                                    .auditType(AuditTypeEnum.CLAIMS_FILE.getType())
                                                    .subpackageCode(i.getSubpackageCode())
                                                    // .userId(SecurityUtils.getUserId())
                                                    .build()))
                            .ifPresent(a -> vo.setAuditProcessDto(a.convert()));


                    return vo;
                }).collect(Collectors.toList()));
    }

    @ApiOperation("查询当前项目下除了传入subpackageCode的所有采购文件")
    @PostMapping("/page")
    public TableDataVo<ClaimsFilePageVo> page(@RequestBody @Valid PageSortEntity<ClaimsFilePageDto> dto) {
        return claimsService.page(dto);
    }

    @Log(title = "引入其它包的采购文件", businessType = BusinessType.INSERT)
    @RedisLock(second = 30)
    @ApiOperation("引入其它包的采购文件")
    @PostMapping("/importOtherFile")
    public R<Boolean> importOtherFile(@RequestBody @Valid ClaimsFileImportDto dto) {
        if (!claimsService.areQuoteEqual(dto.getImportSubpackageCode(), dto.getSubpackageCode())) {
            throw new ServiceException("引入的采购文件包含报价项不一致，无法引入");
        }
        claimsService.importOtherFile(dto);
        return R.ok();
    }

    @Log(title = "将采购文件应用到其他包", businessType = BusinessType.INSERT)
    @RedisLock(second = 30)
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("将采购文件应用到其他包")
    @PostMapping("/applyToOtherPackages")
    public R<Boolean> applyToOtherPackages(@RequestBody @Valid ClaimsFileApplyDto dto) {
        for (String applyCode : dto.getApplySubpackageCodeList()) {
            if (!claimsService.areQuoteEqual(dto.getSubpackageCode(), applyCode)) {
                throw new ServiceException("引入的采购文件包含报价项不一致，无法引入");
            }
        }
        dto.getApplySubpackageCodeList()
                .stream()
                .map(applyCode -> new ClaimsFileImportDto(dto.getSubpackageCode(), applyCode))
                .forEach(claimsService::importOtherFile);
        return R.ok();
    }

    @InnerAuth
    @ApiOperation("采购文件所有内容查询")
    @GetMapping("/remote/queryAll")
    public R<EpcFileContentVo> queryAll(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(claimsService.queryAll(subpackageCode));
    }

    @InnerAuth
    @ApiOperation("采购文件评审办法,单包code查询")
    @GetMapping("/remote/evaluationMethod")
    public R<EvaluationMethodVo> evaluationMethod(@RequestParam("subpackageCode") String subpackageCode) {
        return R.ok(claimsFileEvaluationMethodApi.findBySubpackageCodeVo(subpackageCode));
    }

    @InnerAuth
    @ApiOperation("采购文件评审办法,多包code查询")
    @GetMapping("/remote/evaluationMethods")
    public R<List<EvaluationMethodVo>> evaluationMethods(@RequestParam("subpackageCodes") String[] subpackageCodes) {
        return R.ok(Arrays.stream(subpackageCodes).map(claimsFileEvaluationMethodApi::findBySubpackageCodeVo)
                .collect(Collectors.toList()));
    }

    @InnerAuth
    @ApiOperation("采购文件报价表单,使用多包code查询")
    @GetMapping("/remote/quoteForms")
    public R<Map<String, PurchaseQuoteFormVo>> quoteForms(@RequestParam("subpackageCodes") String[] subpackageCodes) {
        return R.ok(claimsFileQuoteFormApi.list(Arrays.stream(subpackageCodes).collect(Collectors.toSet())));
    }

    @ApiOperation("采购文件报价表单,分页查询")
    @PostMapping("/quoteForm/page")
    public TableDataVo<PurchaseQuoteFormVo> quoteFormPage(
            @RequestBody @Valid PageSortEntity<ClaimsFileQuoteFormQueryDto> pageSort) {
        return claimsFileQuoteFormApi.queryPage(pageSort);
    }

    @RedisLock(second = 30)
    @Log(title = "采购文件报价表单,保存", businessType = BusinessType.INSERT)
    @ApiOperation("采购文件报价表单,保存")
    @PostMapping("/quoteForm/create")
    public R<Boolean> create(@RequestBody @Valid QuoteFormCreatorDto creator) {
        claimsFileQuoteFormApi.createOrUpdate(creator);
        return R.ok(Boolean.TRUE);
    }

    @RedisLock(second = 30)
    @Log(title = "采购文件报价表单,删除报价项", businessType = BusinessType.DELETE)
    @ApiOperation("采购文件报价表单,删除报价项")
    @PostMapping("/quoteForm/del")
    public R<Boolean> dels(@RequestBody @NotEmpty Set<Long> ids) {
        return R.ok(claimsFileQuoteFormApi.delByIds(ids));
    }

    @RedisLock(second = 30)
    @Log(title = "采购文件报价表单,报价表excel导入解析", businessType = BusinessType.IMPORT)
    @ApiOperation("采购文件报价表单,报价表excel导入解析")
    @PostMapping("/quoteForm/importParser")
    public R<PurchaseQuoteFormVo> importParser(@RequestPart("file") MultipartFile file,
                                               @RequestParam("headsJson") String headsJson) {
        try {
            List<AttributeVo> heads = AttributeUtil.asHead(headsJson);
            ExcelUtil<LinkedHashMap<Integer, String>> excelUtil = new ExcelUtil(LinkedHashMap.class);
            List<LinkedHashMap<Integer, String>> rows = excelUtil.importExcel(file.getInputStream());
            if (CollectionUtils.isEmpty(rows) || rows.size() == 1) {
                return R.fail("excel中无数据");
            }
            if (rows.stream().anyMatch(r -> r.size() < heads.size())) {
                return R.fail("excel列数 < 选择列，无法解析");
            }
            LinkedHashMap<Integer, String> excelHead = rows.get(0);
            rows.remove(0);
            List<LinkedHashMap<String, String>> res = new ArrayList<>(rows.size());
            for (LinkedHashMap<Integer, String> row : rows) {
                LinkedHashMap<String, String> r = new LinkedHashMap<>(heads.size());
                for (int i = 0; i < heads.size(); i++) {
                    String titleKeyName = excelHead.get(i);
                    Optional<AttributeVo> first = heads.stream()
                            .filter(f -> Objects.equals(f.getKeyName(), titleKeyName)).findFirst();
                    if (!first.isPresent()) {
                        return R.fail(String.format("excel表头【%s】,无法对应选择的列【%s】", titleKeyName,
                                heads.stream().map(AttributeVo::getKeyName).collect(Collectors.joining(" - "))));
                    }
                    String value = row.get(i);
                    AttributeUtil.validRequiredAndRegex(first.get(), value);
                    r.put(first.get().getKeyVal(), value);
                }
                res.add(r);
            }
            return R.ok(new PurchaseQuoteFormVo(heads, res));
        } catch (Exception e) {
            log.error("报价表excel导入解析，异常，file={}, headsJson={}", file, headsJson, e);
            return R.fail("报价表excel导入解析，异常");
        }
    }

}
