package com.epcos.bidding.supplier.answer.business.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.AttributeUtil;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.common.utils.MenuData;
import com.epcos.bidding.purchase.api.params.MenuDataVo;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileMenuApi;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileMenuDao;
import com.epcos.bidding.supplier.answer.repository.AnswerFileMenuMapper;
import com.epcos.system.api.model.FUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.nio.file.Path;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnswerFileMenuService extends ServiceImpl<AnswerFileMenuMapper, AnswerFileMenuDao> implements IAnswerFileMenuApi {

    @Override
    public List<MenuDataVo> findVo(String subpackageCode, Long supplierId) {
        return BidFileUtil.convertAnswerFileMenu(getSelf().find(subpackageCode, supplierId));
    }

    private AnswerFileMenuService getSelf() {
        return (AnswerFileMenuService) AopContext.currentProxy();
    }

    @Override
//    @EpcosCache(condition = "#subpackageCode!=null && #subpackageCode!='' && #supplierId!=null",
//            key = "#subpackageCode + ':' + #supplierId", table = "answer_file_menu",
//            unless = "#result==null", async = true, expire = 4, timeUnit = TimeUnit.HOURS)
    public List<AnswerFileMenuDao> find(String subpackageCode, Long supplierId) {
        return list(queryWrapper(subpackageCode, supplierId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @EpcosCacheEvict(condition = "#subpackageCode!=null && #subpackageCode!='' && #supplierId!=null",
//            key = "#subpackageCode + ':' + #supplierId", table = "answer_file_menu")
    public void delete(String subpackageCode, Long supplierId) {
        if (!StringUtils.hasText(subpackageCode) && Objects.isNull(supplierId)) {
            return;
        }
        List<AnswerFileMenuDao> daos = find(subpackageCode, supplierId);
        findAttachKeysBySubpackageCode(daos)
                .forEach(FUtil::delNonArchivedFileByFileKey);
        remove(queryWrapper(subpackageCode, supplierId));
    }

    @Transactional(rollbackFor = Exception.class)
//    @EpcosCacheEvict(condition = "#subpackageCodeList!=null && !#subpackageCodeList.isEmpty()",
//            table = "answer_file_menu", delTable = true)
    public void delete(List<String> subpackageCodeList) {
        if (CollectionUtils.isEmpty(subpackageCodeList)) {
            return;
        }
        Wrapper<AnswerFileMenuDao> answerFileMenuDaoWrapper = queryWrapperIn(subpackageCodeList);
        List<AnswerFileMenuDao> list = list(answerFileMenuDaoWrapper);
        findAttachKeysBySubpackageCode(list)
                .forEach(FUtil::delNonArchivedFileByFileKey);
        remove(answerFileMenuDaoWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAttachFileAndCreate(Path tmpDirPath, String buyItemCode, String subpackageCode,
                                       String yearMonthSplit, Long supplierId, List<MenuData> menuDataList) {
        getSelf().delete(subpackageCode, supplierId);
        for (MenuData m : menuDataList) {
            AnswerFileMenuDao answerFileMenuDao = new AnswerFileMenuDao(
                    subpackageCode, supplierId, m.getOrder(), m.getChapterName());
            save(answerFileMenuDao);
            Long pid = answerFileMenuDao.getId();
            List<AnswerFileMenuDao> children = BidFileUtil.answerFileMenuDaos(
                    tmpDirPath, pid, buyItemCode, subpackageCode, yearMonthSplit, supplierId, m);
            if (CollUtil.isNotEmpty(children)) {
                saveBatch(children);
            }
        }
    }

    // 查询目录中文件keys
    private Set<String> findAttachKeysBySubpackageCode(List<AnswerFileMenuDao> daos) {
        return daos.stream()
                .filter(f -> CharSequenceUtil.isNotEmpty(f.getAttachKey()))
                .map(i ->
                        AttributeUtil.asMap(i.getAttachKey())
                ).flatMap(m -> m.keySet().stream())
                .collect(Collectors.toSet());
    }


}
