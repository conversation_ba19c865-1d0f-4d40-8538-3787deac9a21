package com.epcos.bidding.supplier.answer.business.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.epcos.bidding.common.utils.BidFileUtil;
import com.epcos.bidding.purchase.api.params.AttributeVo;
import com.epcos.bidding.purchase.api.params.PurchaseQuoteFormVo;
import com.epcos.bidding.purchase.claims.business.api.IClaimsFileQuoteFormApi;
import com.epcos.bidding.supplier.answer.business.api.IAnswerFileQuoteFormApi;
import com.epcos.bidding.supplier.answer.domain.dao.AnswerFileQuoteFormDao;
import com.epcos.bidding.supplier.answer.repository.AnswerFileQuoteFormMapper;
import com.epcos.bidding.supplier.api.params.*;
import com.epcos.common.core.constant.Constants;
import com.epcos.common.core.constant.FileTypeNameConstants;
import com.epcos.common.core.domain.R;
import com.epcos.common.core.exception.ServiceException;
import com.epcos.system.api.RemoteSystemService;
import com.epcos.system.api.domain.SysDictData;
import com.epcos.system.api.model.FUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Path;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class AnswerFileQuoteFormService extends ServiceImpl<AnswerFileQuoteFormMapper, AnswerFileQuoteFormDao> implements IAnswerFileQuoteFormApi {

    private final IClaimsFileQuoteFormApi claimsFileQuoteFormApi;
    private final RemoteSystemService remoteSystemService;

    /**
     * @param quoteFormVo
     * @param subpackageCode
     * @param supplierIds
     * @param roundList
     * @param quoteSheet     新报价表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(PurchaseQuoteFormVo quoteFormVo, String subpackageCode,
                       Collection<Long> supplierIds, Collection<Integer> roundList,
                       List<LinkedHashMap<String, String>> quoteSheet) {
        if (!StringUtils.hasText(subpackageCode) && CollectionUtils.isEmpty(supplierIds)) {
            return;
        }
        PurchaseQuoteFormVo purchaseQuoteFormVo = Optional.ofNullable(quoteFormVo)
                .orElseGet(() -> claimsFileQuoteFormApi.query(subpackageCode));
        if (purchaseQuoteFormVo != null) {
            Set<Long> ids = new HashSet<>();
            for (AnswerFileQuoteFormDao dao : finds(subpackageCode, supplierIds, roundList)) {
                // 2025-4-7 钉钉号： 202503261757000476409 第2点要求
                delQuoteFormFile(purchaseQuoteFormVo.getHeads(), dao, quoteSheet);
                ids.add(dao.getId());
            }
            if (!CollectionUtils.isEmpty(ids)) {
                removeBatchByIds(ids);
            }
        }
    }

    // 删除报价表中的文件
    // 需要对比新传入的报价表和数据库中的报价表，删除数据库中存在但新传入的报价表中不存在的文件
    private void delQuoteFormFile(List<AttributeVo> heads, AnswerFileQuoteFormDao dao, List<LinkedHashMap<String, String>> quoteSheet) {
        if (!CollectionUtils.isEmpty(heads) && Objects.nonNull(dao) && !CollectionUtils.isEmpty(dao.getBodyJson())) {
            // 找到新提交中的文件url,避免在旧数据中删除与新数据中相同url的文件
            List<AttributeVo> fileFieldHeadList = heads.stream().filter(f -> "file".equals(f.getKeyType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(fileFieldHeadList)) {
                return;
            }
//            Map<String, List<String>> fileFieldMap = findCreateFileField(fileFieldHeadList, quoteSheet);
            dao.getBodyJson().forEach((k, v) -> {
                if (StringUtils.hasText(v)) {
                    fileFieldHeadList.stream()
                            .filter(f -> Objects.equals(f.getKeyVal(), k))
                            .findAny()
                            .ifPresent(head -> {
                                FUtil.delFile(v);
                            });

//                    heads.stream()
//                            .filter(f -> "file".equals(f.getKeyType())
//                                    && Objects.equals(f.getKeyVal(), k))
//                            .findAny()
//                            .ifPresent(head -> {
//                                if (fileFieldMap.get(k) != null && !fileFieldMap.get(k).contains(v)) {
//                                    FUtil.delFile(v);
//                                }
//                            });
                }
            });
        }
    }

    // 多行中的文件
    private Map<String, List<String>> findCreateFileField(List<AttributeVo> fileFieldHeadList, List<LinkedHashMap<String, String>> quoteSheet) {
        if (CollectionUtils.isEmpty(quoteSheet)) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> fileFieldMap = new HashMap<>();
        // 遍历每一行数据
        for (LinkedHashMap<String, String> row : quoteSheet) {
            // 遍历文件字段头列表
            for (AttributeVo fileField : fileFieldHeadList) {
                String keyVal = fileField.getKeyVal();
                String fileUrl = row.get(keyVal);
                if (StringUtils.hasText(fileUrl)) {
                    fileFieldMap.computeIfAbsent(keyVal, k -> new ArrayList<>()).add(fileUrl);
                }
            }
        }
        return fileFieldMap;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Path tmpDirPath, PurchaseQuoteFormVo quoteFormVo,
                       String buyItemCode, String subpackageCode, String yearMonthSplit,
                       Long supplierId, Integer round, List<LinkedHashMap<String, String>> bodyList) {
        if (Objects.isNull(supplierId)
                || Objects.isNull(round)
                || !StringUtils.hasText(subpackageCode)
                || CollectionUtils.isEmpty(bodyList)) {
            return;
        }
        PurchaseQuoteFormVo purchaseQuoteFormVo = Optional.ofNullable(quoteFormVo).orElseGet(() ->
                claimsFileQuoteFormApi.query(subpackageCode));
        boolean isFileUp = Objects.nonNull(tmpDirPath) && tmpDirPath.toFile().exists();
        List<AnswerFileQuoteFormDao> list = bodyList.stream().map(row -> {
            AnswerFileQuoteFormDao dao = new AnswerFileQuoteFormDao(subpackageCode, supplierId, round);
            if (isFileUp) {
                uploadQuotationTableAttachment(tmpDirPath, buyItemCode, yearMonthSplit,
                        purchaseQuoteFormVo, subpackageCode, supplierId, row);
            }
            dao.setBodyJson(row);
            return dao;
        }).collect(Collectors.toList());
        saveBatch(list);
        // 提交文件-删除临时文件
        List<String> rowFileUrlList = findRowFileUrlList(purchaseQuoteFormVo.getHeads(), list);
        FUtil.commitFile(rowFileUrlList.toArray(new String[0]));
    }

    // 找到所有行内的文件url
    private List<String> findRowFileUrlList(List<AttributeVo> heads, List<AnswerFileQuoteFormDao> list) {
        List<AttributeVo> fileHeadList = heads.stream().filter(f -> "file".equals(f.getKeyType())).collect(Collectors.toList());
        return list.stream().map(i ->
                        fileHeadList.stream()
                                .filter(f -> StringUtils.hasText(i.getBodyJson().get(f.getKeyVal())))
                                .map(fh -> i.getBodyJson().get(fh.getKeyVal()))
                                .collect(Collectors.toList())
                ).flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    // 响应文件中的报价表附件上传
    private void uploadQuotationTableAttachment(Path tmpDirPath, String buyItemCode, String yearMonthSplit,
                                                PurchaseQuoteFormVo quoteFormVo, String subpackageCode, Long supplierId,
                                                LinkedHashMap<String, String> row) {
        row.forEach((k, v) -> quoteFormVo.getHeads()
                .stream()
                .filter(f ->
                        StringUtils.hasText(v)
                                && "file".equals(f.getKeyType())
                                && Objects.equals(f.getKeyVal(), k))
                .findAny().ifPresent(f -> {
                    File file = tmpDirPath.resolve(v).toFile();
                    if (!BidFileUtil.isNotExistFile(file) && !BidFileUtil.isEncrypted(file)) {
                        String url = FUtil.upFile(
                                buyItemCode, yearMonthSplit, file,
                                FileTypeNameConstants.TENDER_DOC_PDF_QUOTE_FORM,
                                subpackageCode, supplierId);
                        row.put(k, url);
                    } else {
                        log.error("报价表附件文件不存在, k={}, v={}, file={}, row={}", k, v, file, f);
                        throw new ServiceException("报价表【" + f.getKeyName() + "】附件文件不存在：" + v);
                    }
                }));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAndCreate(Path tmpDirPath, PurchaseQuoteFormVo quoteFormVo,
                             String buyItemCode, String subpackageCode, String yearMonthSplit,
                             Long supplierId, Integer round, List<LinkedHashMap<String, String>> quoteSheet) {
        delete(quoteFormVo, subpackageCode, Collections.singleton(supplierId),
                round != null ? Collections.singletonList(round) : null, quoteSheet);
        create(tmpDirPath, quoteFormVo, buyItemCode, subpackageCode,
                yearMonthSplit, supplierId, round, quoteSheet);
    }

    @Override
    public List<MultiSupplierQuoteFormVo> findVos(Set<SubpackageCodeAndUserIdAndRoundDto> dto) {
        return dto.stream()
                .map(i -> getMultiSupplierQuoteFormVo(i.getSubpackageCode(), i.getSupplierIds(), i.getRound()))
                .collect(Collectors.toList());
    }

    @Override
    public MultiSupplierQuoteFormVo findVo(String subpackageCode, Set<Long> supplierIds, Integer round) {
        return getMultiSupplierQuoteFormVo(subpackageCode, supplierIds, round);
    }

    //    @EpcosCache(condition = "#subpackageCode!=null && #subpackageCode!=''",
//            key = "#subpackageCode+(#round!=null ? ':'+#round : '')+(#supplierId!=null ? ':'+#supplierId : '')",
//            table = "answer_file_quote_form", expire = 5, timeUnit = TimeUnit.HOURS,
//            unless = "#result==null", async = true)
    public List<AnswerFileQuoteFormDao> find(String subpackageCode, Long supplierId, Integer round) {
        return list(queryWrapper(subpackageCode, supplierId, round));
    }

    public List<AnswerFileQuoteFormDao> finds(String subpackageCode, Collection<Long> supplierIds, Collection<Integer> rounds) {
        List<AnswerFileQuoteFormDao> result = new ArrayList<>();
        List<Integer> roundList = getQueryList(rounds);
        List<Long> supplieList = getQueryList(supplierIds);
        for (Integer round : roundList) {
            for (Long supplierId : supplieList) {
                result.addAll(getSelf().find(subpackageCode, supplierId, round));
            }
        }
        return result;
    }

    private <T> List<T> getQueryList(Collection<T> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return Collections.singletonList(null);
        }
        return new ArrayList<>(collection)
                .stream()
                .distinct()
                .collect(Collectors.toList());
    }

    private MultiSupplierQuoteFormVo getMultiSupplierQuoteFormVo(String subpackageCode, Set<Long> supplierIds, Integer round) {
        if (CharSequenceUtil.isEmpty(subpackageCode)) {
            return null;
        }
        MultiSupplierQuoteFormVo vo = new MultiSupplierQuoteFormVo();
        vo.setPurchaseQuoteForm(claimsFileQuoteFormApi.query(subpackageCode));
        List<AnswerFileQuoteFormDao> list = finds(subpackageCode, supplierIds, round != null ? Collections.singletonList(round) : null);
        Map<Long, TreeMap<Integer, List<AnswerFileQuoteFormDao>>> groupSortMap = list.stream().collect(
                Collectors.groupingBy(s -> s.getSupplierId(), Collectors.mapping(e -> e, Collectors.groupingBy(
                        AnswerFileQuoteFormDao::getRound, TreeMap::new,
                        Collectors.collectingAndThen(Collectors.toList(), this::sortRound)))));
        List<SupplierQuoteFormVo> quoteFormVOList = groupSortMap.keySet().stream().map(k -> {
            SupplierQuoteFormVo v = new SupplierQuoteFormVo(k);
            List<RoundQuoteFormVo> roundQuoteFormVoList = groupSortMap.get(k)
                    .keySet()
                    .stream()
                    .map(rk -> {
                        List<AnswerFileQuoteFormDao> answerFileQuoteFormDaos = groupSortMap.get(k).get(rk);
                        List<LinkedHashMap<String, String>> rows = answerFileQuoteFormDaos.stream()
                                .map(AnswerFileQuoteFormDao::getBodyJson).collect(Collectors.toList());
                        RoundQuoteFormVo roundQuoteFormVo = new RoundQuoteFormVo(rk, rows);
                        roundQuoteFormVo.setAllRowQuotationTotalPrice(calcTotal(vo.getHeads(), rows));
                        return roundQuoteFormVo;
                    }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(roundQuoteFormVoList)) {
                TreeMap<Integer, BigDecimal> roundToTotalPrice = roundQuoteFormVoList.stream().collect(Collectors.toMap(
                        RoundQuoteFormVo::getRound,
                        RoundQuoteFormVo::getAllRowQuotationTotalPrice,
                        (a, b) -> a,
                        TreeMap::new
                ));
                Integer minRound = roundToTotalPrice.firstKey();
                // 计算每轮与上一轮的差值百分比
                for (RoundQuoteFormVo quoteFormVo : roundQuoteFormVoList) {
                    Integer currentRound = quoteFormVo.getRound();
                    if (Objects.equals(minRound, currentRound)) {
                        quoteFormVo.setPercentageChange("0");
                        continue;
                    }
                    BigDecimal prevTotal = getPrevTotalPrice(roundToTotalPrice, currentRound);
                    BigDecimal currentTotal = quoteFormVo.getAllRowQuotationTotalPrice();
                    if (prevTotal.equals(BigDecimal.ZERO)) {
                        quoteFormVo.setPercentageChange(BigDecimal.ZERO.toPlainString());
                    } else {
                        BigDecimal divide = currentTotal.subtract(prevTotal).divide(prevTotal, 4, RoundingMode.HALF_UP);
                        DecimalFormat decimalFormat = new DecimalFormat("#.##%");
                        quoteFormVo.setPercentageChange(decimalFormat.format(divide));
                    }
                }
            }
            v.setRoundQuoteFormList(roundQuoteFormVoList);
            return v;
        }).collect(Collectors.toList());
        vo.setSupplierQuoteFormList(quoteFormVOList);
        return vo;
    }

    private void processFileFields(List<LinkedHashMap<String, String>> rows, List<String> fileHeadList) {
        rows.forEach(row -> {
            row.keySet().stream().filter(fileHeadList::contains).forEach(r -> {
                String rv = row.get(r);
                if (StringUtils.hasText(rv)) {
                    row.put(r, null);
                }
            });
        });
    }

    private BigDecimal getPrevTotalPrice(TreeMap<Integer, BigDecimal> roundToTotalPrice, Integer currentRound) {
        if (currentRound - 1 >= 0) {
            BigDecimal res = roundToTotalPrice.get(currentRound - 1);
            if (Objects.nonNull(res)) {
                return res;
            }
            return getPrevTotalPrice(roundToTotalPrice, currentRound - 1);
        }
        return BigDecimal.ZERO;
    }

    private AnswerFileQuoteFormService getSelf() {
        return ((AnswerFileQuoteFormService) AopContext.currentProxy());
    }

    @Override
    public MultiSupplierQuoteFormVo findVo(String subpackageCode, Long supplierId, Integer round) {
        return getMultiSupplierQuoteFormVo(subpackageCode, supplierId == null ? null : Collections.singleton(supplierId), round);
    }

    private List<AnswerFileQuoteFormDao> sortRound(List<AnswerFileQuoteFormDao> list) {
        if (CollUtil.isNotEmpty(list)) {
            list.sort((o1, o2) -> {
                if (Objects.equals(o1.getRound(), o2.getRound())) {
                    return o1.getId().compareTo(o2.getId());
                }
                return o1.getRound().compareTo(o2.getRound());
            });
            return list;
        }
        return list;
    }

    @Override
    public List<LinkedHashMap<String, String>> queryByRounds(String subpackageCode, Long supplierId, Integer round) {
        AnswerFileQuoteFormVo answerFileQuoteFormVO = quoteForm(subpackageCode, supplierId, round);
        return Optional.ofNullable(answerFileQuoteFormVO.getBodyMaps()).orElse(Collections.emptyList());
    }

    @Override
    public AnswerFileQuoteFormVo quoteForm(String subpackageCode, Long supplierId, Integer round) {
        if (CharSequenceUtil.isEmpty(subpackageCode) || Objects.isNull(supplierId) || Objects.isNull(round)) {
            throw new ServiceException("request params subpackageCode and supplierId and round must not be null");
        }
        AnswerFileQuoteFormVo resultVo = new AnswerFileQuoteFormVo(subpackageCode, supplierId, round);
        MultiSupplierQuoteFormVo vo = findVo(subpackageCode, supplierId, round);
        resultVo.setPurchaseQuoteForm(new PurchaseQuoteFormVo(subpackageCode, vo.getHeads(), vo.getBodyMaps()));
        if (CollUtil.isNotEmpty(vo.getSupplierQuoteFormList())) {
            List<LinkedHashMap<String, String>> supplierQuoteFormList = vo.getSupplierQuoteFormList()
                    .stream()
                    .filter(f -> Objects.equals(f.getSupplierId(), supplierId))
                    .findAny()
                    .map(i -> i.queryBodyByRound(round))
                    .orElse(Collections.emptyList());
            resultVo.setBodyMaps(supplierQuoteFormList);
            return resultVo;
        }
        return resultVo;
    }

    // 查询字典中配置的报价表信息
    // 0-产品名 1-数量 2-单价 3-总价
    //4-费率
    @Override
    public List<String> getQuoteDictValue() {
        R<List<SysDictData>> dictValue = remoteSystemService.getDictValue(Constants.QUOTE_FIELD);
        List<SysDictData> data = dictValue.getData();
        if (dictValue.hasFail() || data == null) {
            throw new ServiceException("获取通用项目导出项目信息报价表配置失败");
        }
        List<String> res = data.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        while (res.size() < /*4*/5) {
            res.add("");
        }
        return res;
    }

    // 计算每轮总价，如果有总价，直接返回，如果没有，计算：单价*数量=总价，如果只有单价，无数量，返回单价
    @Override
    public BigDecimal calcTotal(List<AttributeVo> heads, List<LinkedHashMap<String, String>> bodyMaps) {
        BigDecimal total = BigDecimal.ZERO;
        if (CollUtil.isEmpty(bodyMaps)) {
            return total;
        }
        List<String> quoteDictValue = getQuoteDictValue();
        // 4-费率
        String feiLv = quoteDictValue.get(4);
        // 3-总价
        String quotationTotalPrice = quoteDictValue.get(3);
        // 2-单价
        String unitPrice = quoteDictValue.get(2);
        // 1-数量
        String quantity = quoteDictValue.get(1);

        boolean hasFeiLv = heads.stream().anyMatch(a -> Objects.equals(feiLv, a.getKeyVal()));
        boolean hasBidQuotation = heads.stream().anyMatch(a -> Objects.equals(quotationTotalPrice, a.getKeyVal()));
        boolean hasUnitPrice = heads.stream().anyMatch(a -> Objects.equals(unitPrice, a.getKeyVal()));
        boolean hasQuantity = heads.stream().anyMatch(a -> Objects.equals(quantity, a.getKeyVal()));
        for (LinkedHashMap<String, String> bodyMap : bodyMaps) {
            if (hasBidQuotation) {
                String bidQuotation = bodyMap.get(quotationTotalPrice);
                if (StrUtil.isNotBlank(bidQuotation) && NumberUtil.isNumber(bidQuotation)) {
                    BigDecimal bigDecimal = Convert.toBigDecimal(bidQuotation);
                    total = total.add(bigDecimal);
                }
            } else if (hasUnitPrice && hasQuantity) {
                String quantityVal = bodyMap.get(quantity);
                String unitPriceVal = bodyMap.get(unitPrice);
                if (StrUtil.isNotBlank(quantityVal) && StrUtil.isNotBlank(unitPriceVal) && NumberUtil.isNumber(quantityVal) && NumberUtil.isNumber(unitPriceVal)) {
                    BigDecimal bigDecimal = Convert.toBigDecimal(quantityVal).multiply(Convert.toBigDecimal(unitPriceVal));
                    total = total.add(bigDecimal);
                }
            } else if (hasUnitPrice) {
                String unitPriceVal = bodyMap.get(unitPrice);
                if (StrUtil.isNotBlank(unitPriceVal) && NumberUtil.isNumber(unitPriceVal)) {
                    BigDecimal bigDecimal = Convert.toBigDecimal(unitPriceVal);
                    total = total.add(bigDecimal);
                }
            } else if (hasFeiLv) {
                String feiLvVal = bodyMap.get(feiLv);
                if (StrUtil.isNotBlank(feiLvVal)) {
                    //正则匹配 百分比
                    if (feiLvVal.matches("^[0-9]+(\\.[0-9]+)?%$")) {
                        feiLvVal = feiLvVal.replace("%", "");
                        BigDecimal bigDecimal = Convert.toBigDecimal(feiLvVal);
                        total = total.add(bigDecimal);
                    }
                }
            }
        }
        return total;
    }
}
