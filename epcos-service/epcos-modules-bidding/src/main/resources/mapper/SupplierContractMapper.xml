<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.epcos.bidding.supplier.contract.repository.SupplierContractMapper">

    <resultMap id="BaseResultMap" type="com.epcos.bidding.supplier.contract.domain.dao.SupplierContractDao">
        <id column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="supplier_company_name" property="supplierCompanyName"/>
        <result column="buy_item_name" property="buyItemName"/>
        <result column="buy_item_code" property="buyItemCode"/>
        <result column="subpackage_code" property="subpackageCode"/>
        <result column="subpackage_name" property="subpackageName"/>
        <result column="submission_time" property="submissionTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="html_template" property="htmlTemplate"/>
        <result column="contract_url" property="contractUrl"/>
        <result column="attachment" property="attachment"/>
        <result property="deleted" column="deleted"/>
        <result property="createAt" column="create_at"/>
        <result property="createBy" column="create_by"/>
        <result property="updateAt" column="update_at"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <update id="updateByIdReset">
        update supplier_contract
        <set>
            attachment=null,audit_time=null,
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="auditStatus!=null">audit_status = #{auditStatus},</if>
                <if test="htmlTemplate!=null and htmlTemplate!=''">html_template=#{htmlTemplate},</if>
                <if test="contractUrl!=null and contractUrl!=''">contract_url=#{contractUrl},</if>
            </trim>
        </set>
        where id =#{id}
    </update>


</mapper>
