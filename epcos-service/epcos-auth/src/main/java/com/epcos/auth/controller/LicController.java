package com.epcos.auth.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.epcos.auth.lic.LicConfig;
import com.epcos.auth.lic.LicEnum;
import com.epcos.auth.lic.LicGenerateDto;
import com.epcos.common.security.annotation.RequiresPermissions;
import com.verhas.licensor.License;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

@Slf4j
@Profile(value = {"prod", "test", "dev"})
@Api(tags = "授权管理")
@RestController
@RequestMapping("/lic")
public class LicController {

    @Autowired
    private ResourceLoader resourceLoader;
    @Autowired
    private LicConfig licConfig;

    @ApiOperation("生成授权文件")
    @RequiresPermissions("system:license:generate")
    @PostMapping("/generate")
    public ResponseEntity<byte[]> licenseGenerate(@RequestBody @Valid LicGenerateDto dto) {
        try {
            LicEnum licEnum = LicEnum.getBySysCode(dto.getSysCode());
            if (licEnum == null) {
                return buildErrorResponse(dto.getCustomer() + ", 系统识别码未识别: " + dto.getSysCode());
            }
            Properties pros = new Properties();
            BeanUtil.copyProperties(dto, pros, "customerExpireDate");
            pros.put("customerExpireDate", dto.getCustomerExpireDate().toString());
            pros.put("sysPwd", licEnum.getSysPwd());
            pros.put("author", licConfig.getAuthor());
            pros.put("authorUrl", licConfig.getAuthorUrl());
            pros.put("authorPhone", licConfig.getAuthorPhone());
            pros.put("authorAddress", licConfig.getAuthorAddress());
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            pros.store(baos, null);
            String licPriFile = "file:lic/pri.gpg";
            Resource resource = resourceLoader.getResource(licPriFile);
            if (!resource.exists()) {
                log.error("pri.gpg文件不存在. licPriFile={}", licPriFile);
                return buildErrorResponse("pri.gpg文件不存在");
            }
            try (InputStream priInput = resource.getInputStream();
                 ByteArrayInputStream licInput = new ByteArrayInputStream(baos.toByteArray())) {
                License license = new License();
                license.setLicense(licInput);
                license.loadKey(priInput, "epc1688");
                String content = license.encodeLicense("Epc1688!.nMg#%Q");
                return ResponseEntity.ok()
                        .headers(h -> {
                            h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                            h.setContentDispositionFormData("attachment", licEnum.name() + "_" + dto.getCustomerExpireDate() + ".lic");
                        })
                        .body(content.getBytes(StandardCharsets.UTF_8));
            }
        } catch (Exception e) {
            log.error("生成授权文件失败", e);
            return buildErrorResponse("生成授权文件失败");
        }
    }

    private ResponseEntity<byte[]> buildErrorResponse(String message) {
        return ResponseEntity.ok()
                .body(JSONUtil.toJsonStr(message).getBytes(StandardCharsets.UTF_8));
    }

}
